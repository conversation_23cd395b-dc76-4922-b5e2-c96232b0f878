#!/bin/bash

# Check if the image file exists
if [ ! -f "/Users/<USER>/Downloads/revi-template-medias-test/unnamed.jpg" ]; then
    echo "Error: Image file not found at /Users/<USER>/Downloads/revi-template-medias-test/unnamed.jpg"
    exit 1
fi

# Make the curl request
curl -X POST \
  http://localhost:3001/whatsapp-cloud-api/templates/cards \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/Users/<USER>/Downloads/revi-template-medias-test/unnamed.jpg"

echo -e "\nRequest completed"
