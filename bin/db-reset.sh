#!/bin/bash
ENV_FILE=".env"

check_database_url() {
    if [ ! -f "$ENV_FILE" ]; then
        echo "⚠️  Arquivo .env não encontrado em $ENV_FILE"
        exit 1
    fi

    DATABASE_URL=$(grep "^DATABASE_URL=" "$ENV_FILE")
    if [[ "$DATABASE_URL" != *"localhost"* && "$DATABASE_URL" != *"127.0.0.1"* ]]; then
        echo "🚨 ERRO: Reset pode ser executado apenas em banco local!"
        exit 0
    fi
}

check_database_url
./node_modules/.bin/prisma migrate reset && npm run db:migrate:dev && npm run db:seed
