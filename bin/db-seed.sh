#!/bin/bash
ENV_FILE=".env"

check_database_url() {
    if [ ! -f "$ENV_FILE" ]; then
        echo "⚠️  Arquivo .env não encontrado em $ENV_FILE"
        exit 0
    fi

    DATABASE_URL=$(grep "^DATABASE_URL=" "$ENV_FILE")
    if [[ "$DATABASE_URL" != *"localhost"* && "$DATABASE_URL" != *"127.0.0.1"* ]]; then
        echo "🚨 ERRO: Seed pode ser executado apenas em banco local!"
        exit 0
    fi
}

check_database_url
ts-node prisma/seed/index.ts
