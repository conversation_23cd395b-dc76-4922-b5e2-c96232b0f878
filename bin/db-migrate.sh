#!/bin/bash
ENV_FILE=".env"

if [ $# -eq 0 ]; then
    echo "Uso: $0 --dev | --prod"
    exit 1
fi

check_database_url() {
    if [ ! -f "$ENV_FILE" ]; then
        echo "⚠️  Arquivo .env não encontrado em $ENV_FILE"
        exit
    fi

    DATABASE_URL=$(grep "^DATABASE_URL=" "$ENV_FILE")
    if [[ "$DATABASE_URL" != *"localhost"* && "$DATABASE_URL" != *"127.0.0.1"* ]]; then
        echo "🚨 ERRO: Migration em modo 'dev' só pode ser executado em um banco local!"
        exit 0
    fi
}

case "$1" in
    --dev)
        check_database_url
        ./node_modules/.bin/prisma migrate dev
        ;;
    --prod)
        echo "Executando em modo produção..."
        echo "Rodando comando PROD"
        ;;
    *)
        echo "Opção inválida: $1"
        echo "Uso: $0 --dev | --prod"
        exit 1
        ;;
esac
