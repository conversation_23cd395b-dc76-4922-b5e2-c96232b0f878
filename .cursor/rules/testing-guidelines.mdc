---
description: 
globs: *.spec.*
alwaysApply: false
---
# Unit Test Guidelines (NestJS + Jest + Prisma + jest-mock-extended)

## Context

You are working in a NestJS codebase using:

- Prisma ORM
- Jest for unit testing
- jest-mock-extended for mocking dependencies
- NestJS modules, services, and command handlers (CQRS pattern)
- Factory-based test data generation

---

## Test File Structure

### 1. Setup (`beforeEach`)

- Use `Test.createTestingModule` to initialize the system under test (either a service or command handler).
- Use `mockDeep<T>()` from `jest-mock-extended` to mock dependencies.
- Retrieve instances using `module.get()` and assign them to `DeepMockProxy<T>`.

Example:

```ts
let handler: <PERSON>Handler;
let mockServiceA: DeepMockProxy<ServiceA>;
let mockServiceB: DeepMockProxy<ServiceB>;

beforeEach(async () => {
  const module = await Test.createTestingModule({
    providers: [
      SomeHandler,
      { provide: ServiceA, useValue: mockDeep<ServiceA>() },
      { provide: ServiceB, useValue: mockDeep<ServiceB>() },
    ],
  }).compile();

  handler = module.get(SomeHandler);
  mockServiceA = module.get(ServiceA);
  mockServiceB = module.get(ServiceB);
});
```

---

## Test Case Types

### 2. Validation/Error Tests

Test all conditions where the execution should fail early or throw:

- When required data is missing or invalid
- When an entity is not found
- When business rules are violated
- When state is inconsistent

Always validate the **error message** to ensure correctness:

```ts
it('should throw an error when required entity is not found', async () => {
  mockServiceA.find.mockResolvedValue(null);

  const mockDto = DtoFactory.create();
  const mockParams = [];

  const command = new SomeCommand('mock-id', mockDto, mockParams);

  await expect(handler.execute(command)).rejects.toThrow('Entity not found');
});
```

---

### 3. Success Tests

Test the expected successful outcome for each valid input scenario.

Steps:
- Arrange: Set up DTOs, mocks, commands
- Act: Execute the method or handler
- Assert: Validate return value and service interactions

Example:

```ts
const mockDto = DtoFactory.create({ property: 'value' });
const mockParams = [];

const expectedResult = ResultFactory.create();

mockServiceA.someMethod.mockResolvedValue(expectedResult);

const command = new SomeCommand('some-id', mockDto, mockParams);
const result = await handler.execute(command);

expect(result).toEqual(expectedResult);
expect(mockServiceA.someMethod).toHaveBeenCalledWith(
  expect.objectContaining({
    field: 'value',
  }),
);
```

---

## Mock Variable Naming Convention

Use the following pattern to name mock variables:

```ts
const mockDto = DtoFactory.create();
const mockParams = [];
const mockEntity = EntityFactory.create();
```

- Prefix with `mock`
- Use factory `.create()` methods for generating data
- Reflect the purpose or data type in the variable name

---

## Skipped or Pending Tests

If a test case is expected but not yet implemented, use `it.skip` with a descriptive name and a `// TODO` comment.

Example:

```ts
it.skip('should handle case when specific condition is met', async () => {
  // TODO: Implement this test
});
```

- Keep skipped tests visible in test reports
- Use `TODO:` to clearly mark pending implementation
- Revisit and implement before completing the feature

---

## Patterns and Best Practices

- Follow `Arrange - Act - Assert` pattern in every test
  - Add comments `Arrange - Act - Assert` to delimite each section
- Mock all external dependencies
- Use factories to generate consistent, reusable test data
  - Create or use existing factories on test/factories
  - Always import the factories beginning with `'src/../test/factories/entity.factory'`
- Name test variables clearly.
  - Follow the convention: inputX, mockX, actualX, expectedX, etc.
- Use nested `describe()` blocks to separate logical test sections
- Don’t test private/internal methods directly
- Prefer meaningful and descriptive test names
- Write unit tests for each public function.
  - Use test doubles to simulate dependencies.
    - Except for third-party dependencies that are not expensive to execute.
- Write acceptance tests for each module.
  - Follow the Given-When-Then convention.
- Always validate thrown exceptions and their messages using `rejects.toThrow('message')`

---

## Naming Convention

Follow the pattern:

```
should [do something] when [some condition]
```

Examples:

- should return result when input is valid
- should throw error when validation fails
- should call service when method executes

---

## Expected Coverage Areas

| Scenario Type                | Should Be Tested |
|-----------------------------|------------------|
| Missing input or entity     | ✅               |
| Invalid state or conditions | ✅               |
| Success with valid input    | ✅               |
| Optional or edge cases      | ✅               |
| Dependency interactions     | ✅               |
| Specific error messages     | ✅               |
