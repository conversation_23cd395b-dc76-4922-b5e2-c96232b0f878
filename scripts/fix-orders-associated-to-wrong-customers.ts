import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import { PhoneNumberUtils } from '../src/shared/utils/phone-number.utils';
import { NameUtils } from '../src/shared/utils/name.utils';
import async from 'async';

const prisma = new PrismaClient();

async function main() {
  const orders: {
    order_id: string;
    wrong_customer_id: string;
    right_customer_id: string;
  }[] = await prisma.$queryRaw`
    select
      o.id as order_id,
      c.id as wrong_customer_id,
      (select c2.id from customers c2 where c2.phone_number_id = c.phone_number_id and c2.company_id = o.company_id) as right_customer_id
    from
      orders o
    join customers c on
      c.id = o.customer_id
    where
      o.company_id != c.company_id ;
  `;

  let c = 0;
  await async.eachLimit(orders, 3, async (order) => {
    console.log(`[${c}/${orders.length}] ${order.order_id}`);
    await prisma.order.update({
      where: {
        id: order.order_id,
      },
      data: {
        customer: {
          connect: {
            id: order.right_customer_id,
          },
        },
      },
    });
    c++;
  });
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
