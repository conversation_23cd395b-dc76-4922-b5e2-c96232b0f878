import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';
import { PhoneNumberUtils } from '../src/shared/utils/phone-number.utils';

const prisma = new PrismaClient();

const filePath =
  '/Users/<USER>/Downloads/meu-game-usado/filtered_clients.json';

const logFiles = {
  multipleCustomers: './logs/multiple_customers.json',
  success: './logs/success.json',
  errors: './logs/errors.json',
};

// Garantir que o diretório de logs exista
const logDirectory = path.dirname(logFiles.errors);
if (!fs.existsSync(logDirectory)) {
  fs.mkdirSync(logDirectory, { recursive: true });
}

// Função para adicionar logs ao arquivo correspondente
function appendLog(filePath: string, data: object) {
  const logs = fs.existsSync(filePath)
    ? JSON.parse(fs.readFileSync(filePath, 'utf-8'))
    : [];
  logs.push(data);
  fs.writeFileSync(filePath, JSON.stringify(logs, null, 2));
}

async function processPhoneNumberUpdates() {
  // Ler o arquivo JSON
  const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf-8')) as {
    telefone: string;
    'cliente-email': string;
  }[];

  const companyId = '2543fe7f-09c1-4cf4-80e2-e0334a21e9b6';

  for (const { telefone, 'cliente-email': email } of jsonData) {
    try {
      const customers = await prisma.customer.findMany({
        where: {
          email,
          companyId,
        },
      });

      if (customers.length === 0) {
        appendLog(logFiles.errors, {
          email,
          message: `Cliente com e-mail ${email} não encontrado na empresa ${companyId}`,
        });
        continue;
      }

      let customer = customers[0];
      let alreadyUpdated = false;

      if (customers.length > 1) {
        for (const c of customers) {
          if (!c.phoneNumberId) {
            continue;
          }
          const customerPhoneNumber = c.phoneNumberId.slice(-7);

          if (telefone.includes(customerPhoneNumber)) {
            const formattedPhoneNumber =
              PhoneNumberUtils.formatPhoneNumber(telefone);

            await prisma.customer.update({
              where: { id: c.id },
              data: { phoneNumberId: formattedPhoneNumber },
            });

            appendLog(logFiles.multipleCustomers, {
              email,
              customerPhoneNumberId: c.phoneNumberId,
              customerId: c.id,
              formattedPhoneNumber,
              telefone,
              alreadyUpdated: true,
            });

            alreadyUpdated = true;
            customer = c;
            break;
          }
        }
      }

      const formattedPhoneNumber = PhoneNumberUtils.formatPhoneNumber(telefone);

      if (!PhoneNumberUtils.isValidPhoneNumber(formattedPhoneNumber)) {
        appendLog(logFiles.errors, {
          email,
          telefone,
          customerPhoneNumberId: customer.phoneNumberId,
          message: `Número de telefone inválido após formatação: ${formattedPhoneNumber}`,
        });
        continue;
      }

      if (!alreadyUpdated) {
        await prisma.customer.update({
          where: { id: customer.id },
          data: { phoneNumberId: formattedPhoneNumber },
        });
      }

      appendLog(logFiles.success, {
        email,
        customerId: customer.id,
        customerPhoneNumberId: customer.phoneNumberId,
        telefone,
        formattedPhoneNumber,
        message: 'Número de telefone atualizado com sucesso',
      });
    } catch (error: any) {
      appendLog(logFiles.errors, {
        email,
        telefone,
        message: `Erro ao processar o cliente`,
        error: error.message,
      });
    }
  }
}

// Executar o script
processPhoneNumberUpdates()
  .catch((error) => {
    appendLog(logFiles.errors, {
      message: 'Erro ao executar o script',
      error: error.message,
    });
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
