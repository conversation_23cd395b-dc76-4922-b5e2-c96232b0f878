[{"timestamp": "2024-10-02T14:07:11.162Z", "type": "foreign", "gupshupAppName": "ForestMachineApp", "updatedCustomerId": "69281cdf-b120-4ece-91a5-79bcccc1b177", "originalPhoneNumber": "+12054874865", "oldPhoneNumber": "+5512054874865", "newPhoneNumber": "+12054874865", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:11.318Z", "type": "foreign", "gupshupAppName": "VerveApp", "updatedCustomerId": null, "originalPhoneNumber": "+84387974819", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +84387974819"}, {"timestamp": "2024-10-02T14:07:11.834Z", "type": "foreign", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": "8d3f7e0b-f154-4d91-90dc-c39897fc222c", "originalPhoneNumber": "+61450050636", "oldPhoneNumber": "+5561450050636", "newPhoneNumber": "+61450050636", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:12.003Z", "type": "foreign", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": "14373a22-4cbb-4fe0-a437-7276fdf65510", "originalPhoneNumber": "+15622120707", "oldPhoneNumber": "+5515622120707", "newPhoneNumber": "+15622120707", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:12.346Z", "type": "foreign", "gupshupAppName": "Paula<PERSON><PERSON>ber<PERSON><PERSON>", "updatedCustomerId": null, "originalPhoneNumber": "+5491136500149", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +5491136500149"}, {"timestamp": "2024-10-02T14:07:12.510Z", "type": "foreign", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": null, "originalPhoneNumber": "+5491136500149", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +5491136500149"}, {"timestamp": "2024-10-02T14:07:12.677Z", "type": "foreign", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": null, "originalPhoneNumber": "+351968816699", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351968816699"}, {"timestamp": "2024-10-02T14:07:12.838Z", "type": "foreign", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": null, "originalPhoneNumber": "+917041140290", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +917041140290"}, {"timestamp": "2024-10-02T14:07:12.991Z", "type": "foreign", "gupshupAppName": "JambuApp", "updatedCustomerId": null, "originalPhoneNumber": "+353832078929", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +353832078929"}, {"timestamp": "2024-10-02T14:07:13.183Z", "type": "foreign", "gupshupAppName": "<PERSON><PERSON><PERSON><PERSON>A<PERSON>", "updatedCustomerId": null, "originalPhoneNumber": "+5491136671358", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +5491136671358"}, {"timestamp": "2024-10-02T14:07:13.347Z", "type": "foreign", "gupshupAppName": "GestarOrganicsApp", "updatedCustomerId": null, "originalPhoneNumber": "+5491136671358", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +5491136671358"}, {"timestamp": "2024-10-02T14:07:13.549Z", "type": "foreign", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": null, "originalPhoneNumber": "+5491136671358", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +5491136671358"}, {"timestamp": "2024-10-02T14:07:13.703Z", "type": "foreign", "gupshupAppName": "RhevolutApp", "updatedCustomerId": "93e0ff8c-194e-4c63-bffb-ca122edd5da3", "originalPhoneNumber": "+12087515536", "oldPhoneNumber": "+5512087515536", "newPhoneNumber": "+12087515536", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:13.910Z", "type": "foreign", "gupshupAppName": "LiquidzApp", "updatedCustomerId": "a2b74ba6-4f91-486f-9a62-0f1ef8a51d2f", "originalPhoneNumber": "+59174964357", "oldPhoneNumber": "+5559174964357", "newPhoneNumber": "+59174964357", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:14.063Z", "type": "foreign", "gupshupAppName": "SariApp", "updatedCustomerId": null, "originalPhoneNumber": "+************", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +************"}, {"timestamp": "2024-10-02T14:07:14.315Z", "type": "foreign", "gupshupAppName": "HeavyLoveApp", "updatedCustomerId": null, "originalPhoneNumber": "+************", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +************"}, {"timestamp": "2024-10-02T14:07:14.468Z", "type": "foreign", "gupshupAppName": "BGWeb3App", "updatedCustomerId": null, "originalPhoneNumber": "+12362347789", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +12362347789"}, {"timestamp": "2024-10-02T14:07:14.662Z", "type": "foreign", "gupshupAppName": "HeavyLoveApp", "updatedCustomerId": "8af6fd9f-38fb-44d7-9736-1e673383d52e", "originalPhoneNumber": "+16507504602", "oldPhoneNumber": "+5516507504602", "newPhoneNumber": "+16507504602", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:14.957Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+351916164250", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351916164250"}, {"timestamp": "2024-10-02T14:07:15.125Z", "type": "foreign", "gupshupAppName": "OleraApp", "updatedCustomerId": "cce6a0b9-38f0-4350-924d-cb891199567f", "originalPhoneNumber": "+61407670543", "oldPhoneNumber": "+5561407670543", "newPhoneNumber": "+61407670543", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:15.299Z", "type": "foreign", "gupshupAppName": "OleraApp", "updatedCustomerId": null, "originalPhoneNumber": "+595985945301", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +595985945301"}, {"timestamp": "2024-10-02T14:07:15.516Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+447745443857", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +447745443857"}, {"timestamp": "2024-10-02T14:07:15.733Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "7b352889-7578-4ba6-a156-535eab7d2f72", "originalPhoneNumber": "+18329190370", "oldPhoneNumber": "+5518329190370", "newPhoneNumber": "+18329190370", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:15.915Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "fcdef41b-180c-4f0d-b9a1-d0fe92cc2701", "originalPhoneNumber": "+56995556341", "oldPhoneNumber": "+5556995556341", "newPhoneNumber": "+56995556341", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:16.121Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+244931058972", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244931058972"}, {"timestamp": "2024-10-02T14:07:16.298Z", "type": "foreign", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": "06cdf063-d4a7-49c0-b33e-cdbd21b0da44", "originalPhoneNumber": "+41786499590", "oldPhoneNumber": "+5541786499590", "newPhoneNumber": "+41786499590", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:16.452Z", "type": "foreign", "gupshupAppName": "NUMApp", "updatedCustomerId": null, "originalPhoneNumber": "+212772808393", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +212772808393"}, {"timestamp": "2024-10-02T14:07:16.652Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+351936297028", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351936297028"}, {"timestamp": "2024-10-02T14:07:16.846Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "884e2746-1afa-4c09-8a8f-f5ed829546ec", "originalPhoneNumber": "+2455522695", "oldPhoneNumber": "+5524955522695", "newPhoneNumber": "+2455522695", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:17.022Z", "type": "foreign", "gupshupAppName": "OleraApp", "updatedCustomerId": "97c2edff-9389-43ce-9ca8-8699bf470217", "originalPhoneNumber": "+5978935776", "oldPhoneNumber": "+5559978935776", "newPhoneNumber": "+5978935776", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:17.206Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+595971979341", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +595971979341"}, {"timestamp": "2024-10-02T14:07:17.404Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "88f5914a-166c-4d8a-8174-0d3c0e8006c8", "originalPhoneNumber": "+12014703695", "oldPhoneNumber": "+5512014703695", "newPhoneNumber": "+12014703695", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:17.569Z", "type": "foreign", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": null, "originalPhoneNumber": "+491786957803", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +491786957803"}, {"timestamp": "2024-10-02T14:07:17.723Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+351960229556", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351960229556"}, {"timestamp": "2024-10-02T14:07:17.877Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+351935978970", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351935978970"}, {"timestamp": "2024-10-02T14:07:18.029Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": "7e3cb8a0-d3c7-4288-acfd-b018bd32879d", "originalPhoneNumber": "+17273580284", "oldPhoneNumber": "+5517273580284", "newPhoneNumber": "+17273580284", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:18.234Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "b17e7e9b-2e0d-4402-a6ca-19feebd79c1c", "originalPhoneNumber": "+12244642035", "oldPhoneNumber": "+5512244642035", "newPhoneNumber": "+12244642035", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:18.422Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "1f52e3af-9940-4f55-9ff3-4deb453251cc", "originalPhoneNumber": "+50760242011", "oldPhoneNumber": "+5550760242011", "newPhoneNumber": "+50760242011", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:18.575Z", "type": "foreign", "gupshupAppName": "NUMBrainApp", "updatedCustomerId": "ef94b4c0-224d-4dda-abe2-9bf501343add", "originalPhoneNumber": "+27673876145", "oldPhoneNumber": "+5527673876145", "newPhoneNumber": "+27673876145", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:18.728Z", "type": "foreign", "gupshupAppName": "NUMBrainApp", "updatedCustomerId": null, "originalPhoneNumber": "+996998450081", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +996998450081"}, {"timestamp": "2024-10-02T14:07:18.881Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+258876955004", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258876955004"}, {"timestamp": "2024-10-02T14:07:19.065Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+351934157465", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351934157465"}, {"timestamp": "2024-10-02T14:07:19.255Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+null", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +null"}, {"timestamp": "2024-10-02T14:07:19.437Z", "type": "foreign", "gupshupAppName": "557391787351", "updatedCustomerId": null, "originalPhoneNumber": "+null", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +null"}, {"timestamp": "2024-10-02T14:07:19.600Z", "type": "foreign", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": "28d05791-a77a-424f-857a-2383f791b156", "originalPhoneNumber": "+33766496186", "oldPhoneNumber": "+5533766496186", "newPhoneNumber": "+33766496186", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:19.789Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+447711211836", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +447711211836"}, {"timestamp": "2024-10-02T14:07:19.942Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+351934574840", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351934574840"}, {"timestamp": "2024-10-02T14:07:20.140Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+2349031232225", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +2349031232225"}, {"timestamp": "2024-10-02T14:07:20.321Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244958241294", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244958241294"}, {"timestamp": "2024-10-02T14:07:20.481Z", "type": "foreign", "gupshupAppName": "OleraApp", "updatedCustomerId": "84c42ac7-9d73-4938-b742-39eb75895f91", "originalPhoneNumber": "+14384083792", "oldPhoneNumber": "+5514384083792", "newPhoneNumber": "+14384083792", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:20.678Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "92ff7cc8-d236-4d36-9a36-b5423554b864", "originalPhoneNumber": "+50588503349", "oldPhoneNumber": "+5550588503349", "newPhoneNumber": "+50588503349", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:20.833Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+258866388974", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258866388974"}, {"timestamp": "2024-10-02T14:07:20.987Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+584128481451", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +584128481451"}, {"timestamp": "2024-10-02T14:07:21.140Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244924103173", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244924103173"}, {"timestamp": "2024-10-02T14:07:21.303Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244945262816", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244945262816"}, {"timestamp": "2024-10-02T14:07:21.455Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244945369759", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244945369759"}, {"timestamp": "2024-10-02T14:07:21.609Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244947298184", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244947298184"}, {"timestamp": "2024-10-02T14:07:21.763Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+258848193363", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258848193363"}, {"timestamp": "2024-10-02T14:07:21.916Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244946238784", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244946238784"}, {"timestamp": "2024-10-02T14:07:22.070Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+258867095090", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258867095090"}, {"timestamp": "2024-10-02T14:07:22.241Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+258855023112", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258855023112"}, {"timestamp": "2024-10-02T14:07:22.406Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244921587546", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244921587546"}, {"timestamp": "2024-10-02T14:07:22.559Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+584121919999", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +584121919999"}, {"timestamp": "2024-10-02T14:07:22.712Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244921272186", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244921272186"}, {"timestamp": "2024-10-02T14:07:22.865Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244923020958", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244923020958"}, {"timestamp": "2024-10-02T14:07:23.018Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+258851541669", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258851541669"}, {"timestamp": "2024-10-02T14:07:23.172Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+258877433960", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258877433960"}, {"timestamp": "2024-10-02T14:07:23.325Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+923498427762", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +923498427762"}, {"timestamp": "2024-10-02T14:07:23.480Z", "type": "foreign", "gupshupAppName": "OleraApp", "updatedCustomerId": "dff7e8e3-b69c-4521-95f7-8f22ad6fc8bd", "originalPhoneNumber": "+18199180677", "oldPhoneNumber": "+5518199180677", "newPhoneNumber": "+18199180677", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:23.686Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+994703353633", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +994703353633"}, {"timestamp": "2024-10-02T14:07:23.840Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": "ada74117-93d0-4f0f-8f3b-fc8e68eb58b4", "originalPhoneNumber": "+14077162220", "oldPhoneNumber": "+5514077162220", "newPhoneNumber": "+14077162220", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:23.993Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": "aa722d8e-8052-48c7-8e7f-f5fb1446f262", "originalPhoneNumber": "+2389730806", "oldPhoneNumber": "+5523989730806", "newPhoneNumber": "+2389730806", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:24.169Z", "type": "foreign", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": "a3ec7822-77f9-4044-a03e-74f84bf0ccee", "originalPhoneNumber": "+13235055130", "oldPhoneNumber": "+5513235055130", "newPhoneNumber": "+13235055130", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:24.323Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244931339062", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244931339062"}, {"timestamp": "2024-10-02T14:07:24.476Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244935388676", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244935388676"}, {"timestamp": "2024-10-02T14:07:24.634Z", "type": "foreign", "gupshupAppName": "OleraApp", "updatedCustomerId": "f1f070b8-2503-4603-9bdd-7b25e0836426", "originalPhoneNumber": "+13059223648", "oldPhoneNumber": "+5513059223648", "newPhoneNumber": "+13059223648", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:24.787Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244921885487", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244921885487"}, {"timestamp": "2024-10-02T14:07:24.986Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+447710173736", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +447710173736"}, {"timestamp": "2024-10-02T14:07:25.145Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+2348061915990", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +2348061915990"}, {"timestamp": "2024-10-02T14:07:25.299Z", "type": "foreign", "gupshupAppName": "PentagonoAlphavilleApp", "updatedCustomerId": "fe6a3ef9-8fe1-41b9-a7ab-cac90e2e83ec", "originalPhoneNumber": "+50768534141", "oldPhoneNumber": "+5550768534141", "newPhoneNumber": "+50768534141", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:25.457Z", "type": "foreign", "gupshupAppName": "PentagonoCaiubiApp", "updatedCustomerId": "0892f535-a42b-43ac-94c5-a7c0d2b3d2ec", "originalPhoneNumber": "+34646892482", "oldPhoneNumber": "+5534646892482", "newPhoneNumber": "+34646892482", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:25.665Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "321b2afd-a209-4eca-970d-f54b19a72ce3", "originalPhoneNumber": "+19133001652", "oldPhoneNumber": "+5519133001652", "newPhoneNumber": "+19133001652", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:25.818Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+447943792204", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +447943792204"}, {"timestamp": "2024-10-02T14:07:26.035Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+584245372817", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +584245372817"}, {"timestamp": "2024-10-02T14:07:26.342Z", "type": "foreign", "gupshupAppName": "ItPetApp", "updatedCustomerId": null, "originalPhoneNumber": "+201221579807", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +201221579807"}, {"timestamp": "2024-10-02T14:07:26.495Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+351961027906", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351961027906"}, {"timestamp": "2024-10-02T14:07:26.649Z", "type": "foreign", "gupshupAppName": "PentagonoBartiraApp", "updatedCustomerId": null, "originalPhoneNumber": "+971502606370", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +971502606370"}, {"timestamp": "2024-10-02T14:07:26.899Z", "type": "foreign", "gupshupAppName": "PrincipessaApp", "updatedCustomerId": "8f69b63a-10ce-43a4-bd05-43ef3d92d059", "originalPhoneNumber": "+19522885790", "oldPhoneNumber": "+5519522885790", "newPhoneNumber": "+19522885790", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:27.063Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244930547626", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244930547626"}, {"timestamp": "2024-10-02T14:07:27.283Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+351910329053", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351910329053"}, {"timestamp": "2024-10-02T14:07:27.467Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "648f7eae-2ad6-4b96-a900-793b74aaa7de", "originalPhoneNumber": "+56950728581", "oldPhoneNumber": "+5556950728581", "newPhoneNumber": "+56950728581", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:27.635Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+916297140193", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +916297140193"}, {"timestamp": "2024-10-02T14:07:27.847Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+258826621310", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258826621310"}, {"timestamp": "2024-10-02T14:07:28.064Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "0e50e24e-3d6d-41cb-bfce-0c97fb84ae80", "originalPhoneNumber": "+12039176242", "oldPhoneNumber": "+5512039176242", "newPhoneNumber": "+12039176242", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:28.224Z", "type": "foreign", "gupshupAppName": "RAEventosApp", "updatedCustomerId": null, "originalPhoneNumber": "+923400234006", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +923400234006"}, {"timestamp": "2024-10-02T14:07:28.387Z", "type": "foreign", "gupshupAppName": "MvrkApp", "updatedCustomerId": null, "originalPhoneNumber": "+352661380175", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +352661380175"}, {"timestamp": "2024-10-02T14:07:28.541Z", "type": "foreign", "gupshupAppName": "MvrkApp", "updatedCustomerId": null, "originalPhoneNumber": "+447821841783", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +447821841783"}, {"timestamp": "2024-10-02T14:07:28.696Z", "type": "foreign", "gupshupAppName": "MvrkApp", "updatedCustomerId": "b7efc0be-b8c5-41d0-8b98-46552b838a2f", "originalPhoneNumber": "+19803132496", "oldPhoneNumber": "+5519803132496", "newPhoneNumber": "+19803132496", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:28.889Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "f859b70d-3d2b-49d9-91bf-ced10ce7f82e", "originalPhoneNumber": "+16823310804", "oldPhoneNumber": "+5516823310804", "newPhoneNumber": "+16823310804", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:29.052Z", "type": "foreign", "gupshupAppName": "AyaPitaya2App", "updatedCustomerId": "84871bfd-4ef6-431f-b811-aae09acfa3c9", "originalPhoneNumber": "+17372808161", "oldPhoneNumber": "+5517372808161", "newPhoneNumber": "+17372808161", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:29.272Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "fe26dca4-709f-4b8d-b8de-b92692587421", "originalPhoneNumber": "+17864524518", "oldPhoneNumber": "+5517864524518", "newPhoneNumber": "+17864524518", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:29.428Z", "type": "foreign", "gupshupAppName": "PentagonoMorumbiApp", "updatedCustomerId": "b335f87b-6c41-409e-96a2-693f75d2adf9", "originalPhoneNumber": "+33753956028", "oldPhoneNumber": "+5533753956028", "newPhoneNumber": "+33753956028", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:29.581Z", "type": "foreign", "gupshupAppName": "CuraproxApp2", "updatedCustomerId": null, "originalPhoneNumber": "+573102688021", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +573102688021"}, {"timestamp": "2024-10-02T14:07:29.735Z", "type": "foreign", "gupshupAppName": "PentagonoBartiraApp", "updatedCustomerId": null, "originalPhoneNumber": "+351910502765", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351910502765"}, {"timestamp": "2024-10-02T14:07:29.918Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+351935053850", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351935053850"}, {"timestamp": "2024-10-02T14:07:30.085Z", "type": "foreign", "gupshupAppName": "MvrkApp", "updatedCustomerId": null, "originalPhoneNumber": "+233543110545", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +233543110545"}, {"timestamp": "2024-10-02T14:07:30.257Z", "type": "foreign", "gupshupAppName": "AyaPitaya2App", "updatedCustomerId": "2ccf7e31-15bf-4e21-8948-3953f628cb35", "originalPhoneNumber": "+18328304444", "oldPhoneNumber": "+5518328304444", "newPhoneNumber": "+18328304444", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:30.421Z", "type": "foreign", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": null, "originalPhoneNumber": "+393488412686", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +393488412686"}, {"timestamp": "2024-10-02T14:07:30.577Z", "type": "foreign", "gupshupAppName": "OleraApp", "updatedCustomerId": null, "originalPhoneNumber": "+244926352980", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244926352980"}, {"timestamp": "2024-10-02T14:07:30.795Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+353834179701", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +353834179701"}, {"timestamp": "2024-10-02T14:07:30.950Z", "type": "foreign", "gupshupAppName": "MvrkApp", "updatedCustomerId": null, "originalPhoneNumber": "+2347017153561", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +2347017153561"}, {"timestamp": "2024-10-02T14:07:31.105Z", "type": "foreign", "gupshupAppName": "MvrkApp", "updatedCustomerId": "6f147d95-e1ac-42d4-ae99-1e8588058dcb", "originalPhoneNumber": "+13478823334", "oldPhoneNumber": "+5513478823334", "newPhoneNumber": "+13478823334", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:31.261Z", "type": "foreign", "gupshupAppName": "MvrkApp", "updatedCustomerId": "ff7b6b37-ab98-46ea-98b4-e74d4fff869c", "originalPhoneNumber": "+34659876651", "oldPhoneNumber": "+5534659876651", "newPhoneNumber": "+34659876651", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:31.459Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "5b073d45-5be1-4c5c-bd50-51eacb5e0d24", "originalPhoneNumber": "+4790761822", "oldPhoneNumber": "+5547990761822", "newPhoneNumber": "+4790761822", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:31.640Z", "type": "foreign", "gupshupAppName": "PentagonoMorumbiApp", "updatedCustomerId": "43d2da5b-4ede-4d30-970f-4c74c0cf92fa", "originalPhoneNumber": "+14074808495", "oldPhoneNumber": "+5514074808495", "newPhoneNumber": "+14074808495", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:31.802Z", "type": "foreign", "gupshupAppName": "AyaPitaya2App", "updatedCustomerId": "acb7faa6-bf42-42cd-9e1a-27c4f3c954d9", "originalPhoneNumber": "+19176013292", "oldPhoneNumber": "+5519176013292", "newPhoneNumber": "+19176013292", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:31.995Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+573015913549", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +573015913549"}, {"timestamp": "2024-10-02T14:07:32.213Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+593982039838", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +593982039838"}, {"timestamp": "2024-10-02T14:07:32.396Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+595994342035", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +595994342035"}, {"timestamp": "2024-10-02T14:07:32.549Z", "type": "foreign", "gupshupAppName": "554396041010", "updatedCustomerId": null, "originalPhoneNumber": "+null", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +null"}, {"timestamp": "2024-10-02T14:07:32.708Z", "type": "foreign", "gupshupAppName": "AyaPitaya2App", "updatedCustomerId": null, "originalPhoneNumber": "+8618521339289", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +8618521339289"}, {"timestamp": "2024-10-02T14:07:32.898Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+2347080180454", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +2347080180454"}, {"timestamp": "2024-10-02T14:07:33.110Z", "type": "foreign", "gupshupAppName": "UmaUmaApp", "updatedCustomerId": "fb69ec9b-074d-4cd3-ad2c-ef3fb5be097d", "originalPhoneNumber": "+16098744327", "oldPhoneNumber": "+5516098744327", "newPhoneNumber": "+16098744327", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:33.311Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+5491160442581", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +5491160442581"}, {"timestamp": "2024-10-02T14:07:33.466Z", "type": "foreign", "gupshupAppName": "OleraApp", "updatedCustomerId": null, "originalPhoneNumber": "+351915904091", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351915904091"}, {"timestamp": "2024-10-02T14:07:33.628Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+595976928357", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +595976928357"}, {"timestamp": "2024-10-02T14:07:33.817Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+353838972304", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +353838972304"}, {"timestamp": "2024-10-02T14:07:34.003Z", "type": "foreign", "gupshupAppName": "OleraApp", "updatedCustomerId": null, "originalPhoneNumber": "+2348075059615", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +2348075059615"}, {"timestamp": "2024-10-02T14:07:34.191Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+221771733929", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +221771733929"}, {"timestamp": "2024-10-02T14:07:34.345Z", "type": "foreign", "gupshupAppName": "PentagonoCaiubiApp", "updatedCustomerId": "662d6db6-cfae-4737-88d7-cf6475e1e1dd", "originalPhoneNumber": "+34685742842", "oldPhoneNumber": "+5534685742842", "newPhoneNumber": "+34685742842", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:34.540Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "be39893e-4b4e-4d84-8116-60edf226ad6e", "originalPhoneNumber": "+16417507343", "oldPhoneNumber": "+5516417507343", "newPhoneNumber": "+16417507343", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:34.718Z", "type": "foreign", "gupshupAppName": "MvrkApp", "updatedCustomerId": null, "originalPhoneNumber": "+5492616928011", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +5492616928011"}, {"timestamp": "2024-10-02T14:07:34.939Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+5493415890386", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +5493415890386"}, {"timestamp": "2024-10-02T14:07:35.092Z", "type": "foreign", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": "3a20e8f9-5dcf-4d2a-b877-3433775f72e1", "originalPhoneNumber": "+41788297766", "oldPhoneNumber": "+5541788297766", "newPhoneNumber": "+41788297766", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:35.252Z", "type": "foreign", "gupshupAppName": "OleraApp", "updatedCustomerId": "64e20ac3-8d29-4b35-84ab-79a5a141d6af", "originalPhoneNumber": "+19179129540", "oldPhoneNumber": "+5519179129540", "newPhoneNumber": "+19179129540", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:35.407Z", "type": "foreign", "gupshupAppName": "PentagonoBartiraApp", "updatedCustomerId": null, "originalPhoneNumber": "+593984395740", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +593984395740"}, {"timestamp": "2024-10-02T14:07:35.566Z", "type": "foreign", "gupshupAppName": "TrupeApp", "updatedCustomerId": "36968b13-e735-43aa-a8d7-3d4f58b067b8", "originalPhoneNumber": "+27846947631", "oldPhoneNumber": "+5527846947631", "newPhoneNumber": "+27846947631", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:35.719Z", "type": "foreign", "gupshupAppName": "PentagonoBartiraApp", "updatedCustomerId": "6dc8413d-f1e2-46fa-a14c-7d6c847a8d58", "originalPhoneNumber": "+34646892482", "oldPhoneNumber": "+5534646892482", "newPhoneNumber": "+34646892482", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:35.883Z", "type": "foreign", "gupshupAppName": "Z2FoodsB2CApp", "updatedCustomerId": "dfebda51-a516-48be-a107-12ea8fa17aa3", "originalPhoneNumber": "+17785222309", "oldPhoneNumber": "+5517785222309", "newPhoneNumber": "+17785222309", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:36.043Z", "type": "foreign", "gupshupAppName": "MoonMilkApp", "updatedCustomerId": "a8cc3f96-7eed-4e23-9708-c83116bb8176", "originalPhoneNumber": "+14756194899", "oldPhoneNumber": "+5514756194899", "newPhoneNumber": "+14756194899", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:36.216Z", "type": "foreign", "gupshupAppName": "MoonMilkApp", "updatedCustomerId": "38a630af-baca-4950-be00-74653e61a923", "originalPhoneNumber": "+17865661981", "oldPhoneNumber": "+5517865661981", "newPhoneNumber": "+17865661981", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:36.377Z", "type": "foreign", "gupshupAppName": "Z2FoodsB2CApp", "updatedCustomerId": null, "originalPhoneNumber": "+351931364183", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351931364183"}, {"timestamp": "2024-10-02T14:07:36.538Z", "type": "foreign", "gupshupAppName": "Z2FoodsB2CApp", "updatedCustomerId": "266f7961-51a0-49e2-9e5a-17c35d3eddd3", "originalPhoneNumber": "+34663565185", "oldPhoneNumber": "+5534663565185", "newPhoneNumber": "+34663565185", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:36.693Z", "type": "foreign", "gupshupAppName": "PentagonoMorumbiApp", "updatedCustomerId": null, "originalPhoneNumber": "+351915223618", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351915223618"}, {"timestamp": "2024-10-02T14:07:36.881Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+593995986616", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +593995986616"}, {"timestamp": "2024-10-02T14:07:37.101Z", "type": "foreign", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+919081212430", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +919081212430"}, {"timestamp": "2024-10-02T14:07:37.274Z", "type": "foreign", "gupshupAppName": "Z2FoodsB2CApp", "updatedCustomerId": null, "originalPhoneNumber": "+4915258967134", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +4915258967134"}, {"timestamp": "2024-10-02T14:07:37.430Z", "type": "foreign", "gupshupAppName": "MoonMilkApp", "updatedCustomerId": null, "originalPhoneNumber": "+351917866139", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351917866139"}, {"timestamp": "2024-10-02T14:07:37.586Z", "type": "foreign", "gupshupAppName": "PariocaApp", "updatedCustomerId": null, "originalPhoneNumber": "+393388623596", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +393388623596"}, {"timestamp": "2024-10-02T14:07:37.758Z", "type": "foreign", "gupshupAppName": "Z2FoodsB2CApp", "updatedCustomerId": null, "originalPhoneNumber": "+351910286820", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351910286820"}, {"timestamp": "2024-10-02T14:07:37.918Z", "type": "foreign", "gupshupAppName": "AyaPitaya2App", "updatedCustomerId": "12d92117-ba21-466f-bede-9274c351de36", "originalPhoneNumber": "+14154707118", "oldPhoneNumber": "+5514154707118", "newPhoneNumber": "+14154707118", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:38.073Z", "type": "foreign", "gupshupAppName": "MoonMilkApp", "updatedCustomerId": null, "originalPhoneNumber": "+8801614762212", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +8801614762212"}, {"timestamp": "2024-10-02T14:07:38.234Z", "type": "foreign", "gupshupAppName": "Z2FoodsB2CApp", "updatedCustomerId": "876c5585-b5ff-4f21-884b-ab4e7b92be5c", "originalPhoneNumber": "+18168769226", "oldPhoneNumber": "+5518168769226", "newPhoneNumber": "+18168769226", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:38.397Z", "type": "foreign", "gupshupAppName": "MoonMilkApp", "updatedCustomerId": "a8a8981d-60e2-4f22-b4d1-8b0c8566c388", "originalPhoneNumber": "+16463222584", "oldPhoneNumber": "+5516463222584", "newPhoneNumber": "+16463222584", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:38.587Z", "type": "foreign", "gupshupAppName": "Z2FoodsB2CApp", "updatedCustomerId": "520918a2-0a9e-4096-8c82-015349b6afcc", "originalPhoneNumber": "+51958732947", "oldPhoneNumber": "+5551958732947", "newPhoneNumber": "+51958732947", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:38.741Z", "type": "foreign", "gupshupAppName": "PentagonoCaiubiApp", "updatedCustomerId": "b3566bc5-0cf0-4111-a2c3-1c6fafb41d08", "originalPhoneNumber": "+16787934949", "oldPhoneNumber": "+5516787934949", "newPhoneNumber": "+16787934949", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:39.091Z", "type": "residential", "gupshupAppName": "BluntApp", "updatedCustomerId": "812c0df0-619e-437f-add7-74fe856ea71d", "originalPhoneNumber": "+551126121204", "oldPhoneNumber": "+5511926121204", "newPhoneNumber": "+551126121204", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:39.250Z", "type": "residential", "gupshupAppName": "BonuzApp", "updatedCustomerId": "191fadae-416c-4e74-8903-58d3e83a8c2f", "originalPhoneNumber": "+551238767757", "oldPhoneNumber": "+5512938767757", "newPhoneNumber": "+551238767757", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:39.417Z", "type": "residential", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": null, "originalPhoneNumber": "+917041140290", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +917041140290"}, {"timestamp": "2024-10-02T14:07:39.579Z", "type": "residential", "gupshupAppName": "BluntApp", "updatedCustomerId": "dff5925a-ac33-4d96-9df5-f2c588261ce4", "originalPhoneNumber": "+551144202964", "oldPhoneNumber": "+5511944202964", "newPhoneNumber": "+551144202964", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:39.737Z", "type": "residential", "gupshupAppName": "ItBrandsApp", "updatedCustomerId": null, "originalPhoneNumber": "+558140425718", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +558140425718"}, {"timestamp": "2024-10-02T14:07:39.893Z", "type": "residential", "gupshupAppName": "ItBrandsApp", "updatedCustomerId": null, "originalPhoneNumber": "+558140425715", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +558140425715"}, {"timestamp": "2024-10-02T14:07:40.053Z", "type": "residential", "gupshupAppName": "ItBrandsApp", "updatedCustomerId": null, "originalPhoneNumber": "+558140425730", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +558140425730"}, {"timestamp": "2024-10-02T14:07:40.224Z", "type": "residential", "gupshupAppName": "AhlexApp", "updatedCustomerId": "ca6dd50e-333b-47d3-bf44-24fccaf22bef", "originalPhoneNumber": "+551141985151", "oldPhoneNumber": "+5511941985151", "newPhoneNumber": "+551141985151", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:40.401Z", "type": "residential", "gupshupAppName": "ItBrandsApp", "updatedCustomerId": null, "originalPhoneNumber": "+551231970823", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +551231970823"}, {"timestamp": "2024-10-02T14:07:40.557Z", "type": "residential", "gupshupAppName": "ItBrandsApp", "updatedCustomerId": null, "originalPhoneNumber": "+551231970821", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +551231970821"}, {"timestamp": "2024-10-02T14:07:40.712Z", "type": "residential", "gupshupAppName": "JambuApp", "updatedCustomerId": null, "originalPhoneNumber": "+353832078929", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +353832078929"}, {"timestamp": "2024-10-02T14:07:40.865Z", "type": "residential", "gupshupAppName": "AreaOitoApp", "updatedCustomerId": "e28cc0e2-69b3-4a90-9c2f-a932e77b9b03", "originalPhoneNumber": "+555430558243", "oldPhoneNumber": "+5554930558243", "newPhoneNumber": "+555430558243", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:41.037Z", "type": "residential", "gupshupAppName": "AhlexApp", "updatedCustomerId": "9f79c7a5-b7bb-4690-b62c-20d90ee1021f", "originalPhoneNumber": "+554333482139", "oldPhoneNumber": "+5543933482139", "newPhoneNumber": "+554333482139", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:41.193Z", "type": "residential", "gupshupAppName": "GestarOrganicsApp", "updatedCustomerId": "ea4aeaa8-ad33-40f0-a876-3211f21ae5a7", "originalPhoneNumber": "+551150433903", "oldPhoneNumber": "+5511950433903", "newPhoneNumber": "+551150433903", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:41.507Z", "type": "residential", "gupshupAppName": "AhlexApp", "updatedCustomerId": "80a03198-3c71-493f-870a-948e57e8d55c", "originalPhoneNumber": "+554733045415", "oldPhoneNumber": "+5547933045415", "newPhoneNumber": "+554733045415", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:41.689Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "364bd54a-14be-4225-8179-90b16c56a73a", "originalPhoneNumber": "+552122400486", "oldPhoneNumber": "+5521922400486", "newPhoneNumber": "+552122400486", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:41.901Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "067e3d4f-2c7d-4930-8e7f-2791675436a0", "originalPhoneNumber": "+554333729916", "oldPhoneNumber": "+5543933729916", "newPhoneNumber": "+554333729916", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:42.122Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+447745443857", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +447745443857"}, {"timestamp": "2024-10-02T14:07:42.357Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "ed04ef64-a1bf-48d2-a1d6-de834c247d6d", "originalPhoneNumber": "+551151969150", "oldPhoneNumber": "+5511951969150", "newPhoneNumber": "+551151969150", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:42.559Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+244931058972", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244931058972"}, {"timestamp": "2024-10-02T14:07:42.765Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+351936297028", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351936297028"}, {"timestamp": "2024-10-02T14:07:42.924Z", "type": "residential", "gupshupAppName": "ZeferinoApp", "updatedCustomerId": "21103caf-473a-46e0-9eda-9864744cc77d", "originalPhoneNumber": "+556132475814", "oldPhoneNumber": "+5561932475814", "newPhoneNumber": "+556132475814", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:43.106Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "96828d8f-3877-420a-8023-05c7e78fcc5a", "originalPhoneNumber": "+553233311490", "oldPhoneNumber": "+5532933311490", "newPhoneNumber": "+553233311490", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:43.266Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+351935978970", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351935978970"}, {"timestamp": "2024-10-02T14:07:43.449Z", "type": "residential", "gupshupAppName": "ItPetApp", "updatedCustomerId": "abc7f789-8076-4fe3-94c0-e02129c2eae9", "originalPhoneNumber": "+556331422796", "oldPhoneNumber": "+5563931422796", "newPhoneNumber": "+556331422796", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:43.602Z", "type": "residential", "gupshupAppName": "BonuzApp", "updatedCustomerId": "6cfda2f8-9d28-4ec3-9a90-71041d170ecc", "originalPhoneNumber": "+554130510194", "oldPhoneNumber": "+5541930510194", "newPhoneNumber": "+554130510194", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:43.818Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "1ae64b84-1438-4641-8a3c-165a64fc03e2", "originalPhoneNumber": "+551156708789", "oldPhoneNumber": "+5511956708789", "newPhoneNumber": "+551156708789", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:43.971Z", "type": "residential", "gupshupAppName": "BonuzApp", "updatedCustomerId": "f14b4893-5b24-4af4-86ff-32171820b176", "originalPhoneNumber": "+554738040660", "oldPhoneNumber": "+5547938040660", "newPhoneNumber": "+554738040660", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:44.145Z", "type": "residential", "gupshupAppName": "PowerFocusApp", "updatedCustomerId": "e6c44943-0f03-44d7-8356-19c06a58523f", "originalPhoneNumber": "+551122161960", "oldPhoneNumber": "+5511922161960", "newPhoneNumber": "+551122161960", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:44.349Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+351934157465", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351934157465"}, {"timestamp": "2024-10-02T14:07:44.501Z", "type": "residential", "gupshupAppName": "BonuzApp", "updatedCustomerId": "cb918980-2e1b-408b-8bd4-a29ff3e8a128", "originalPhoneNumber": "+554130510190", "oldPhoneNumber": "+5541930510190", "newPhoneNumber": "+554130510190", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:44.656Z", "type": "residential", "gupshupAppName": "BonuzApp", "updatedCustomerId": "8e20c5ad-a038-48ac-bb2a-3fd5a6f25b08", "originalPhoneNumber": "+554130510192", "oldPhoneNumber": "+5541930510192", "newPhoneNumber": "+554130510192", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:44.809Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+351934574840", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351934574840"}, {"timestamp": "2024-10-02T14:07:44.981Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244958241294", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244958241294"}, {"timestamp": "2024-10-02T14:07:45.136Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+584128481451", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +584128481451"}, {"timestamp": "2024-10-02T14:07:45.292Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244924103173", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244924103173"}, {"timestamp": "2024-10-02T14:07:45.445Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244945262816", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244945262816"}, {"timestamp": "2024-10-02T14:07:45.599Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244945369759", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244945369759"}, {"timestamp": "2024-10-02T14:07:45.753Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244947298184", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244947298184"}, {"timestamp": "2024-10-02T14:07:45.907Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+258848193363", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258848193363"}, {"timestamp": "2024-10-02T14:07:46.062Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244946238784", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244946238784"}, {"timestamp": "2024-10-02T14:07:46.218Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+258855023112", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258855023112"}, {"timestamp": "2024-10-02T14:07:46.372Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244921587546", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244921587546"}, {"timestamp": "2024-10-02T14:07:46.526Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+584121919999", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +584121919999"}, {"timestamp": "2024-10-02T14:07:46.701Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244921272186", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244921272186"}, {"timestamp": "2024-10-02T14:07:46.855Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244923020958", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244923020958"}, {"timestamp": "2024-10-02T14:07:47.008Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+258851541669", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258851541669"}, {"timestamp": "2024-10-02T14:07:47.164Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244931339062", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244931339062"}, {"timestamp": "2024-10-02T14:07:47.318Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244935388676", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244935388676"}, {"timestamp": "2024-10-02T14:07:47.472Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244921885487", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244921885487"}, {"timestamp": "2024-10-02T14:07:47.627Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+447943792204", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +447943792204"}, {"timestamp": "2024-10-02T14:07:47.844Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+584245372817", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +584245372817"}, {"timestamp": "2024-10-02T14:07:47.999Z", "type": "residential", "gupshupAppName": "OleraApp", "updatedCustomerId": "5d732292-d648-4611-a34d-b4658896d283", "originalPhoneNumber": "+551128421185", "oldPhoneNumber": "+5511928421185", "newPhoneNumber": "+551128421185", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:48.162Z", "type": "residential", "gupshupAppName": "ItPetApp", "updatedCustomerId": null, "originalPhoneNumber": "+201221579807", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +201221579807"}, {"timestamp": "2024-10-02T14:07:48.352Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "fcfaf20b-4f95-4854-8b57-91961f769442", "originalPhoneNumber": "+551130913574", "oldPhoneNumber": "+5511930913574", "newPhoneNumber": "+551130913574", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:48.505Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": "85b51536-193f-47ac-85be-eaf6a9c8a7bd", "originalPhoneNumber": "+553130258231", "oldPhoneNumber": "+5531930258231", "newPhoneNumber": "+553130258231", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:48.688Z", "type": "residential", "gupshupAppName": "BgWeb3App2", "updatedCustomerId": null, "originalPhoneNumber": "+244930547626", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244930547626"}, {"timestamp": "2024-10-02T14:07:48.846Z", "type": "residential", "gupshupAppName": "AyaPitaya2App", "updatedCustomerId": "28e87e18-76fa-46f3-814c-0fd0fe0084b3", "originalPhoneNumber": "+551338284987", "oldPhoneNumber": "+5513938284987", "newPhoneNumber": "+551338284987", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:49.035Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+258826621310", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +258826621310"}, {"timestamp": "2024-10-02T14:07:49.190Z", "type": "residential", "gupshupAppName": "MvrkApp", "updatedCustomerId": null, "originalPhoneNumber": "+447821841783", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +447821841783"}, {"timestamp": "2024-10-02T14:07:49.396Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+351935053850", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351935053850"}, {"timestamp": "2024-10-02T14:07:49.587Z", "type": "residential", "gupshupAppName": "MvrkApp", "updatedCustomerId": null, "originalPhoneNumber": "+233543110545", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +233543110545"}, {"timestamp": "2024-10-02T14:07:49.742Z", "type": "residential", "gupshupAppName": "PentagonoMorumbiApp", "updatedCustomerId": "e8daac03-cc8f-4bd0-b2b9-e5fedaeff2e6", "originalPhoneNumber": "+551156421382", "oldPhoneNumber": "+5511956421382", "newPhoneNumber": "+551156421382", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:49.898Z", "type": "residential", "gupshupAppName": "PentagonoMorumbiApp", "updatedCustomerId": "811b4f75-798c-4bd8-83cc-4129193bc215", "originalPhoneNumber": "+551124126656", "oldPhoneNumber": "+5511924126656", "newPhoneNumber": "+551124126656", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:50.054Z", "type": "residential", "gupshupAppName": "PentagonoAlphavilleApp", "updatedCustomerId": "4f5c3b5f-568c-4429-a95e-6c288dce5b71", "originalPhoneNumber": "+551124126656", "oldPhoneNumber": "+5511924126656", "newPhoneNumber": "+551124126656", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:50.220Z", "type": "residential", "gupshupAppName": "PentagonoBartiraApp", "updatedCustomerId": "cca434ac-990c-4bc6-8392-aa95c3b9b516", "originalPhoneNumber": "+551124126656", "oldPhoneNumber": "+5511924126656", "newPhoneNumber": "+551124126656", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:50.385Z", "type": "residential", "gupshupAppName": "PentagonoCaiubiApp", "updatedCustomerId": "7d99f588-ddd8-47c1-bb18-4e0e063e128d", "originalPhoneNumber": "+551124126656", "oldPhoneNumber": "+5511924126656", "newPhoneNumber": "+551124126656", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:50.543Z", "type": "residential", "gupshupAppName": "AyaPitaya2App", "updatedCustomerId": "d56996d1-6c53-4749-9b78-e17226d0f86d", "originalPhoneNumber": "+551150436434", "oldPhoneNumber": "+5511950436434", "newPhoneNumber": "+551150436434", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:50.706Z", "type": "residential", "gupshupAppName": "SmarshApp", "updatedCustomerId": "2194aa0f-07da-4b7a-a256-9cc8938d6ba1", "originalPhoneNumber": "+552721423447", "oldPhoneNumber": "+5527921423447", "newPhoneNumber": "+552721423447", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:50.860Z", "type": "residential", "gupshupAppName": "SmarshApp", "updatedCustomerId": "cf9a78d6-e0bb-4761-99cd-427bb8d58316", "originalPhoneNumber": "+554330667078", "oldPhoneNumber": "+5543930667078", "newPhoneNumber": "+554330667078", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:51.029Z", "type": "residential", "gupshupAppName": "OleraApp", "updatedCustomerId": null, "originalPhoneNumber": "+244926352980", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +244926352980"}, {"timestamp": "2024-10-02T14:07:51.631Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+353834179701", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +353834179701"}, {"timestamp": "2024-10-02T14:07:51.827Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "5b3b1707-32df-4461-a0fe-c2b53e9eda11", "originalPhoneNumber": "+552121456394", "oldPhoneNumber": "+5521921456394", "newPhoneNumber": "+552121456394", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:51.983Z", "type": "residential", "gupshupAppName": "PentagonoAlphavilleApp", "updatedCustomerId": "5b65798b-730d-4722-9290-e47a5c4de1cd", "originalPhoneNumber": "+551125949000", "oldPhoneNumber": "+5511925949000", "newPhoneNumber": "+551125949000", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:52.162Z", "type": "residential", "gupshupAppName": "PentagonoMorumbiApp", "updatedCustomerId": "1c9ea283-1523-4db3-b01c-04ac2869efe6", "originalPhoneNumber": "+551125949000", "oldPhoneNumber": "+5511925949000", "newPhoneNumber": "+551125949000", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:52.317Z", "type": "residential", "gupshupAppName": "PentagonoBartiraApp", "updatedCustomerId": "4d7c436a-734d-40f9-ad3d-bde8549eb32d", "originalPhoneNumber": "+551125949000", "oldPhoneNumber": "+5511925949000", "newPhoneNumber": "+551125949000", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:52.477Z", "type": "residential", "gupshupAppName": "PentagonoCaiubiApp", "updatedCustomerId": "352c42ea-ff5b-48a9-a293-425e0cfb3999", "originalPhoneNumber": "+551125949000", "oldPhoneNumber": "+5511925949000", "newPhoneNumber": "+551125949000", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:52.631Z", "type": "residential", "gupshupAppName": "PentagonoAlphavilleApp", "updatedCustomerId": "f57ae93d-1841-416e-ba78-cd715c9a8a8d", "originalPhoneNumber": "+551126908021", "oldPhoneNumber": "+5511926908021", "newPhoneNumber": "+551126908021", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:52.797Z", "type": "residential", "gupshupAppName": "PentagonoMorumbiApp", "updatedCustomerId": "b0634815-3bb9-486c-934a-9eaabdcc109f", "originalPhoneNumber": "+551131647798", "oldPhoneNumber": "+5511931647798", "newPhoneNumber": "+551131647798", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:52.950Z", "type": "residential", "gupshupAppName": "PentagonoMorumbiApp", "updatedCustomerId": "5af0f312-a5c4-4477-ba5e-38f340059b5c", "originalPhoneNumber": "+551123612533", "oldPhoneNumber": "+5511923612533", "newPhoneNumber": "+551123612533", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:53.154Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": null, "originalPhoneNumber": "+353838972304", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +353838972304"}, {"timestamp": "2024-10-02T14:07:53.324Z", "type": "residential", "gupshupAppName": "AhlexApp", "updatedCustomerId": "12faf8c8-93a4-47c7-a743-e990b3a212fd", "originalPhoneNumber": "+551938760522", "oldPhoneNumber": "+5519938760522", "newPhoneNumber": "+551938760522", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:53.488Z", "type": "residential", "gupshupAppName": "BonuzApp", "updatedCustomerId": "ac9f827a-d443-4354-8131-fb635382e5e3", "originalPhoneNumber": "+553131773966", "oldPhoneNumber": "+5531931773966", "newPhoneNumber": "+553131773966", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:53.643Z", "type": "residential", "gupshupAppName": "MvrkApp", "updatedCustomerId": "1f51a58d-c5e6-490e-90b7-9507943f306d", "originalPhoneNumber": "+555130361510", "oldPhoneNumber": "+5551930361510", "newPhoneNumber": "+555130361510", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:53.800Z", "type": "residential", "gupshupAppName": "BadiaApp", "updatedCustomerId": "1cf82717-3d3c-488f-976a-e90fb6322f21", "originalPhoneNumber": "+556232195175", "oldPhoneNumber": "+5562932195175", "newPhoneNumber": "+556232195175", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:53.997Z", "type": "residential", "gupshupAppName": "KeyDesignApp2", "updatedCustomerId": "d455fc68-c7ba-4658-b028-0a25c8d3b011", "originalPhoneNumber": "+554330256851", "oldPhoneNumber": "+5543930256851", "newPhoneNumber": "+554330256851", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:54.152Z", "type": "residential", "gupshupAppName": "MvrkApp", "updatedCustomerId": "d154381c-f03f-4647-ba6c-cde5cab0c2d8", "originalPhoneNumber": "+551150433741", "oldPhoneNumber": "+5511950433741", "newPhoneNumber": "+551150433741", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:54.319Z", "type": "residential", "gupshupAppName": "Z2FoodsB2CApp", "updatedCustomerId": null, "originalPhoneNumber": "+351931364183", "oldPhoneNumber": null, "newPhoneNumber": null, "success": false, "errorMessage": "No customer found for phone number +351931364183"}, {"timestamp": "2024-10-02T14:07:54.473Z", "type": "residential", "gupshupAppName": "Z2foodsApp", "updatedCustomerId": "68a949e8-17c0-4180-9635-96fea1c51822", "originalPhoneNumber": "+555434438389", "oldPhoneNumber": "+5554934438389", "newPhoneNumber": "+555434438389", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:54.629Z", "type": "residential", "gupshupAppName": "MoonMilkApp", "updatedCustomerId": "d409be83-9e83-4cd4-a1f8-e7208deb501e", "originalPhoneNumber": "+551129357041", "oldPhoneNumber": "+5511929357041", "newPhoneNumber": "+551129357041", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:54.799Z", "type": "residential", "gupshupAppName": "PentagonoMorumbiApp", "updatedCustomerId": "fbbbcedb-b9bf-41df-b3f0-a443b12a4c7b", "originalPhoneNumber": "+551128722673", "oldPhoneNumber": "+5511928722673", "newPhoneNumber": "+551128722673", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:54.957Z", "type": "residential", "gupshupAppName": "PentagonoBartiraApp", "updatedCustomerId": "9bd8e515-2751-4566-bb7f-4f93e006ffff", "originalPhoneNumber": "+551128722673", "oldPhoneNumber": "+5511928722673", "newPhoneNumber": "+551128722673", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:55.116Z", "type": "residential", "gupshupAppName": "OleraApp", "updatedCustomerId": "6b550253-d92c-41a2-a1d1-49837356da52", "originalPhoneNumber": "+551144202968", "oldPhoneNumber": "+5511944202968", "newPhoneNumber": "+551144202968", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:55.271Z", "type": "residential", "gupshupAppName": "PentagonoAlphavilleApp", "updatedCustomerId": "66e7d946-0323-4183-aad1-84e3c7cb75a9", "originalPhoneNumber": "+551126736633", "oldPhoneNumber": "+5511926736633", "newPhoneNumber": "+551126736633", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:55.426Z", "type": "residential", "gupshupAppName": "PentagonoMorumbiApp", "updatedCustomerId": "f0b4790d-cf3a-41de-9f08-f42a1b63f5cc", "originalPhoneNumber": "+551153500853", "oldPhoneNumber": "+5511953500853", "newPhoneNumber": "+551153500853", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:55.582Z", "type": "residential", "gupshupAppName": "OleraApp", "updatedCustomerId": "e36a68b7-32e6-4721-a1df-f0455e6d7578", "originalPhoneNumber": "+551151982744", "oldPhoneNumber": "+5511951982744", "newPhoneNumber": "+551151982744", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:55.748Z", "type": "residential", "gupshupAppName": "BluntApp", "updatedCustomerId": "d3ab7aad-c4c0-4795-89df-fb043c556d75", "originalPhoneNumber": "+551126121227", "oldPhoneNumber": "+5511926121227", "newPhoneNumber": "+551126121227", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:55.925Z", "type": "residential", "gupshupAppName": "BluntApp", "updatedCustomerId": "6b453c43-395e-4c6a-8476-39742c2cd902", "originalPhoneNumber": "+551126121207", "oldPhoneNumber": "+5511926121207", "newPhoneNumber": "+551126121207", "success": true, "errorMessage": null}, {"timestamp": "2024-10-02T14:07:56.081Z", "type": "residential", "gupshupAppName": "MoonMilkApp", "updatedCustomerId": "e19193f5-e88f-439f-bad1-e187d8ec805b", "originalPhoneNumber": "+551932525201", "oldPhoneNumber": "+5519932525201", "newPhoneNumber": "+551932525201", "success": true, "errorMessage": null}]