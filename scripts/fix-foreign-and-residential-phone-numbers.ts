import { <PERSON>risma, PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';
import { PhoneNumberUtils } from '../src/shared/utils/phone-number.utils';
const foreignPhoneNumbersPath = path.join(
  '/Users/<USER>/code/revi/backups/mongo/foreign-phone-numbers-3.json',
);
const residentialPhoneNumbersPath = path.join(
  '/Users/<USER>/code/revi/backups/mongo/residential-phone-numbers-3.json',
);

const filePath =
  '/Users/<USER>/code/revi/apps-v2/revi-back/scripts/outputs/fix-foreign-and-residential-phones.output.json';

function createLog({
  type,
  gupshupAppName,
  originalPhoneNumber,
  updatedCustomerId,
  oldPhoneNumber,
  newPhoneNumber,
  success,
  errorMessage,
}: {
  type: 'foreign' | 'residential';
  gupshupAppName: string;
  originalPhoneNumber: string;
  updatedCustomerId: string | null;
  oldPhoneNumber: string | null;
  newPhoneNumber: string | null;
  success: boolean;
  errorMessage: string | null;
}) {
  // Read the existing JSON file
  const fileData = fs.readFileSync(filePath, 'utf8');
  const jsonData = JSON.parse(fileData);

  // Modify the JSON data
  jsonData.push({
    timestamp: new Date().toISOString(),
    type,
    gupshupAppName,
    updatedCustomerId,
    originalPhoneNumber,
    oldPhoneNumber,
    newPhoneNumber,
    success,
    errorMessage,
  });

  // Write the updated data back to the JSON file
  fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2), 'utf8');

  console.log('Phone number updated successfully!');
}

function readJsonFile<T>(filePath: string): Promise<T[]> {
  return new Promise((resolve, reject) => {
    fs.readFile(filePath, 'utf8', (err, data) => {
      if (err) {
        reject(`Error reading the file: ${err}`);
        return;
      }

      try {
        const jsonData: T[] = JSON.parse(data);
        resolve(jsonData);
      } catch (parseErr) {
        reject(`Error parsing JSON: ${parseErr}`);
      }
    });
  });
}

const prisma = new PrismaClient();

async function fixPhoneNumber(
  gupshupAppName: string,
  phoneNumber: string,
  type: 'foreign' | 'residential',
) {
  let sql;
  if (type === 'foreign') {
    sql = Prisma.sql`
      select
        c.id as customer_id,
        c.phone_number_id
      from
        customers c
      join
        companies co on c.company_id = co.id
      where
        co.gupshup_app_name = ${gupshupAppName}
        and right(c.phone_number_id, 8) = right(${phoneNumber}, 8);
    `;
  } else {
    sql = Prisma.sql`
      select
        c.id as customer_id,
        c.phone_number_id
      from
        customers c
      join
        companies co on c.company_id = co.id
      where
        co.gupshup_app_name = ${gupshupAppName}
        and right(c.phone_number_id, 8) = right(${phoneNumber}, 8)
        and left(c.phone_number_id, 5) = left(${phoneNumber}, 5);
    `;
  }

  const customers: {
    customer_id: string;
    phone_number_id: string;
  }[] = await prisma.$queryRaw(sql);

  if (customers.length === 0) {
    const errorMessage = `No customer found for phone number ${phoneNumber}`;
    console.log(errorMessage);
    createLog({
      type,
      gupshupAppName,
      updatedCustomerId: null,
      originalPhoneNumber: phoneNumber,
      oldPhoneNumber: null,
      newPhoneNumber: null,
      success: false,
      errorMessage: errorMessage,
    });
    return;
  }

  // - check if the fixed customer will duplicate customer (should be impossible)
  if (customers.length > 1) {
    const errorMessage = `Multiple customers found for phone number ${phoneNumber}`;
    console.error(errorMessage);
    createLog({
      type,
      gupshupAppName,
      updatedCustomerId: null,
      originalPhoneNumber: phoneNumber,
      oldPhoneNumber: null,
      newPhoneNumber: null,
      success: false,
      errorMessage: errorMessage,
    });
    return;
  }

  const formattedPhoneNumber = PhoneNumberUtils.formatPhoneNumber(phoneNumber);

  if (!PhoneNumberUtils.isValidPhoneNumber(formattedPhoneNumber)) {
    const errorMessage = `Invalid phone number ${phoneNumber} after formatting`;
    console.error(errorMessage);
    createLog({
      type,
      gupshupAppName,
      updatedCustomerId: null,
      originalPhoneNumber: phoneNumber,
      oldPhoneNumber: customers[0].phone_number_id,
      newPhoneNumber: formattedPhoneNumber,
      success: false,
      errorMessage: errorMessage,
    });
    return;
  }

  console.log('type: ', type);
  console.log(
    `before: ${customers[0].phone_number_id}\nafter: ${formattedPhoneNumber}`,
  );
  console.log('----------------------');

  // - fix the customer recipient_phone_number_id
  await prisma.customer.update({
    where: {
      id: customers[0].customer_id,
    },
    data: {
      phoneNumberId: formattedPhoneNumber,
    },
  });

  // - fix the conversation phone number
  const conversation = await prisma.conversation.findFirst({
    where: {
      customerId: customers[0].customer_id,
    },
  });
  if (!conversation) {
    console.error(
      `No conversation found for customer ${customers[0].customer_id}`,
    );
    return;
  }
  await prisma.conversation.update({
    where: {
      id: conversation.id,
    },
    data: {
      recipientPhoneNumberId: formattedPhoneNumber,
    },
  });
  // - fix the message recipient_phone_number_id and sender_phone_number_id
  await prisma.message.updateMany({
    where: {
      conversationId: conversation.id,
      fromSystem: true,
    },
    data: {
      recipientPhoneNumberId: formattedPhoneNumber,
    },
  });
  await prisma.message.updateMany({
    where: {
      conversationId: conversation.id,
      fromSystem: false,
    },
    data: {
      senderPhoneNumberId: formattedPhoneNumber,
    },
  });

  createLog({
    type,
    gupshupAppName,
    originalPhoneNumber: phoneNumber,
    oldPhoneNumber: customers[0].phone_number_id,
    updatedCustomerId: customers[0].customer_id,
    // updatedConversationId: conversation?.id,
    newPhoneNumber: formattedPhoneNumber,
    success: true,
    errorMessage: null,
  });
}

async function fixForeignPhoneNumbers() {
  // 1. get list of messages received from gupshup that are foreign phone numbers
  const data = await readJsonFile<{
    app: string;
    'payload.sender.country_code': number;
    'payload.sender.phone': number;
  }>(foreignPhoneNumbersPath);

  for (const item of data) {
    const gupshupAppName = item.app;
    const phoneNumber = `+${item['payload.sender.phone']}`;
    await fixPhoneNumber(gupshupAppName, phoneNumber, 'foreign');
  }
}

async function fixResidentialPhoneNumbers() {
  // 1. get list of messages received from gupshup that are brazilian residential phone numbers
  const data = await readJsonFile<{
    app: string;
    'payload.sender.phone': number;
  }>(residentialPhoneNumbersPath);

  for (const item of data) {
    const gupshupAppName = item.app;
    const phoneNumber = `+${item['payload.sender.phone']}`;
    await fixPhoneNumber(gupshupAppName, phoneNumber, 'residential');
  }
}

async function main() {
  // PSEUDOCODE:
  // fix foreign and residential phones
  // 1. get list of messages received from gupshup that are brazilian residential phone numbers
  // 2. for each customer:
  // - check if the fixed customer will duplicate customer (should be impossible)
  // - fix the conversation recipient_phone_number_id
  // - fix the message recipient_phone_number_id and sender_phone_number_id
  // - fix the customer phone number

  // TODO: group by phone to avoid duplicated call
  await fixForeignPhoneNumbers();
  await fixResidentialPhoneNumbers();

  // fix duplicated phones
  // 1. find customers that have the first 5 digits (ex: +5544) and the last 8 digits are equals (ex: 95794954)
  // 2. for each duplication, identify the right phone number (residential or mobile)
  // 3. move all orders from the wrong customer to the right customer
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
