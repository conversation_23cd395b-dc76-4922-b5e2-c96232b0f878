import { PrismaClient, Prisma } from '@prisma/client';
import * as fs from 'fs';

const prisma = new PrismaClient();

async function main() {
  const path = '/Users/<USER>/Downloads/dobro/2025-06-21/stores.json';
  const companyId = '095f4b20-074d-4c8e-acdf-27410739993f';
  const productsDataStr = await fs.promises.readFile(path, 'utf-8');
  const data: {
    store_name: string;
    order_ids: string[];
  }[] = JSON.parse(productsDataStr);

  for (const item of data) {
    console.log(
      `Processing item: ${item.store_name} - Number of orders: ${item.order_ids.length}`,
    );
    await prisma.order.updateMany({
      where: {
        companyId,
        sourceId: {
          in: item.order_ids,
        },
      },
      data: {
        storeName: item.store_name,
      },
    });
    console.log(`Finished processing item: ${item.store_name}`);
  }
  console.log('Finished processing data');
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
