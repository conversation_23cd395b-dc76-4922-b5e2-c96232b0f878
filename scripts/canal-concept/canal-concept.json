{"orders": {"value": {"fileColumn": "Valor do Pedido", "multiplier": 100, "aggregationType": "first", "decimalSeparator": ","}, "coupon": {}, "status": {"fileColumn": "Situacao - Nome"}, "sourceId": {"fileColumn": "Pedido Id"}, "storeName": {}, "salesChannel": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "Data"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Produto - Valor (R$)", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": ","}, "totalItemsQuantity": {}, "totalShippingValue": {"fileColumn": "Valor de Frete", "multiplier": 100, "aggregationType": "first", "decimalSeparator": ","}, "totalDiscountsValue": {}}, "customers": {"city": {"fileColumn": "Endereco - Cidade"}, "name": {"fileColumn": "Cliente - Nome"}, "email": {"fileColumn": "Cliente - Email"}, "state": {"fileColumn": "Endereco - UF"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Cliente - Telefone Celular"}, "phoneNumberId2": {}}}