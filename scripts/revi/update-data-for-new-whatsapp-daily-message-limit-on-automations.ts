import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

function getFrequencyFromCron(cronExpression: string): string {
  const [_minutes, _hours, dayOfMonth, month, dayOfWeek] =
    cronExpression.split(' ');

  if (dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
    return 'daily';
  }
  if (dayOfMonth === '*' && month === '*' && dayOfWeek !== '*') {
    return 'weekly';
  }
  if (dayOfMonth !== '*' && month === '*' && dayOfWeek === '*') {
    return 'monthly';
  }

  throw new Error('Invalid cron expression');
}

function getTotalOccurrencesInMonth(cronExpression: string): number {
  const [_minutes, _hours, dayOfMonth, month, daysOfWeek] =
    cronExpression.split(' ');
  const frequency = getFrequencyFromCron(cronExpression);

  if (frequency === 'daily') {
    return 31;
  }
  if (frequency === 'weekly') {
    return daysOfWeek.split(',').length * 4; // Considerando 4 semanas por mês
  }
  if (frequency === 'monthly') {
    return dayOfMonth.split(',').length;
  }

  throw new Error('Invalid cron expression');
}

async function main() {
  const automations = await prisma.automation.findMany();

  for (const automation of automations) {
    if (automation.cronExpression && automation.monthlyMessageLimitOnWhatsapp) {
      const occurrencesInMonth = getTotalOccurrencesInMonth(
        automation.cronExpression,
      );
      const newDailyLimit = Math.floor(
        automation.monthlyMessageLimitOnWhatsapp / occurrencesInMonth,
      );

      await prisma.automation.update({
        where: { id: automation.id },
        data: { dailyMessageLimitOnWhatsapp: newDailyLimit },
      });

      console.log(
        `Automation ${automation.id} - ${automation.name} updated with daily limit: ${newDailyLimit}`,
      );
    }
  }
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
