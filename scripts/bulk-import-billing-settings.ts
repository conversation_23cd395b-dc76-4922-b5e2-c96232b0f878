import { PrismaClient } from '@prisma/client';
import csv from 'csvtojson';
import { v4 as uuidv4 } from 'uuid';
import { omit } from 'lodash';

const prisma = new PrismaClient();

function parseCurrency(formatted: string) {
  const cleaned = formatted.replace('R$', '').trim();
  const value = parseFloat(cleaned);
  if (isNaN(value)) {
    console.log(`Valor numérico inválido ${formatted}`);
  }
  return Math.round(value * 100);
}

export interface ReportRow {
  ['Mes']: string;
  ['Client Name Revi']: string;
  ['Hubspot ID']: string;
  ['Gupshup App']: string;
  ['Gupshup App 1']: string;
  ['Gupshup App 2']: string;
  ['Gupshup App 3']: string;
  ['Gupshup App 4']: string;
  ['Gupshup App 5']: string;
  ['Gupshup App 6']: string;
  ['CNPJ']: string;
  ['Razao Social']: string;
  ['App Creation Date']: string;
  ['Status']: string;
  ['Marketing Quantidade']: string;
  ['Marketing Disparo Pacote']: string;
  ['Marketing Disparo Extra']: string;
  ['Utilidade Quantidade']: string;
  ['Utilidade Disparo Pacote']: string;
  ['Utilidade Disparo Extra']: string;
  ['Atendimento Quantidade']: string;
  ['Atendimento Disparo Pacote']: string;
  ['Atendimento Disparo Extra']: string;
  ['Email Quantidade']: string;
  ['Email Disparo Pacote']: string;
  ['Email Disparo Extra']: string;
  ['SMS Quantidade']: string;
  ['SMS Disparo Pacote']: string;
  ['SMS Disparo Extra']: string;
  ['Fixo Plataforma']: string;
  ['Time CS']: string;
  ['Time de Sucesso']: string;
  ['MRR']: string;
}

async function bulkImportBillingSettingsData(path: string) {
  console.log('Iniciando importação');
  const data: ReportRow[] = await csv().fromFile(path);

  const gupshupAppNames = data.map((row) => row['Gupshup App']);

  const companies = await prisma.company.findMany({
    where: {
      gupshupAppName: { in: gupshupAppNames },
    },
    select: {
      id: true,
      gupshupAppName: true,
    },
  });

  const appNameToCompanyId = new Map<string, string>();
  companies.forEach((company) => {
    if (company.gupshupAppName) {
      appNameToCompanyId.set(company.gupshupAppName, company.id);
    }
  });

  const mappedCompanyIds = new Set<string>();

  const mappedBillingSettingsData = data
    .map((row) => {
      const companyId = appNameToCompanyId.get(row['Gupshup App']);
      if (!companyId || mappedCompanyIds.has(companyId)) return null;
      if (row.Status !== '1. Ativo') return null;
      mappedCompanyIds.add(companyId);

      return {
        id: uuidv4(),
        companyId,
        customerServiceFee: parseCurrency(row['Atendimento Disparo Pacote']),
        platformFee: parseCurrency(row['Fixo Plataforma']),
        whatsappMarketingPackageLimit: Number(
          row['Marketing Quantidade'].replace(',', ''),
        ),
        whatsappUtilityPackageLimit: Number(
          row['Utilidade Quantidade'].replace(',', ''),
        ),
        whatsappServicePackageLimit: Number(
          row['Atendimento Quantidade'].replace(',', ''),
        ),
        whatsappMarketingMessageFee: parseCurrency(
          row['Marketing Disparo Extra'],
        ),
        whatsappUtilityMessageFee: parseCurrency(
          row['Utilidade Disparo Extra'],
        ),
        whatsappServiceMessageFee: parseCurrency(
          row['Atendimento Disparo Extra'],
        ),
        whatsappMarketingExtraMessageFee: parseCurrency(
          row['Marketing Disparo Extra'],
        ),
        whatsappUtilityExtraMessageFee: parseCurrency(
          row['Utilidade Disparo Extra'],
        ),
        whatsappServiceExtraMessageFee: parseCurrency(
          row['Atendimento Disparo Extra'],
        ),
        billingContactEmail: null,
        paymentMethod: undefined,
        smsMessageFee: parseCurrency(row['SMS Disparo Pacote']),
        smsPackageLimit: Number(row['SMS Quantidade']),
        smsExtraMessageFee: parseCurrency(row['SMS Disparo Extra']),
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    })
    .filter(
      (setting): setting is NonNullable<typeof setting> => setting !== null,
    );

  const companyIds = mappedBillingSettingsData.map((s) => s.companyId);

  const existingSettings = await prisma.billingSettings.findMany({
    where: {
      companyId: { in: companyIds },
    },
    select: {
      companyId: true,
    },
  });

  const existingSettingsCompanyIds = new Set(
    existingSettings.map((e) => e.companyId),
  );

  const settingsToCreate = mappedBillingSettingsData.filter(
    (s) => !existingSettingsCompanyIds.has(s.companyId),
  );
  const settingsToUpdate = mappedBillingSettingsData.filter((s) =>
    existingSettingsCompanyIds.has(s.companyId),
  );

  if (settingsToCreate.length > 0) {
    await prisma.billingSettings.createMany({
      data: settingsToCreate.map(({ id, ...rest }) => rest),
      skipDuplicates: true,
    });

    const createdCompanyIds = settingsToCreate.map((s) => s.companyId);
    const createdSettings = await prisma.billingSettings.findMany({
      where: { companyId: { in: createdCompanyIds } },
    });

    if (createdSettings.length > 0) {
      await prisma.billingSettingsHistory.createMany({
        data: createdSettings.map((setting) => ({
          ...omit(setting, ['id', 'createdAt', 'updatedAt']),
          billingSettingsId: setting.id,
        })),
        skipDuplicates: true,
      });
    }
  }

  await updateBillingSettingsWithCaseWhen(settingsToUpdate);

  if (settingsToUpdate.length > 0) {
    const updatedCompanyIds = settingsToUpdate.map((s) => s.companyId);
    const updatedSettings = await prisma.billingSettings.findMany({
      where: { companyId: { in: updatedCompanyIds } },
    });

    await prisma.billingSettingsHistory.createMany({
      data: updatedSettings.map((setting) => ({
        ...omit(setting, ['id', 'createdAt', 'updatedAt']),
        billingSettingsId: setting.id,
      })),
      skipDuplicates: true,
    });
  }

  console.log('Importação concluída.');
}

async function updateBillingSettingsWithCaseWhen(settingsToUpdate: any[]) {
  if (settingsToUpdate.length === 0) return;

  const columns = [
    'customer_service_fee',
    'platform_fee',
    'whatsapp_marketing_package_limit',
    'whatsapp_utility_package_limit',
    'whatsapp_service_package_limit',
    'whatsapp_marketing_message_cost',
    'whatsapp_utility_message_cost',
    'whatsapp_service_message_cost',
    'whatsapp_marketing_extra_message_fee',
    'whatsapp_utility_extra_message_fee',
    'whatsapp_service_extra_message_fee',
    'sms_message_cost',
    'sms_package_limit',
    'sms_extra_message_fee',
  ];

  const columnToPropertyMap: Record<string, string> = {
    customer_service_fee: 'customerServiceFee',
    platform_fee: 'platformFee',
    whatsapp_marketing_package_limit: 'whatsappMarketingPackageLimit',
    whatsapp_utility_package_limit: 'whatsappUtilityPackageLimit',
    whatsapp_service_package_limit: 'whatsappServicePackageLimit',
    whatsapp_marketing_message_cost: 'whatsappMarketingMessageFee',
    whatsapp_utility_message_cost: 'whatsappUtilityMessageFee',
    whatsapp_service_message_cost: 'whatsappServiceMessageFee',
    whatsapp_marketing_extra_message_fee: 'whatsappMarketingExtraMessageFee',
    whatsapp_utility_extra_message_fee: 'whatsappUtilityExtraMessageFee',
    whatsapp_service_extra_message_fee: 'whatsappServiceExtraMessageFee',
    sms_message_cost: 'smsMessageFee',
    sms_package_limit: 'smsPackageLimit',
    sms_extra_message_fee: 'smsExtraMessageFee',
  };

  const columnCases: Record<string, string[]> = {};
  for (const column of columns) {
    columnCases[column] = [];
  }

  const companyIds: string[] = [];

  for (const setting of settingsToUpdate) {
    companyIds.push(`'${setting.companyId}'`);
    for (const column of columns) {
      const prop = columnToPropertyMap[column];
      const value = setting[prop];
      const formattedValue =
        typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
      columnCases[column].push(
        `WHEN '${setting.companyId}' THEN ${formattedValue}`,
      );
    }
  }

  const setClauses = columns
    .map(
      (column) =>
        `"${column}" = CASE company_id\n${columnCases[column].join(
          '\n',
        )}\nELSE "${column}" END`,
    )
    .join(',\n');

  const companyIdList = companyIds.join(', ');

  const sql = `
    UPDATE billing_settings
    SET
      ${setClauses},
      "updated_at" = NOW()
    WHERE company_id IN (${companyIdList});
  `;

  await prisma.$executeRawUnsafe(sql);
}

bulkImportBillingSettingsData('./pln.csv');
