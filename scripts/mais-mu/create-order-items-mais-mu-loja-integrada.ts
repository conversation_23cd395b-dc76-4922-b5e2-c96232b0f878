import { PrismaClient, Prisma } from '@prisma/client';
import * as fs from 'fs';

const prisma = new PrismaClient();

async function main() {
  const path = '/Users/<USER>/Downloads/mais-mu/products/products.json';
  const companyId = '256fed56-3087-4258-bf0c-73fa1836b251';
  const productsDataStr = await fs.promises.readFile(path, 'utf-8');
  const data = JSON.parse(productsDataStr);

  // find all orders without order_items
  const orderIdsWithouOrderItems: {
    order_id: string;
    source_order_id: string;
  }[] = await prisma.$queryRaw`
    select
      o.id as order_id,
      o.source_id as source_order_id
    from
      orders o
    left join order_items oi on
      oi.order_id = o.id
    where
      o.company_id = ${companyId}
      and oi.id is null
  `;

  // create map of sourceOrderId to orderId
  const sourceOrderIdToOrderId = orderIdsWithouOrderItems.reduce(
    (acc, { order_id, source_order_id }) => {
      acc[source_order_id] = order_id;
      return acc;
    },
    {} as Record<string, string>,
  );

  console.log('Starting to process data');
  // create order_items data
  let i = 0;
  for (const item of data) {
    i++;
    console.log(`Processing item: ${item.product_name} - ${i}/${data.length}`);
    let product = await prisma.orderProduct.findFirst({
      where: {
        companyId,
        name: item.product_name,
      },
    });

    if (!product) {
      product = await prisma.orderProduct.create({
        data: {
          companyId,
          name: item.product_name,
        },
      });
    }

    const orderItemsData: Prisma.OrderItemCreateManyInput[] =
      item.order_items.map(
        (orderItem: { sourceOrderId: string; quantity: number }) => {
          const orderId = sourceOrderIdToOrderId[orderItem.sourceOrderId];
          if (!orderId) {
            return null;
          }

          return {
            orderId,
            productId: product!.id,
            quantity: orderItem.quantity,
          };
        },
      );

    await prisma.orderItem.createMany({
      data: orderItemsData.filter((orderItem) => orderItem !== null),
    });
    console.log(`Processed item: ${item.product_name}`);
  }
  console.log('Finished processing data');
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
