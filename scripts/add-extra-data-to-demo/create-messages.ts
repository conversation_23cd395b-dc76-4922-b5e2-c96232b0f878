import { faker } from '@faker-js/faker';
import { Prisma, PrismaClient } from '@prisma/client';

const NUM_MESSAGES_PER_CONVERSATION = 6;
const PROBABILITY_OF_READING = 0.85;

const conversationTemplates = [
  {
    niche: 'Moda feminina',
    customerMessages: [
      'Oi, esse vestido tem outros tamanhos?',
      'O tecido é leve?',
      'Vocês entregam em BH?',
    ],
    companyMessages: [
      'Olá! Temos do P ao GG 😊',
      'Sim, é bem fresquinho, ideal pro verão!',
      'Entregamos sim, inclusive com frete grátis acima de R$150.',
    ],
  },
  {
    niche: 'Petshop',
    customerMessages: [
      'O pacote de ração vem com quantos quilos?',
      'Serve para filhotes?',
      'Tem entrega no mesmo dia?',
    ],
    companyMessages: [
      'Esse tem 10kg e é super nutritivo.',
      'Sim, é indicado para filhotes até 12 meses.',
      'Se pedir até às 14h, entregamos hoje mesmo!',
    ],
  },
  {
    niche: 'Eletrônicos',
    customerMessages: [
      'Esse fone tem cancelamento de ruído?',
      'É original?',
      'Tem garantia?',
    ],
    companyMessages: [
      'Tem sim, cancelamento ativo de ruído 😉',
      'Produto 100% original, com nota fiscal.',
      'Garantia de 12 meses com o fabricante.',
    ],
  },
  {
    niche: 'Beleza e cosméticos',
    customerMessages: [
      'Esse hidratante é pra pele oleosa?',
      'Tem alguma fragrância?',
      'Pode usar de dia?',
    ],
    companyMessages: [
      'Isso, ele é oil-free e ideal pra peles oleosas!',
      'Tem um cheirinho leve de lavanda 🌿',
      'Pode sim, mas recomendamos usar com protetor solar.',
    ],
  },
  {
    niche: 'Suplementos',
    customerMessages: [
      'Esse whey é vegano?',
      'Quantas porções vêm?',
      'É adoçado com stevia?',
    ],
    companyMessages: [
      'Sim, 100% vegano 🌱',
      'Vêm 30 doses por pote.',
      'Exatamente, usamos stevia natural.',
    ],
  },
  {
    niche: 'Livros',
    customerMessages: [
      'Esse livro é edição de capa dura?',
      'É original em inglês?',
      'Tem como embalar pra presente?',
    ],
    companyMessages: [
      'Sim, capa dura e com marcador exclusivo!',
      'Essa versão é em inglês, direto da editora.',
      'Claro, embalamos com papel especial sem custo 😊',
    ],
  },
  {
    niche: 'Decoração',
    customerMessages: [
      'Esse quadro vem com moldura?',
      'Tem outras cores?',
      'Qual o prazo de entrega?',
    ],
    companyMessages: [
      'Já vem com moldura preta fosca.',
      'Temos nas cores preta, branca e madeira clara.',
      'Entrega em até 5 dias úteis!',
    ],
  },
  {
    niche: 'Games',
    customerMessages: [
      'Esse jogo roda no PS5?',
      'É mídia física ou digital?',
      'Vem com DLC?',
    ],
    companyMessages: [
      'Compatível sim com o PS5!',
      'Essa é a versão digital, enviamos o código por e-mail.',
      'Sim, inclui a DLC “Expansion Pack I”.',
    ],
  },
  {
    niche: 'Infantil',
    customerMessages: [
      'Essa mochila serve pra criança de 6 anos?',
      'É resistente à água?',
      'Vem com lancheira?',
    ],
    companyMessages: [
      'Serve sim, é tamanho escolar padrão.',
      'Sim, material impermeável!',
      'Tem opção com lancheira e estojo combinando 💼',
    ],
  },
  {
    niche: 'Casa e utilidades',
    customerMessages: [
      'Essa panela serve para indução?',
      'Qual o tamanho dela?',
      'É antiaderente?',
    ],
    companyMessages: [
      'Sim, compatível com fogões por indução.',
      'Tem 28cm de diâmetro.',
      'Antiaderente premium, sem PFOA.',
    ],
  },
];

export async function createRealisticMessagesForDemo(
  prisma: PrismaClient,
  automationId?: string,
) {
  const demoCompany = await prisma.company.findFirst({
    where: { id: 'demo' },
  });

  if (!demoCompany || !demoCompany.phoneNumberId) {
    throw new Error('Empresa demo não encontrada ou sem número de telefone.');
  }

  const conversations = await prisma.conversation.findMany({
    where: {
      companyId: demoCompany.id,
    },
    include: {
      customer: true,
    },
    take: 47,
  });

  if (conversations.length === 0) {
    throw new Error('Nenhuma conversa encontrada para a empresa demo.');
  }

  const messages: Prisma.MessageCreateManyInput[] = [];

  const users = await prisma.user.findMany({
    where: { companyId: 'demo' },
  });

  for (let i = 0; i < conversations.length; i++) {
    const conversation = conversations[i];
    const template = conversationTemplates[i % conversationTemplates.length];

    const shuffledCompanyMessages = faker.helpers.shuffle(
      template.companyMessages,
    );
    const shuffledCustomerMessages = faker.helpers.shuffle(
      template.customerMessages,
    );

    let lastDate = new Date(conversation.createdAt.getTime());

    for (let j = 0; j < NUM_MESSAGES_PER_CONVERSATION; j++) {
      const isCustomerTurn = j % 2 === 0;

      const delayMinutes = isCustomerTurn
        ? faker.number.int({ min: 1, max: 5 })
        : faker.number.int({ min: 15, max: 35 });
      const createdAt = new Date(lastDate.getTime() + delayMinutes * 60 * 1000);
      lastDate = createdAt;

      const text = isCustomerTurn
        ? shuffledCustomerMessages[
            Math.floor(j / 2) % shuffledCustomerMessages.length
          ]
        : shuffledCompanyMessages[
            Math.floor(j / 2) % shuffledCompanyMessages.length
          ];

      messages.push({
        id: faker.string.uuid(),
        text,
        senderPhoneNumberId: isCustomerTurn
          ? conversation.customer.phoneNumberId!
          : demoCompany.phoneNumberId,
        recipientPhoneNumberId: isCustomerTurn
          ? demoCompany.phoneNumberId
          : conversation.customer.phoneNumberId!,
        conversationId: conversation.id,
        fromSystem: isCustomerTurn ? false : true,
        status: Math.random() < PROBABILITY_OF_READING ? 'read' : 'sent',
        createdAt,
        updatedAt: createdAt,
        createdByUserId: users[0].id,
        automationId,
      });
    }
  }

  await prisma.message.createMany({
    data: messages,
    skipDuplicates: true,
  });

  console.log(`${messages.length} mensagens realistas criadas com sucesso.`);
}
