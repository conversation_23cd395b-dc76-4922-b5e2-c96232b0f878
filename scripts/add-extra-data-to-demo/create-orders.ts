import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma } from '@prisma/client';
import { subDays, setHours, setMinutes, setSeconds } from 'date-fns';

const ORDER_STATUSES = [
  'Pendente',
  'Pa<PERSON>',
  'Faturado',
  'Em separação',
  'Enviado',
  '<PERSON>tre<PERSON>',
  'Cancelado',
  'Devolvido',
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  'Aguardando pagamento',
  'Atrasado',
  'Em análise',
  'Autorizado',
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>ando',
  'Recusado',
  'Erro no pagamento',
  'Aguardando retirada',
  'Retirado',
  'Em transporte',
  'Extraviado',
  'Em disputa',
  'Reembolsado parcialmente',
];

const COUPONS = [
  'DESCONTO10',
  'FRETEGRATIS',
  'PROMOCAO25',
  'QUERO20',
  'PRIMEIRA10',
  'CUPOMVIP',
  'OFERTA30',
  'BLACK50',
  'BEMVINDO5',
  'AMIGOS20',
  'CLIENTEFIEL',
  'NATAL40',
  'FLASH15',
  'APP15',
  'COMBO25',
  'FESTA10',
  'EXCLUSIVO20',
  'INVERNO30',
  'VERAO10',
  'OUTONO15',
  'PRIMAVERA20',
  'LOUCURA50',
  'DESCONTOMASSA',
  'VEMCOMIGO',
  'GANHE15',
  'PROMO20',
  'SUPER30',
  'CUPOMTOP',
  'MEGA15',
  'VALE10',
];

const HOURS_WITH_WEIGHTS = [
  { hour: 6, weight: 1 },
  { hour: 7, weight: 2 },
  { hour: 8, weight: 2 },
  { hour: 9, weight: 2 },
  { hour: 10, weight: 1 },
  { hour: 11, weight: 4 },
  { hour: 12, weight: 4 },
  { hour: 13, weight: 4 },
  { hour: 14, weight: 2 },
  { hour: 15, weight: 2 },
  { hour: 16, weight: 2 },
  { hour: 17, weight: 1 },
  { hour: 18, weight: 4 },
  { hour: 19, weight: 4 },
  { hour: 20, weight: 4 },
  { hour: 21, weight: 1 },
  { hour: 22, weight: 1 },
  { hour: 23, weight: 1 },
];

function pickHourWeighted(): number {
  const totalWeight = HOURS_WITH_WEIGHTS.reduce(
    (sum, { weight }) => sum + weight,
    0,
  );
  const weightedPick = Math.random() * totalWeight;
  let acc = 0;

  for (const { hour, weight } of HOURS_WITH_WEIGHTS) {
    acc += weight;
    if (weightedPick <= acc) return hour;
  }

  return 12; // fallback (nunca deve cair aqui)
}

function generateRandomOrderDate(daysAgo: number): Date {
  const baseDate = subDays(new Date(), daysAgo);
  const hour = pickHourWeighted();
  const minute = faker.number.int({ min: 0, max: 59 });
  const second = faker.number.int({ min: 0, max: 59 });

  return setSeconds(setMinutes(setHours(baseDate, hour), minute), second);
}

function generateOrderCount(customerIndex: number): number {
  const orderGroup = customerIndex % 5;
  switch (orderGroup) {
    case 0:
      return faker.number.int({ min: 4, max: 10 });
    case 1:
      return faker.number.int({ min: 3, max: 7 });
    case 2:
      return 1;
    case 3:
      return faker.number.int({ min: 1, max: 2 });
    case 4:
      return 1;
    default:
      return 1;
  }
}

function generateDaysAgo(customerIndex: number): number {
  const dayGroup = customerIndex % 5;
  switch (dayGroup) {
    case 0:
      return faker.number.int({ min: 1, max: 60 });
    case 1:
      return faker.number.int({ min: 30, max: 365 });
    case 2:
      return faker.number.int({ min: 180, max: 720 });
    case 3:
      return faker.number.int({ min: 200, max: 600 });
    case 4:
      return faker.number.int({ min: 1, max: 30 });
    default:
      return faker.number.int({ min: 1, max: 60 });
  }
}

function generateOrderValue(customerIndex: number): number {
  const orderValueGroup = customerIndex % 5;
  // For the group with orderValueGroup === 2, generate much higher order values,
  // simulating customers who make large, infrequent purchases.
  if (orderValueGroup === 2) {
    return faker.number.int({ min: 100_000, max: 300_000 });
  }
  // For all other groups, generate smaller, more frequent order values.
  return faker.number.int({ min: 3_000, max: 60_000 });
}

export async function createOrders(prisma: PrismaClient) {
  const orders: Prisma.OrderCreateManyInput[] = [];
  const products: Prisma.OrderProductCreateManyInput[] = [];
  const orderItems: Prisma.OrderItemCreateManyInput[] = [];

  const customers = await prisma.customer.findMany({
    where: { companyId: 'demo' },
  });

  for (let i = 0; i < customers.length; i++) {
    const customer = customers[i];
    const numOrders = generateOrderCount(i);

    for (let j = 0; j < numOrders; j++) {
      const daysAgo = generateDaysAgo(i);
      const orderId = faker.string.uuid();
      const value = generateOrderValue(i);

      const totalItems = faker.number.int({ min: 1, max: 6 });
      const createdAt = generateRandomOrderDate(daysAgo);

      const useCoupon = Math.random() < 0.3;
      const coupon = useCoupon
        ? faker.helpers.arrayElement(COUPONS)
        : undefined;

      orders.push({
        id: orderId,
        companyId: 'demo',
        customerId: customer.id,
        sourceId: faker.string.uuid(),
        sourceCreatedAt: createdAt,
        sourceUpdatedAt: createdAt,
        status: faker.helpers.arrayElement(ORDER_STATUSES),
        value,
        totalItemsValue: value,
        totalDiscountsValue: faker.number.int({ min: 0, max: 5000 }),
        totalShippingValue: faker.number.int({ min: 0, max: 1500 }),
        totalTaxValue: faker.number.int({ min: 0, max: 2000 }),
        totalItemsQuantity: totalItems,
        coupon,
      });

      for (let k = 0; k < totalItems; k++) {
        const productId = faker.string.uuid();
        products.push({
          id: productId,
          companyId: 'demo',
          name: faker.commerce.productName(),
        });

        orderItems.push({
          orderId,
          productId,
          quantity: faker.number.int({ min: 1, max: 5 }),
        });
      }
    }
  }

  await prisma.order.createMany({ data: orders, skipDuplicates: true });
  await prisma.orderProduct.createMany({
    data: products,
    skipDuplicates: true,
  });
  await prisma.orderItem.createMany({ data: orderItems, skipDuplicates: true });

  console.log(
    `Created ${orders.length} orders for ${customers.length} customers.`,
  );
}
