import { faker } from '@faker-js/faker';
import { PrismaClient, PaymentMethod, Company } from '@prisma/client';

export async function createBillingSetting(
  prisma: PrismaClient,
  company: Company,
) {
  if (!company) {
    throw new Error('Company not found');
  }

  const existingBilling = await prisma.billingSettings.findUnique({
    where: { companyId: company.id },
  });

  if (existingBilling) {
    console.log('Billing settings already exist for demo company.');
    return;
  }

  await prisma.billingSettings.create({
    data: {
      companyId: company.id,
      customerServiceFee: 1000,
      platformFee: 500,
      whatsappMarketingPackageLimit: 1000,
      whatsappUtilityPackageLimit: 500,
      whatsappServicePackageLimit: 500,
      whatsappMarketingMessageFee: 80,
      whatsappUtilityMessageFee: 60,
      whatsappServiceMessageFee: 50,
      whatsappMarketingExtraMessageFee: 120,
      whatsappUtilityExtraMessageFee: 100,
      whatsappServiceExtraMessageFee: 90,
      billingContactEmail: faker.internet.email(),
      paymentMethod: PaymentMethod.boleto,
      smsMessageFee: 30,
      smsPackageLimit: 1000,
      smsExtraMessageFee: 50,
    },
  });

  console.log('Billing settings created.');
}
