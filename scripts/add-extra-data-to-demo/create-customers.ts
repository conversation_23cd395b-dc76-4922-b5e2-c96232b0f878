import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma } from '@prisma/client';

const NUM_CUSTOMERS = 8500;
const PROBABILITY_OF_OPTED_OUT = 0.05;

export async function createCustomersForDemo(prisma: PrismaClient) {
  const customers: Prisma.CustomerCreateManyInput[] = [];

  for (let i = 0; i < NUM_CUSTOMERS; i++) {
    customers.push({
      sourceId: faker.string.uuid(),
      sourceCustomerId: faker.string.uuid(),
      sourceUserId: faker.string.uuid(),
      sourceCreatedAt: faker.date.recent({ days: 90 }),
      sourceUpdatedAt: faker.date.recent({ days: 90 }),
      phoneNumberId: faker.string.uuid(),
      name: faker.person.fullName(),
      email: faker.internet.email(),
      isOptedIn: true,
      isOptedOut: Math.random() < PROBABILITY_OF_OPTED_OUT,
      isOptedInToNewsletter: Math.random() < PROBABILITY_OF_OPTED_OUT,
      companyId: 'demo',
      birthDate: faker.date.past(),
      state: faker.location.state(),
      city: faker.location.city(),
    });
  }
  return prisma.customer.createMany({
    data: customers,
    skipDuplicates: true,
  });
}
