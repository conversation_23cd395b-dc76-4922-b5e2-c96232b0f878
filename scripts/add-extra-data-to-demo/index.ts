import { PrismaClient } from '@prisma/client';
import { createCustomersForDemo } from './create-customers';
import { createOrders } from './create-orders';
import { createConversationTicketsForDemo } from './create-conversation-tickets';
import { createRealisticMessagesForDemo } from './create-messages';
import { createBillingSetting } from './create-billing-setting';
import { createConversations } from '../../prisma/seed/create-conversations';
import { createAutomationResultsForDemo } from './create-automation-results';

const prisma = new PrismaClient();

async function main() {
  const demoCompany = await prisma.company.findUnique({
    where: { id: 'demo' },
  });

  if (!demoCompany) {
    throw new Error('Demo company not found');
  }

  await createBillingSetting(prisma, demoCompany);

  await createCustomersForDemo(prisma);
  await createOrders(prisma);
  await createConversations(prisma, [demoCompany]);
  await createRealisticMessagesForDemo(prisma);
  await createConversationTicketsForDemo(prisma);

  await createAutomationResultsForDemo(prisma);
}
main();
