import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma } from '@prisma/client';
import { ArrayUtils } from '../../src/shared/utils/array.utils';
import { subDays } from 'date-fns';

const NUM_WHATSAPP_CAMPAIGNS = 40;
const PROBABILITY_OF_SCHEDULED = 0.1;

export async function createWhatsappCampaigns(prisma: PrismaClient) {
  const demoCompany = await prisma.company.findUnique({
    where: { id: 'demo' },
  });

  if (!demoCompany) {
    throw new Error('Demo company not found');
  }

  const messageTemplates = await prisma.messageTemplate.findMany({
    where: { companyId: demoCompany.id },
  });

  const templateIds = messageTemplates.map((t) => t.id);

  const whatsappCampaigns: Prisma.WhatsappCampaignCreateManyInput[] = [];

  for (let i = 0; i < NUM_WHATSAPP_CAMPAIGNS; i++) {
    const status =
      Math.random() < PROBABILITY_OF_SCHEDULED ? 'scheduled' : 'completed';

    whatsappCampaigns.push({
      createdAt: subDays(new Date(), i),
      companyId: demoCompany.id,
      templateId: ArrayUtils.randomArrayElement(templateIds),
      totalRecipients: faker.number.int({ min: 10, max: 1000 }),
      totalProcessed: faker.number.int({ min: 0, max: 1000 }),
      status,
      filterCriteria: faker.lorem.sentence(),
      templateArgs: JSON.stringify({
        firstName: faker.person.firstName(),
        discount: faker.commerce.price({ min: 5, max: 50 }),
      }),
      scheduledExecutionTime: status === 'scheduled' ? faker.date.soon() : null,
      scheduledJobId: faker.string.uuid(),
    });
  }

  return prisma.whatsappCampaign.createMany({
    data: whatsappCampaigns,
    skipDuplicates: true,
  });
}
