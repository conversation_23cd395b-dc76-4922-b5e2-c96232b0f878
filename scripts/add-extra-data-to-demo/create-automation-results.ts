import { faker } from '@faker-js/faker';
import { MessageStatus, Prisma, PrismaClient } from '@prisma/client';

const DEMO_COMPANY_ID = 'demo';
const AUTOMATION_TYPE_ID = '4utom4t3-8a3c-4a3d-85c2-1386ffc4d651';
const RESPONSE_PROBABILITY = 0.12;

function getAbandonedCartTemplateWithName(name: string) {
  return `🚨 Ei ${name}! Você esqueceu algo... 🛒
  Notamos que você deixou alguns itens no seu carrinho. Eles ainda estão te esperando! 😍

  📦 Estoque limitado
  🎁 Desconto pode acabar
  💳 Pagamento 100% seguro

  👉 Finalize sua compra agora e garanta seus produtos: [link]

  ⚡ Não perca essa chance!`;
}

export async function createAutomationResultsForDemo(prisma: PrismaClient) {
  const messageTemplate = await prisma.messageTemplate.findFirst({
    where: { ctaLink: { not: null } },
  });

  const demoCompany = await prisma.company.findUnique({
    where: { id: DEMO_COMPANY_ID },
  });

  if (!demoCompany || !demoCompany.phoneNumberId || !messageTemplate) {
    console.warn('Demo company or template not found. Aborting.');
    return;
  }

  const automation = await prisma.automation.create({
    data: {
      id: faker.string.uuid(),
      companyId: DEMO_COMPANY_ID,
      name: 'ABANDONED_CART',
      isActive: true,
      messageTemplateId: messageTemplate.id,
      automationTypeId: AUTOMATION_TYPE_ID,
    },
  });
  console.log('Automation criada');

  const customers = await prisma.customer.findMany({
    where: { companyId: DEMO_COMPANY_ID },
    take: 400,
  });

  const conversations = customers.map((customer) => {
    const date = faker.date.recent({ days: 60 });
    if (!customer.phoneNumberId) {
      return null; // Skip customers without phone numbers
    }
    return {
      id: faker.string.uuid(),
      createdAt: date,
      updatedAt: date,
      recipientPhoneNumberId: customer.phoneNumberId,
      recipientName: faker.person.fullName(),
      customerId: customer.id,
      companyId: DEMO_COMPANY_ID,
    };
  });

  await prisma.conversation.createMany({
    data: conversations.filter(Boolean) as Prisma.ConversationCreateManyInput[],
    skipDuplicates: true,
  });

  console.log('Conversas criadas');

  const savedConversations = await prisma.conversation.findMany({
    where: { companyId: DEMO_COMPANY_ID },
    take: 1500,
  });

  const messages = savedConversations.map((conversation) => ({
    createdAt: faker.date.recent({ days: 60 }),
    tempId: faker.string.uuid(),
    text: getAbandonedCartTemplateWithName(conversation.recipientName),
    senderPhoneNumberId: demoCompany.phoneNumberId,
    recipientPhoneNumberId: conversation.recipientPhoneNumberId,
    conversationId: conversation.id,
    fromSystem: true,
    status: MessageStatus.sent,
    whatsappMessageId: faker.string.uuid(),
    automationId: automation.id,
  }));

  await prisma.message.createMany({
    data: messages,
    skipDuplicates: true,
  });

  const phoneNumbersId = customers
    .map((customer) => customer.phoneNumberId)
    .filter((id): id is string => id !== null);

  const allMessages = await prisma.message.findMany({
    where: {
      recipientPhoneNumberId: { in: phoneNumbersId },
      automationId: automation.id,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  const idAndLastMessageReceivedByAutomation: Record<string, Date> = {};

  for (const message of allMessages) {
    idAndLastMessageReceivedByAutomation[message.recipientPhoneNumberId] =
      message.createdAt;
  }

  const orders = customers.map((customer) => {
    const date = faker.date.soon(
      6,
      idAndLastMessageReceivedByAutomation[customer.phoneNumberId!],
    );
    return {
      id: faker.string.uuid(),
      companyId: DEMO_COMPANY_ID,
      customerId: customer.id,
      sourceId: faker.string.uuid(),
      sourceCreatedAt: date,
      sourceUpdatedAt: date,
      value: faker.number.int({ min: 100, max: 10000 }),
      status: faker.helpers.arrayElement(['Enviado', 'Entregue']),
      totalItemsValue: faker.number.int({ min: 100, max: 5000 }),
      totalDiscountsValue: faker.number.int({ min: 0, max: 5000 }),
      totalShippingValue: faker.number.int({ min: 0, max: 1500 }),
      totalTaxValue: faker.number.int({ min: 0, max: 2000 }),
      totalItemsQuantity: faker.number.int({ min: 1, max: 50 }),
    };
  });

  await prisma.order.createMany({
    data: orders,
    skipDuplicates: true,
  });

  console.log('Pedidos criados');

  const messagesToRespond = await prisma.message.findMany({
    where: { automationId: automation.id },
    take: 200,
  });

  const responseMessages = messagesToRespond
    .filter(() => Math.random() < RESPONSE_PROBABILITY)
    .map((message) => ({
      createdAt: faker.date.between({
        from: message.createdAt,
        to: new Date(),
      }),
      tempId: faker.string.uuid(),
      text: faker.helpers.arrayElement([
        'Oi, quero saber mais sobre o produto',
        'Ainda está disponível?',
        'Qual o prazo de entrega?',
        'Pode me ajudar com um desconto?',
        'Obrigado, vou finalizar a compra.',
      ]),
      senderPhoneNumberId: message.recipientPhoneNumberId,
      recipientPhoneNumberId: demoCompany.phoneNumberId,
      conversationId: message.conversationId!,
      fromSystem: false,
      status: MessageStatus.sent,
      whatsappMessageId: faker.string.uuid(),
    }));

  await prisma.message.createMany({
    data: responseMessages,
    skipDuplicates: true,
  });

  const messagesFromAutomation = await prisma.message.findMany({
    where: { automationId: automation.id },
  });

  const shortUrlsClicks: Prisma.ShortUrlClickCreateManyInput[] = [];

  for (const message of messagesFromAutomation) {
    const shortUrlId = faker.string.uuid();

    await prisma.shortUrl.create({
      data: {
        id: shortUrlId,
        companyId: DEMO_COMPANY_ID,
        messageId: message.id,
      },
    });

    const clicks = faker.number.int({ min: 5, max: 50 });

    for (let i = 0; i < clicks; i++) {
      shortUrlsClicks.push({
        shortUrlId,
      });
    }
  }

  await prisma.shortUrlClick.createMany({
    data: shortUrlsClicks,
  });

  console.log('ShortUrls e cliques criados');
}
