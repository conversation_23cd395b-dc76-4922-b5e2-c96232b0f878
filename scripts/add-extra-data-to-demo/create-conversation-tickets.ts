import { PrismaClient, Prisma } from '@prisma/client';
import { faker } from '@faker-js/faker';
import { addMinutes, subDays } from 'date-fns';

const NUM_OPEN_TICKETS = 47;
const NUM_CLOSED_TICKETS = 4928;

export async function createConversationTicketsForDemo(prisma: PrismaClient) {
  const conversations = await prisma.conversation.findMany({
    select: { id: true, createdAt: true },
    take: NUM_OPEN_TICKETS + NUM_CLOSED_TICKETS,
    where: {
      companyId: 'demo',
      messages: {
        some: {},
      },
    },
  });

  const demoId = await prisma.user.findFirst({
    select: { id: true },
    where: { companyId: 'demo' },
  });

  const tickets: Prisma.ConversationTicketCreateManyInput[] = [];

  const openConversations = faker.helpers
    .shuffle(conversations)
    .slice(0, NUM_OPEN_TICKETS);

  for (const conv of openConversations) {
    const conversation = await prisma.conversation.findUnique({
      where: { id: conv.id },
      select: { createdAt: true },
    });

    if (!conversation) continue;

    const createdAt = conversation.createdAt;

    const firstAgentResponseAt = new Date(
      createdAt.getTime() + faker.number.int({ min: 2, max: 15 }) * 60 * 1000,
    );

    tickets.push({
      id: faker.string.uuid(),
      conversationId: conv.id,
      status: 'open',
      ownerId: demoId?.id,
      agentId: demoId?.id,
      isWaitingForUserMessage: faker.datatype.boolean(),
      isWaitingForCsatFlow: faker.datatype.boolean(),
      firstAgentResponseAt,
      createdAt: conv.createdAt,
    });
  }

  for (let i = 0; i < NUM_CLOSED_TICKETS; i++) {
    const conv = faker.helpers.arrayElement(conversations);
    const createdAt = await prisma.conversation.findUnique({
      where: { id: conv.id },
      select: { createdAt: true },
    });

    if (!createdAt) continue;

    const firstAgentResponseAt = new Date(
      createdAt.createdAt.getTime() +
        faker.number.int({ min: 3, max: 15 }) * 60 * 1000,
    );
    const finishedAt = new Date(
      firstAgentResponseAt.getTime() +
        faker.number.int({ min: 10, max: 60 }) * 60 * 1000,
    );

    tickets.push({
      id: faker.string.uuid(),
      conversationId: conv.id,
      status: 'closed',
      ownerId: demoId?.id,
      agentId: demoId?.id,
      finishedAt,
      score: faker.number.int({ min: 1, max: 5 }),
      firstAgentResponseAt,
      isWaitingForUserMessage: false,
      isWaitingForCsatFlow: false,
      createdAt: conv.createdAt,
    });
  }

  await prisma.conversationTicket.createMany({
    data: tickets,
    skipDuplicates: true,
  });

  console.log('✔ Tickets criados com sucesso.');
  await prisma.$disconnect();
}
