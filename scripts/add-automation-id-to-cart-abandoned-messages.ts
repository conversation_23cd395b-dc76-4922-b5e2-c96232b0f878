import { PrismaClient, Prisma } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const automations = await prisma.automation.findMany({
    where: {
      automationTypeId: 'c6918913-e22c-45dd-b405-697609ba81fb',
    },
  });

  let i = 0;
  for (const automation of automations) {
    i++;
    console.log(`updating automation ${i} of ${automations.length}...`);
    await prisma.message.updateMany({
      where: {
        messageTemplate: {
          type: 'ABANDONED_CART',
          companyId: automation.companyId,
        },
      },
      data: {
        automationId: automation.id,
      },
    });
    console.log(`updated automation ${i} of ${automations.length}`);
  }
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
