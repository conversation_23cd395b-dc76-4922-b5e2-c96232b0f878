-- Adicionado manualmente. Codigo perigoso
-- O comando abaixo vai alterar os flows initial_contact_flow_id para o tipo initial_contact
-- Já que agora não utilizaremos a tabela companies para guardar o initial_contact_flow_id e sim reconheceremos pelo tipo
UPDATE flows
SET type = 'initial_contact'
WHERE id IN (
  SELECT initial_contact_flow_id
  FROM companies
  WHERE initial_contact_flow_id IS NOT NULL
);