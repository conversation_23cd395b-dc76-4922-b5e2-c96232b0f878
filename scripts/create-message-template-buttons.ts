import { PrismaClient, Prisma } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const gupshupTemplates = await prisma.gupshupTemplate.findMany({
    include: {
      messageTemplate: {
        select: {
          ctaLink: true,
        },
      },
    },
  });

  for (const gupshupTemplate of gupshupTemplates) {
    const buttons = gupshupTemplate.buttons as any;

    if (buttons || gupshupTemplate.footer) {
      const messageTemplateButtonsData: Prisma.MessageTemplateButtonUncheckedCreateInput[] =
        buttons?.map((button) => ({
          text: button.text,
          type: button.type,
          url: gupshupTemplate.messageTemplate.ctaLink,
        }));

      await prisma.messageTemplate.update({
        where: {
          id: gupshupTemplate.messageTemplateId,
        },
        data: {
          footerText: gupshupTemplate.footer,
          messageTemplateButtons: {
            createMany: {
              data: messageTemplateButtonsData,
              skipDuplicates: true,
            },
          },
        },
      });
    }
  }
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
