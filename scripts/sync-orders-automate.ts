import axios from 'axios';
import readline from 'readline';
import { format, parseISO, addDays, isBefore } from 'date-fns';

//const BASE_URL = 'https://integrator.api.userevi.com';
const BASE_URL = 'http://localhost:3002';
const HEADER = {
  'x-aws-event-bridge-api-key': 'secret',
};

// Adicione módulos diante o nome que está na controller dele
// Valide apenas se o endpoint de sincronizar pedidoS existe e tem suporte a data como query param
const allModules = [
  'omie',
  'shoppub',
  'linx-commerce',
  'nuvem-shop',
  'tray',
  'cart-panda',
  'tiny',
  'vnda',
  'omny',
  'millennium',
  'ingresse',
  'magento',
  'magento2',
  'vnda',
];

function askQuestion(query: string): Promise<string> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });
  return new Promise((resolve) =>
    rl.question(query, (ans) => {
      rl.close();
      resolve(ans);
    }),
  );
}

async function syncModuleByDay(module: string, start: Date, end: Date) {
  let current = start;

  while (
    isBefore(current, end) ||
    current.toDateString() === end.toDateString()
  ) {
    // Pode ser alterado o '1' para aumentar o intervalo que ele busca por loop
    // Recomendo deixar em 1, as vezes pode ser um volume grande de pedidos e de empresas
    const nextDay = addDays(current, 1);
    const startDate = format(current, 'yyyy-MM-dd');
    const endDate = format(nextDay, 'yyyy-MM-dd');

    const url = `${BASE_URL}/${module}/sync-orders?startDate=${startDate}&endDate=${endDate}`;
    try {
      const response = await axios.post(url, {}, { headers: HEADER });
      console.log(
        `[${module}] ✅ ${startDate} sincronizado com sucesso!`,
        response.data,
      );
    } catch (error: any) {
      console.error(
        `[${module}] ❌ Erro ao sincronizar ${startDate}:`,
        error.response?.data || error.message,
      );
    }

    current = nextDay;
  }
}

async function run() {
  console.log('Selecione os módulos desejados (ex: 1,3,5):\n');
  allModules.forEach((mod, i) => {
    console.log(`${i + 1}. ${mod}`);
  });

  const selectedIndexesInput = await askQuestion(
    '\nDigite os números dos módulos: ',
  );
  const selectedIndexes = selectedIndexesInput
    .split(',')
    .map((num) => parseInt(num.trim()) - 1)
    .filter((index) => index >= 0 && index < allModules.length);

  const selectedModules = selectedIndexes.map((i) => allModules[i]);

  const startInput = await askQuestion(
    '\nDigite a data inicial (YYYY-MM-DD): ',
  );
  const endInput = await askQuestion('Digite a data final (YYYY-MM-DD): ');

  const start = parseISO(startInput);
  const end = parseISO(endInput);

  for (const module of selectedModules) {
    console.log(`\n🔄 Iniciando sincronização do módulo: ${module}`);
    await syncModuleByDay(module, start, end);
  }
}

run();
