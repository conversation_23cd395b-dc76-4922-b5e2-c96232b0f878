import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import { PhoneNumberUtils } from '../src/shared/utils/phone-number.utils';
import { NameUtils } from '../src/shared/utils/name.utils';
import async from 'async';

const prisma = new PrismaClient();

function hasAccents(str) {
  const accentRegex = /[À-ÿ]/;
  return accentRegex.test(str);
}

async function main() {
  fs.readFile(
    '/Users/<USER>/Downloads/Vendas_20231101_175906.csv',
    'latin1',
    async (err, data) => {
      if (err) {
        console.error(err);
        return;
      }
      const rows = [];
      const customers = rows.reduce((prev, curr) => {
        const name = curr['Nome do comprador'];
        const phone = curr['Telefone'];
        if (!name || !phone) {
          return prev;
        }
        const formattedPhone = PhoneNumberUtils.formatPhoneNumber(phone);
        if (!PhoneNumberUtils.isValidPhoneNumber(formattedPhone)) return prev;

        if (prev[phone]) return prev;

        if (!hasAccents(name)) return prev;

        const formattedName = NameUtils.formatName(name);

        return {
          ...prev,
          [formattedPhone]: formattedName,
        };
      }, {});
      let c = 0;
      const total = Object.keys(customers).length;
      await async.eachLimit(
        Object.entries(customers),
        3,
        async ([phone, name, email]) => {
          console.log(`[${c}/${total}] ${phone} - ${name}`);
          await prisma.customer.update({
            where: {
              phoneNumberId_email_companyId: {
                phoneNumberId: phone,
                email: email || null,
                companyId: 'abc',
              },
            },
            data: {
              name,
            },
          });
          try {
            await prisma.conversation.update({
              where: {
                recipientPhoneNumberId_companyId: {
                  companyId: 'abc',
                  recipientPhoneNumberId: phone,
                },
              },
              data: {
                recipientName: name,
              },
            });
          } catch (err: any) {
            //
          }
          c++;
        },
      );
    },
  );
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
