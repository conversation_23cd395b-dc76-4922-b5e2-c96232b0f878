import { PrismaClient } from '@prisma/client';
import csv from 'csvto<PERSON>son';

const prisma = new PrismaClient();

async function countMissingOrders(paths: string[], companyId: string) {
  let allData: any[] = [];

  for (const path of paths) {
    const data = await csv({ delimiter: ',', trim: true }).fromFile(path);
    allData = allData.concat(data);
  }

  const orderIdsByHost: Record<string, string[]> = {};

  for (const row of allData) {
    if (row.Host && row.Order) {
      if (!orderIdsByHost[row.Host]) {
        orderIdsByHost[row.Host] = [];
      }
      orderIdsByHost[row.Host].push(row.Order);
    }
  }

  const missingOrdersByHost: Record<string, number> = {};

  for (const [host, orderIds] of Object.entries(orderIdsByHost)) {
    const existingOrders = await prisma.order.findMany({
      where: {
        sourceId: { in: orderIds },
        companyId: companyId,
      },
      select: { sourceId: true },
    });

    const existingOrderIds = new Set(
      existingOrders.map((order) => order.sourceId),
    );

    const missingOrders = orderIds.filter((id) => !existingOrderIds.has(id));
    missingOrdersByHost[host] = missingOrders.length;
  }

  console.log('Missing orders per host:');
  for (const [host, count] of Object.entries(missingOrdersByHost)) {
    console.log(`${host}: ${count} missing orders`);
  }

  return missingOrdersByHost;
}

async function main() {
  const companyId = 'test';
  const paths = ['/mnt/c/Users/<USER>/Downloads/mock-data.xlsx'];

  console.log(`Processing files: ${paths.join(', ')}`);

  const missingOrdersByHost = await countMissingOrders(paths, companyId);
  console.log('Final missing orders per host:', missingOrdersByHost);
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
