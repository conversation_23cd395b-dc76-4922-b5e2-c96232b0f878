import { PrismaClient } from '@prisma/client';
import csv from 'csvto<PERSON><PERSON>';

const prisma = new PrismaClient();

async function processFile(path: string, companyId: string) {
  const data = await csv({ delimiter: ';', trim: true }).fromFile(path);

  const ordersByHost: Record<string, string[]> = {};

  for (const row of data) {
    if (!row.Host || !row.Order) continue;

    if (!ordersByHost[row.Host]) {
      ordersByHost[row.Host] = [];
    }
    ordersByHost[row.Host].push(row.Order);
  }

  for (const [host, orders] of Object.entries(ordersByHost)) {
    console.log(`Updating ${orders.length} orders for host: ${host}`);
    await prisma.order.updateMany({
      where: {
        sourceId: { in: orders },
        companyId: companyId,
      },
      data: { storeName: host },
    });
    console.log(`Successfully updated orders for host: ${host}`);
  }
}

async function main() {
  const companyId = 'ef11d0b2-3f7e-4b4c-9b22-7c8fb727139c';
  const paths = ['/mnt/c/Users/<USER>/Downloads/Report.csv'];

  for (const path of paths) {
    console.log(`Processing file: ${path}`);
    await processFile(path, companyId);
    console.log(`Completed processing file: ${path}`);
  }
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
