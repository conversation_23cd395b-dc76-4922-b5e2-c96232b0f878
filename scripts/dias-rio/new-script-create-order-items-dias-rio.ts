import { PrismaClient, Prisma } from '@prisma/client';
import * as fs from 'fs';

const prisma = new PrismaClient();

async function main() {
  const path =
    '/Users/<USER>/Downloads/dias-rio/files-to-import/products.json';
  const companyId = 'e28dd443-e5ff-4ff1-bd55-098625f919bc';
  // const companyId = 'test';
  const productsDataStr = await fs.promises.readFile(path, 'utf-8');
  const data = JSON.parse(productsDataStr);

  // Buscar todos os order_items existentes para evitar criar duplicatas
  const existingOrderItems = await prisma.orderItem.findMany({
    where: {
      order: {
        companyId,
      },
    },
    select: {
      orderId: true,
      productId: true,
    },
  });

  // Criar um Set para facilitar a busca por duplicatas
  const existingOrderItemsSet = new Set(
    existingOrderItems.map((item) => `${item.orderId}-${item.productId}`),
  );

  // Buscar todas as orders da empresa (não só as sem order_items)
  const allOrders: { order_id: string; source_order_id: string }[] =
    await prisma.$queryRaw`
      SELECT o.id AS order_id, o.source_id AS source_order_id
      FROM orders o
      WHERE o.company_id = ${companyId}
    `;

  // Criar um mapa de sourceOrderId para orderId (agora incluindo todas as orders)
  const sourceOrderIdToOrderId = allOrders.reduce(
    (acc, { order_id, source_order_id }) => {
      acc[source_order_id] = order_id;
      return acc;
    },
    {} as Record<string, string>,
  );

  console.log('Starting to process data');

  let i = 0;
  for (const item of data) {
    i++;
    console.log(`Processing item: ${item.product_name} - ${i}/${data.length}`);

    let product = await prisma.product.findFirst({
      where: {
        companyId,
        name: item.product_name,
      },
    });

    if (!product) {
      product = await prisma.product.create({
        data: {
          companyId,
          name: item.product_name,
        },
      });
    }

    const orderItemsData: Prisma.OrderItemCreateManyInput[] = item.order_items
      .map((orderItem: { sourceOrderId: string; quantity: number }) => {
        const orderId = sourceOrderIdToOrderId[orderItem.sourceOrderId];
        if (!orderId) return null;

        // Verifica se o order_item já existe antes de criar
        if (existingOrderItemsSet.has(`${orderId}-${product!.id}`)) {
          return null;
        }

        return {
          orderId,
          productId: product!.id,
          quantity: orderItem.quantity,
        };
      })
      .filter(Boolean) as Prisma.OrderItemCreateManyInput[];

    if (orderItemsData.length > 0) {
      await prisma.orderItem.createMany({
        data: orderItemsData,
        skipDuplicates: true, // Garante que, mesmo se houver concorrência, não criamos duplicatas
      });
    }

    console.log(`Processed item: ${item.product_name}`);
  }

  console.log('Finished processing data');
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
