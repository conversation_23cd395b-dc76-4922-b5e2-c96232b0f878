import csv from 'csvtojson';
import { PrismaClient } from '@prisma/client';
import { parse, setDate } from 'date-fns';

enum InvoiceItemName {
  CustomerServiceFee = 'Customer Service Fee',
  Discount = 'Discount',
  ImplementationCost = 'Implementation Cost',
  PlatformFee = 'Platform Fee',
  SmsExtra = 'SMS (Extra)',
  SmsPackage = 'SMS (Package)',
  WhatsAppMarketingExtra = 'WhatsApp Marketing (Extra)',
  WhatsAppMarketingPackage = 'WhatsApp Marketing (Package)',
  WhatsAppServiceExtra = 'WhatsApp Service (Extra)',
  WhatsAppServicePackage = 'WhatsApp Service (Package)',
  WhatsAppUtilityExtra = 'WhatsApp Utility (Extra)',
  WhatsAppUtilityPackage = 'WhatsApp Utility (Package)',
  EmailPackage = 'Email (Package)',
}

const safeParseInt = (value: string | undefined): number => {
  if (value === undefined || value === null || value.trim() === '') {
    return 0;
  }
  return parseInt(value.replace(/,/g, ''));
};

enum InvoiceItemDescription {
  CustomerServiceFee = 'Customer service fee',
  Discount = 'Discount applied to the invoice',
  ImplementationCost = 'Implementation cost',
  PlatformFee = 'Monthly platform fee',
  SmsExtra = 'SMS extra usage fee',
  SmsPackage = 'SMS included in the package',
  WhatsAppMarketingExtra = 'WhatsApp Marketing extra usage fee',
  WhatsAppMarketingPackage = 'WhatsApp Marketing included in the package',
  WhatsAppServiceExtra = 'WhatsApp Service extra usage fee',
  WhatsAppServicePackage = 'WhatsApp Service included in the package',
  WhatsAppUtilityExtra = 'WhatsApp Utility extra usage fee',
  WhatsAppUtilityPackage = 'WhatsApp Utility included in the package',
  EmailPackage = 'Email included in the package',
}

function parseMonthDate(monthStr: string): Date {
  const parsed = parse(monthStr.trim(), 'MMMM , yyyy', new Date());
  return setDate(parsed, 2);
}

function parseCurrency(formatted: string): number {
  if (!formatted) return 0;

  const raw = formatted.replace(/R\$/g, '').trim();

  let normalized = raw;

  if (raw.match(/\d+\.\d{3},\d{2}$/)) {
    normalized = raw.replace(/\./g, '').replace(',', '.');
  } else if (raw.match(/\d{1,3}(,\d{3})+\.\d{2}$/)) {
    normalized = raw.replace(/,/g, '');
  } else if (raw.includes(',')) {
    normalized = raw.replace(',', '.');
  } else {
    normalized = raw;
  }

  const value = parseFloat(normalized);

  if (isNaN(value)) {
    console.warn(`Valor inválido: "${formatted}"`);
    return 0;
  }

  return Math.round(value * 100);
}

export interface InvoiceRow {
  Mes: string;
  'Client Name Revi': string;
  'Gupshup App': string;
  'Marketing Quantidade': string;
  'Disparos Marketing': string;
  'Marketing Disparo Pacote': string;
  'Utilidade Quantidade': string;
  'Utilidade Disparo Extra': string;
  'Extra Utilidade': string;
  'Atendimento Quantidade': string;
  'Atendimento Disparo Pacote': string;
  'Atendimento Disparo Extra': string;
  'Fixo Plataforma': string;
  'SMS Quantidade': string;
  'SMS Disparo Pacote': string;
  'SMS Disparo Extra': string;
  'Time de Sucesso': string;
  'Utilidade Disparo Pacote': string;
  'Extra SMS': string;
  'Excedentes Marketing': string;
  'Extra Atendimentos': string;
  'Marketing Disparo Extra': string;
  'Email Quantidade': string;
  'Email Disparo Pacote': string;
  'Email Disparo Extra': string;
  Setup: string;
  Desconto: string;
  Total: string;
}

function calculateTotalValue(parsedData: InvoiceRow): number {
  return (
    parseCurrency(parsedData['Fixo Plataforma']) +
    parseCurrency(parsedData['Time de Sucesso']) +
    parseCurrency(parsedData.Setup) +
    parseCurrency(parsedData['SMS Disparo Extra']) *
      safeParseInt(parsedData['Extra SMS']) +
    parseCurrency(parsedData['SMS Disparo Pacote']) *
      safeParseInt(parsedData['SMS Quantidade']) +
    parseCurrency(parsedData['Marketing Disparo Extra']) *
      safeParseInt(parsedData['Excedentes Marketing']) +
    parseCurrency(parsedData['Marketing Disparo Pacote']) *
      safeParseInt(parsedData['Marketing Quantidade']) +
    parseCurrency(parsedData['Atendimento Disparo Extra']) *
      safeParseInt(parsedData['Extra Atendimentos']) +
    parseCurrency(parsedData['Atendimento Disparo Pacote']) *
      safeParseInt(parsedData['Atendimento Quantidade']) +
    parseCurrency(parsedData['Utilidade Disparo Extra']) *
      safeParseInt(parsedData['Extra Utilidade']) +
    parseCurrency(parsedData['Utilidade Disparo Pacote']) *
      safeParseInt(parsedData['Utilidade Quantidade']) +
    parseCurrency(parsedData['Email Disparo Pacote']) *
      (safeParseInt(parsedData['Email Quantidade']) / 1000) +
    parseCurrency(parsedData.Desconto)
  );
}

const prisma = new PrismaClient();

async function bulkImportInvoicesFromCsv(filePaths: string[]) {
  const companiesInvoiceData: InvoiceRow[] = [];

  for (const filePath of filePaths) {
    const rows = await csv().fromFile(filePath);
    companiesInvoiceData.push(...rows);
  }

  const companiesGupshupAppNames = companiesInvoiceData.map(
    (invoice) => invoice['Gupshup App'],
  );

  const companies = await prisma.company.findMany({
    where: { gupshupAppName: { in: companiesGupshupAppNames } },
  });

  const gupshupAppNameToCompanyId = companies.reduce((acc, company) => {
    if (!company.gupshupAppName) return acc;
    acc[company.gupshupAppName] = company.id;
    return acc;
  }, {} as Record<string, string>);

  const processedInvoices = new Set<string>();

  for (const invoice of companiesInvoiceData) {
    const companyId = gupshupAppNameToCompanyId[invoice['Gupshup App']];
    if (!companyId) continue;

    const referenceMonthDate = parseMonthDate(invoice.Mes);

    const key = `${companyId}-${referenceMonthDate}`;
    if (processedInvoices.has(key)) continue;
    processedInvoices.add(key);

    await prisma.invoice.create({
      data: {
        company: { connect: { id: companyId } },
        paymentMethod: 'boleto',
        value: calculateTotalValue(invoice),
        dueDate: parseMonthDate(invoice.Mes),
        referenceMonth: parseMonthDate(invoice.Mes),
        invoiceItems: {
          create: buildInvoiceItems(invoice),
        },
      },
    });
  }
}

interface InvoiceItem {
  name: string;
  description: string;
  unitPrice: number;
  quantity: number;
  discount: number;
  totalPrice: number;
}

function buildInvoiceItems(invoice: InvoiceRow) {
  const items: InvoiceItem[] = [];

  function tryPush(item: InvoiceItem) {
    if (
      item.totalPrice > 0 ||
      (item.name === InvoiceItemName.Discount && item.discount > 0)
    )
      items.push(item);
  }

  if (invoice['Time de Sucesso']) {
    tryPush({
      name: InvoiceItemName.CustomerServiceFee,
      description: InvoiceItemDescription.CustomerServiceFee,
      unitPrice: parseCurrency(invoice['Time de Sucesso']),
      quantity: 1,
      discount: 0,
      totalPrice: parseCurrency(invoice['Time de Sucesso']),
    });
  }

  if (invoice['Desconto']) {
    tryPush({
      name: InvoiceItemName.Discount,
      description: InvoiceItemDescription.Discount,
      unitPrice: 0,
      quantity: 1,
      discount: parseCurrency(invoice['Desconto']),
      totalPrice: -parseCurrency(invoice['Desconto']),
    });
  }

  if (invoice['Setup']) {
    const unitPrice = parseCurrency(invoice['Setup']);
    tryPush({
      name: InvoiceItemName.ImplementationCost,
      description: InvoiceItemDescription.ImplementationCost,
      unitPrice,
      quantity: 1,
      discount: 0,
      totalPrice: unitPrice,
    });
  }

  if (invoice['Fixo Plataforma']) {
    const unitPrice = parseCurrency(invoice['Fixo Plataforma']);
    tryPush({
      name: InvoiceItemName.PlatformFee,
      description: InvoiceItemDescription.PlatformFee,
      unitPrice,
      quantity: 1,
      discount: 0,
      totalPrice: unitPrice,
    });
  }

  if (invoice['SMS Disparo Extra'] && invoice['Extra SMS']) {
    const unitPrice = parseCurrency(invoice['SMS Disparo Extra']);
    const quantity = safeParseInt(invoice['Extra SMS'] || '0');
    tryPush({
      name: InvoiceItemName.SmsExtra,
      description: InvoiceItemDescription.SmsExtra,
      unitPrice,
      quantity,
      discount: 0,
      totalPrice: unitPrice * quantity,
    });
  }

  if (invoice['Email Disparo Pacote'] && invoice['Email Quantidade']) {
    const unitPrice = parseCurrency(invoice['Email Disparo Pacote']);
    const quantity = safeParseInt(invoice['Email Quantidade']);
    tryPush({
      name: InvoiceItemName.EmailPackage,
      description: InvoiceItemDescription.EmailPackage,
      unitPrice,
      quantity,
      discount: 0,
      totalPrice: unitPrice * (quantity / 1000),
    });
  }

  if (invoice['SMS Disparo Pacote'] && invoice['SMS Quantidade']) {
    const unitPrice = parseCurrency(invoice['SMS Disparo Pacote']);
    const quantity = safeParseInt(invoice['SMS Quantidade']);
    tryPush({
      name: InvoiceItemName.SmsPackage,
      description: InvoiceItemDescription.SmsPackage,
      unitPrice,
      quantity,
      discount: 0,
      totalPrice: unitPrice * quantity,
    });
  }

  if (invoice['Marketing Disparo Extra'] && invoice['Excedentes Marketing']) {
    const unitPrice = parseCurrency(invoice['Marketing Disparo Extra']);
    const quantity = safeParseInt(invoice['Excedentes Marketing']);
    tryPush({
      name: InvoiceItemName.WhatsAppMarketingExtra,
      description: InvoiceItemDescription.WhatsAppMarketingExtra,
      unitPrice,
      quantity,
      discount: 0,
      totalPrice: unitPrice * quantity,
    });
  }

  if (invoice['Marketing Disparo Pacote'] && invoice['Marketing Quantidade']) {
    const unitPrice = parseCurrency(invoice['Marketing Disparo Pacote']);
    const quantity = safeParseInt(invoice['Marketing Quantidade']);
    tryPush({
      name: InvoiceItemName.WhatsAppMarketingPackage,
      description: InvoiceItemDescription.WhatsAppMarketingPackage,
      unitPrice,
      quantity,
      discount: 0,
      totalPrice: unitPrice * quantity,
    });
  }

  if (invoice['Atendimento Disparo Extra'] && invoice['Extra Atendimentos']) {
    const unitPrice = parseCurrency(invoice['Atendimento Disparo Extra']);
    const quantity = safeParseInt(invoice['Extra Atendimentos']);
    tryPush({
      name: InvoiceItemName.WhatsAppServiceExtra,
      description: InvoiceItemDescription.WhatsAppServiceExtra,
      unitPrice,
      quantity,
      discount: 0,
      totalPrice: unitPrice * quantity,
    });
  }

  if (
    invoice['Atendimento Disparo Pacote'] &&
    invoice['Atendimento Quantidade']
  ) {
    const unitPrice = parseCurrency(invoice['Atendimento Disparo Pacote']);
    const quantity = safeParseInt(invoice['Atendimento Quantidade']);
    tryPush({
      name: InvoiceItemName.WhatsAppServicePackage,
      description: InvoiceItemDescription.WhatsAppServicePackage,
      unitPrice,
      quantity,
      discount: 0,
      totalPrice: unitPrice * quantity,
    });
  }

  if (invoice['Utilidade Disparo Extra'] && invoice['Extra Utilidade']) {
    const unitPrice = parseCurrency(invoice['Utilidade Disparo Extra']);
    const quantity = safeParseInt(invoice['Extra Utilidade']);
    tryPush({
      name: InvoiceItemName.WhatsAppUtilityExtra,
      description: InvoiceItemDescription.WhatsAppUtilityExtra,
      unitPrice,
      quantity,
      discount: 0,
      totalPrice: unitPrice * quantity,
    });
  }

  if (invoice['Utilidade Disparo Pacote'] && invoice['Utilidade Quantidade']) {
    const unitPrice = parseCurrency(invoice['Utilidade Disparo Pacote']);
    const quantity = safeParseInt(invoice['Utilidade Quantidade']);
    tryPush({
      name: InvoiceItemName.WhatsAppUtilityPackage,
      description: InvoiceItemDescription.WhatsAppUtilityPackage,
      unitPrice,
      quantity,
      discount: 0,
      totalPrice: unitPrice * quantity,
    });
  }

  return items;
}

bulkImportInvoicesFromCsv(['./copy.csv']); // junho
