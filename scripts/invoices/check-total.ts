import { PrismaClient } from '@prisma/client';
import csv from 'csvtojson';

const prisma = new PrismaClient();

function parseCurrency(formatted: string): number {
  if (!formatted) return 0;

  const raw = formatted.replace(/R\$/g, '').trim();

  let normalized = raw;

  if (raw.match(/\d+\.\d{3},\d{2}$/)) {
    normalized = raw.replace(/\./g, '').replace(',', '.');
  } else if (raw.match(/\d{1,3}(,\d{3})+\.\d{2}$/)) {
    normalized = raw.replace(/,/g, '');
  } else if (raw.includes(',')) {
    normalized = raw.replace(',', '.');
  } else {
    normalized = raw;
  }

  const value = parseFloat(normalized);

  if (isNaN(value)) {
    console.warn(`Valor inválido: "${formatted}"`);
    return 0;
  }

  return Math.round(value * 100);
}

export interface InvoiceRow {
  Mes: string;
  'Client Name Revi': string;
  'Gupshup App': string;
  'Marketing Quantidade': string;
  'Disparos Marketing': string;
  'Marketing Disparo Pacote': string;
  'Utilidade Quantidade': string;
  'Utilidade Disparo Extra': string;
  'Extra Utilidade': string;
  'Atendimento Quantidade': string;
  'Atendimento Disparo Pacote': string;
  'Atendimento Disparo Extra': string;
  'Fixo Plataforma': string;
  'SMS Quantidade': string;
  'SMS Disparo Pacote': string;
  'SMS Disparo Extra': string;
  'Time de Sucesso': string;
  'Utilidade Disparo Pacote': string;
  'Extra SMS': string;
  'Excedentes Marketing': string;
  'Extra Atendimentos': string;
  'Marketing Disparo Extra': string;
  Setup: string;
  Desconto: string;
  Total: string;
}

async function main(path: string) {
  const data: InvoiceRow[] = await csv().fromFile(path);

  const apps = [...new Set(data.map((row) => row['Gupshup App']))];

  const companies = await prisma.company.findMany({
    where: { gupshupAppName: { in: apps } },
    select: { id: true, gupshupAppName: true },
  });

  const companyMap = new Map(companies.map((c) => [c.gupshupAppName, c.id]));

  const invoicesFromDB = await prisma.invoice.findMany({
    where: { companyId: { in: companies.map((c) => c.id) } },
    select: {
      companyId: true,
      value: true,
      referenceMonth: true,
    },
  });

  for (const row of data) {
    const companyId = companyMap.get(row['Gupshup App']);
    if (!companyId) {
      console.warn(`Empresa não encontrada para ${row['Gupshup App']}`);
      continue;
    }

    const dbInvoice = invoicesFromDB.find((inv) => inv.companyId === companyId);

    if (!dbInvoice) {
      console.warn(`Invoice não encontrada para empresa ${row['Gupshup App']}`);
      continue;
    }

    const totalPlanilha = parseCurrency(row.Total);
    const totalBanco = Number(dbInvoice.value);

    if (Math.abs(totalPlanilha - totalBanco) > 0.01) {
      console.log(`🛑 Diferença para ${row['Gupshup App']} (${row.Mes}):
        - Planilha: R$ ${(totalPlanilha / 100).toFixed(2)}
        - Banco:    R$ ${(totalBanco / 100).toFixed(2)}
      `);
    }
  }
}

main('../copy.csv');
