import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const gupshupTemplates = await prisma.gupshupTemplate.findMany();

  for (const gupshupTemplate of gupshupTemplates) {
    const buttons = gupshupTemplate.buttons as Array<any>;

    const quickReplyButtons = buttons?.filter(
      (button) => button.type === 'QUICK_REPLY',
    );

    if (quickReplyButtons?.length > 0) {
      for (const [index, button] of quickReplyButtons.entries()) {
        const messageButton = await prisma.messageTemplateButton.findUnique({
          where: {
            messageTemplateId_text_type: {
              messageTemplateId: gupshupTemplate.messageTemplateId,
              text: button.text,
              type: button.type,
            },
          },
        });

        if (messageButton) {
          await prisma.messageTemplateButton.update({
            where: {
              id: messageButton.id,
            },
            data: {
              index: index,
            },
          });

          console.log(
            `Botão '${button.text}' atualizado para a posição ${index}`,
          );
        }
      }
    }
  }
}

main()
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
