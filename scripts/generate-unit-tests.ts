import fs from 'fs';
import dotenv from 'dotenv';
import OpenAI from 'openai';
import path from 'path';

dotenv.config();

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

(async () => {
  try {
    const sourceFilePath = process.argv[2]; // Get the file path from command-line arguments
    if (!sourceFilePath) {
      console.error('Please provide a file path as an argument.');
      process.exit(1);
    }

    const sourceFileContent = fs.readFileSync(sourceFilePath, 'utf8');

    const testFilePath = sourceFilePath.replace('.ts', '.spec.ts');
    const resolvedTestFilePath = path.resolve(testFilePath);

    let testFileContent = '';
    if (!!fs.existsSync(resolvedTestFilePath)) {
      testFileContent = fs.readFileSync(testFilePath, 'utf8');
    }

    const newTestFileContent = await generateUnitTests(
      sourceFileContent,
      testFileContent,
    );

    fs.writeFileSync(resolvedTestFilePath, newTestFileContent);
  } catch (error: any) {
    console.error('An error occurred:', error.message);
    process.exit(1);
  }
})();

async function generateUnitTests(
  sourceCodeSnippet: string,
  testCodeSnippet: string,
): Promise<string> {
  try {
    const prompt = getGenerateUnitTestsPrompt(
      sourceCodeSnippet,
      testCodeSnippet,
    );
    // Call OpenAI API
    console.log('generating tests...');
    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini', // Use GPT-4 for advanced reasoning
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.2, // Low temperature for deterministic results
      max_tokens: 16384,
    });
    console.log('tests generated!');

    console.log('Tokens usage:', response.usage);
    const unitTests = response.choices[0]?.message?.content?.trim();

    return unitTests || '';
  } catch (error: any) {
    throw new Error(
      `An error occurred while generating unit tests: ${error.message}`,
    );
  }
}

function getGenerateUnitTestsPrompt(
  sourceCodeSnippet: string,
  testCodeSnippet: string,
): string {
  return `> You are a senior software engineer with expertise in **NestJS**, **Prisma ORM**, and **Jest** for unit testing. Your task is to write clean, efficient, and comprehensive unit tests for the following file:
>
> ### **Source Code**:
> \`\`\`typescript
> ${sourceCodeSnippet}
> \`\`\`
>
> ### **Previous Test Code** (if available):
> \`\`\`typescript
> ${testCodeSnippet}
> \`\`\`
>
> ### **Requirements**:
> 1. **Enhance or add missing unit tests** for all public methods, functions, or services defined in the file.
> 2. Use **Jest** as the testing framework.
> 3. Mock all dependencies, including:
>    - **Prisma ORM** methods, such as \`findFirst\`, \`create\`, \`update\`, \`delete\`, etc., using \`jest.fn()\` or libraries like \`@nestjs/testing\`.
>    - Any external services or modules injected via NestJS DI.
> 4. Ensure test cases include:
>    - **Positive scenarios**: Validate expected outputs for valid inputs.
>    - **Edge cases**: Test invalid inputs, empty data, and boundary conditions.
>    - **Error handling**: Simulate Prisma query failures and ensure appropriate exceptions are thrown or logged.
> 5. Organize tests with the **Arrange-Act-Assert** pattern for clarity.
> 6. Use clear and descriptive test names following this structure:
>    - \`should <do something> when <condition>\`
> 7. Ensure **100% test coverage** where possible, including:
>    - Service logic.
>    - Controller endpoints.
>    - Error scenarios.
> 8. If previous test code exists, **refactor or improve it** for:
>    - Better readability.
>    - Increased test coverage.
>    - Adherence to NestJS and Jest testing best practices.
> 9. Add **comments** explaining each test and mock setup.
>
> ### **Output**:
> - Provide the final Jest test file (e.g., \`file.service.spec.ts\` or \`file.controller.spec.ts\`).
> - Include:
>   - Required imports for \`TestingModule\`, \`PrismaService\`, and relevant mocks.
>   - Mock setup for **PrismaService** and other dependencies.
>   - The full Jest test code with test cases clearly structured.
> - Only include the test code without any comments or instructions or language type blocks.
>
> ### Example of Mocked PrismaService:
> \`\`\`typescript
> const prismaServiceMock = {
>   user: {
>     findFirst: jest.fn(),
>     create: jest.fn(),
>     update: jest.fn(),
>   },
> };
> \`\`\``;
}
