import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import { PhoneNumberUtils } from '../src/shared/utils/phone-number.utils';

const prisma = new PrismaClient();

const filePath =
  '/Users/<USER>/Downloads/meu-game-usado/filtered_clients.json';

async function processPhoneNumberUpdates() {
  // Ler o arquivo JSON
  const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf-8')) as {
    telefone: string;
    'cliente-email': string;
  }[];

  // const companyId = '2543fe7f-09c1-4cf4-80e2-e0334a21e9b6';
  const companyId = 'test';

  for (const { telefone, 'cliente-email': email } of jsonData) {
    try {
      const customers = await prisma.customer.findMany({
        where: {
          email,
          companyId,
        },
      });

      if (customers.length === 0) {
        console.log(
          `Cliente com e-mail ${email} não encontrado na empresa ${companyId}`,
        );
        continue;
      }

      let customer = customers[0];

      if (customers.length > 1) {
        for (const c of customers) {
          // customer final 7 numbers
          if (!c.phoneNumberId) {
            continue;
          }
          const customerPhoneNumber = c.phoneNumberId.slice(-7);

          if (telefone.includes(customerPhoneNumber)) {
            console.log('');
            console.log('');
            console.log(
              'cliente com email duplicado porém identificado pelo telefone',
            );
            console.log('customer id:', c.id);
            console.log('customer email:', email);
            console.log('customerPhoneNumberId:', c.phoneNumberId);

            console.log('telefone:', telefone);

            const formattedPhoneNumber =
              PhoneNumberUtils.formatPhoneNumber(telefone);

            console.log('formattedPhoneNumber:', formattedPhoneNumber);

            console.log('');
            console.log('');

            customer = c;
            break;
          }
        }
      }

      if (!customer) {
        console.log(
          `Cliente com e-mail ${email} não encontrado na empresa ${companyId}`,
        );
        continue;
      }

      const formattedPhoneNumber = PhoneNumberUtils.formatPhoneNumber(telefone);

      if (!PhoneNumberUtils.isValidPhoneNumber(formattedPhoneNumber)) {
        console.log(`Número de telefone inválido após formatação: ${telefone}`);
        continue;
      }

      await prisma.customer.update({
        where: { id: customer.id },
        data: { phoneNumberId: formattedPhoneNumber },
      });

      console.log(
        `Número de telefone atualizado para o cliente ${email}: ${formattedPhoneNumber}`,
      );
    } catch (error) {
      console.error(`Erro ao processar o cliente ${email}:`, error);
    }
  }
}

// Executar o script
processPhoneNumberUpdates()
  .catch((error) => {
    console.error('Erro ao executar o script:', error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
