{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug Nest Framework", "args": ["${workspaceFolder}/src/main.ts"], "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "-r", "ts-node/register", "-r", "tsconfig-paths/register"], "sourceMaps": true, "envFile": "${workspaceFolder}/.env", "cwd": "${workspaceRoot}", "console": "integratedTerminal", "protocol": "inspector"}]}