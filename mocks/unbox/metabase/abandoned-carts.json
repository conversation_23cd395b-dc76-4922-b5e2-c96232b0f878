{"data": {"rows": [["2024-07-07T15:41:39.799Z", "5a11327e-f649-4b54-a1f5-8a46a35598cf", "QHcNbpYZrKzncKrdR", "sunrize", "2024-08-09T10:39:53.023Z", "https://sunrize.com.br/carrinho/finalizar-pedido?id=5a11327e-f649-4b54-a1f5-8a46a35598cf&token=vk37dodfQgl0PSNdGEvsWhr0JxPZ5y3jKrPcnS9cJTQ", "N/A", "N/A", 0, "<PERSON>", "029.824.669-45", "rod<PERSON><PERSON><PERSON><PERSON>@icloud.com", "(48) 99917-2828", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "110", "Casa 2", "Campeche", "Florianópolis", "SC", "88065500", ["<PERSON><PERSON><PERSON><PERSON>"], ["Sabor:<PERSON>unil<PERSON>"], [1], [149.9], [], "N/A", "N/A", "N/A", "N/A"], ["2024-07-07T15:41:39.799Z", "3750f3d6-2ba6-4e63-84e0-f31e8086179e", "QHcNbpYZrKzncKrdR", "sunrize", "2024-08-08T13:25:35.829Z", "https://sunrize.com.br/carrinho/finalizar-pedido?id=3750f3d6-2ba6-4e63-84e0-f31e8086179e&token=23PHD5YhwnnLi0zGNU9dX1deK9BzxxtZDMJl3nKxi6U", "N/A", "PRIMEIRACOMPRA", 14.99, "<PERSON><PERSON>", "211.428.740-87", "<EMAIL>", "(51) 99746-0913", "<PERSON><PERSON>", "903", "N/A", "Liberdade", "Novo Hamburgo", "RS", "93330150", ["Proteína Chocolate"], ["Sabor: Chocolate"], [1], [149.9], [], "Correios PAC", "Correios PAC", 20.89, 9], ["2024-07-07T15:41:39.799Z", "9d48b0a4-f833-4fea-92ed-b83ab0e500b1", "QHcNbpYZrKzncKrdR", "sunrize", "2024-08-08T12:45:29.476Z", "https://sunrize.com.br/carrinho/finalizar-pedido?id=9d48b0a4-f833-4fea-92ed-b83ab0e500b1&token=ol5N0-9COQGxfav77ZMhCGyP4oqH_xAYOuBjD4S3FIo", "N/A", "PRIMEIRACOMPRA", 14.99, "<PERSON>", "099.890.337-06", "<EMAIL>", "(21) 99855-0675", "<PERSON><PERSON> de São Vicente", "10", "Apto. 705", "Gávea", "Rio de Janeiro", "RJ", "22451040", ["<PERSON><PERSON><PERSON><PERSON>"], ["Sabor:<PERSON>unil<PERSON>"], [1], [149.9], [], "Total Express", "Total Express", 21.11, 4], ["2024-07-07T15:41:39.799Z", "208221cc-4cc5-4688-8ef4-27c5f9c8073a", "QHcNbpYZrKzncKrdR", "sunrize", "2024-08-07T19:43:57.673Z", "https://sunrize.com.br/carrinho/finalizar-pedido?id=208221cc-4cc5-4688-8ef4-27c5f9c8073a&token=fthbUyHBDM9F1Xx1qhvdOr4uFJtDMPwR3sjkJ3GvhAo", "N/A", "PRIMEIRACOMPRA", 14.99, "<PERSON>", "148.681.636-39", "<EMAIL>", "(31) 99975-3771", "Rua Porto Rico", "136", "apt 301", "Itapoã", "Belo Horizonte", "MG", "31710370", ["<PERSON><PERSON><PERSON><PERSON>"], ["Sabor:<PERSON>unil<PERSON>"], [1], [149.9], [], "N/A", "N/A", "N/A", "N/A"], ["2024-07-07T15:41:39.799Z", "9da87ff1-021c-487b-a56c-7392e2486329", "QHcNbpYZrKzncKrdR", "sunrize", "2024-08-07T15:31:26.442Z", "https://sunrize.com.br/carrinho/finalizar-pedido?id=9da87ff1-021c-487b-a56c-7392e2486329&token=rsuwhAwvQm89EfdEp0pfaH1ZvlxJq1uYYUtlLyBLHdF", "N/A", "PRIMEIRACOMPRA", 26.982, "<PERSON><PERSON>", "070.437.106-50", "<EMAIL>", "(31) 98730-7675", "Rua Rio Grande do Norte", "916", "1002", "Funcionários", "Belo Horizonte", "MG", "30130131", ["Combo 2 Proteínas <PERSON> (10% off)"], ["sabor:<PERSON><PERSON><PERSON><PERSON> (2un)"], [1], [269.82], [299.8], "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", 36.24, 3], ["2024-07-07T15:41:39.799Z", "40a5a7d9-f9ac-4328-b4fb-b862d70a1bbb", "QHcNbpYZrKzncKrdR", "sunrize", "2024-08-07T11:07:23.678Z", "https://sunrize.com.br/carrinho/finalizar-pedido?id=40a5a7d9-f9ac-4328-b4fb-b862d70a1bbb&token=AE3nIBf1qpwg0WWHm6LlKL4cGrGYrpNCIAd6QqS-Gi8", "N/A", "N/A", 0, "<PERSON>", "014.256.772-82", "<EMAIL>", "(92) 99614-1319", "<PERSON><PERSON>", "498", "N/A", "Nova Cidade", "Manaus", "AM", "69017324", ["Proteína Chocolate"], ["Sabor: Chocolate"], [1], [149.9], [], "N/A", "N/A", "N/A", "N/A"], ["2024-07-07T15:41:39.799Z", "fb3538c7-dc06-4a55-b301-9fc33531ba3d", "QHcNbpYZrKzncKrdR", "sunrize", "2024-08-07T11:03:26.234Z", "https://sunrize.com.br/carrinho/finalizar-pedido?id=fb3538c7-dc06-4a55-b301-9fc33531ba3d&token=OfeEmbAKQfzKIMSJNcL__hCtshEtqsIGv_jJEJDzV2i", "N/A", "LORESOUZA", 14.99, "<PERSON>", "067.330.785-97", "<EMAIL>", "(71) 98536-1574", "<PERSON><PERSON>", "231", "N/A", "<PERSON><PERSON><PERSON>", "Salvador", "BA", "41210040", ["Proteína Chocolate"], ["Sabor: Chocolate"], [1], [149.9], [], "Correios PAC", "Correios PAC", 23.24, 9], ["2024-07-07T15:41:39.799Z", "4075f6ce-48ef-4024-ab65-10ffc067a4e5", "QHcNbpYZrKzncKrdR", "sunrize", "2024-08-07T10:07:31.942Z", "https://sunrize.com.br/carrinho/finalizar-pedido?id=4075f6ce-48ef-4024-ab65-10ffc067a4e5&token=ySnCA6y3AuK21sQrjwlJPav2dawseE_R5zJtC44cqS8", "N/A", "N/A", 0, "<PERSON><PERSON><PERSON>", "395.059.418-38", "<EMAIL>", "(19) 99713-3624", "<PERSON><PERSON>", "344", "apto 13", "Centro", "Piracicaba", "SP", "13400300", ["<PERSON><PERSON><PERSON><PERSON>"], ["Sabor:<PERSON>unil<PERSON>"], [1], [149.9], [], "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", 11.95, 3]], "cols": [{"display_name": "filterDate", "source": "native", "field_ref": ["field", "filterDate", {"base-type": "type/Instant"}], "name": "filterDate", "base_type": "type/Instant", "effective_type": "type/Instant"}, {"display_name": "_id", "source": "native", "field_ref": ["field", "_id", {"base-type": "type/Text"}], "name": "_id", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "ID loja", "source": "native", "field_ref": ["field", "ID loja", {"base-type": "type/Text"}], "name": "ID loja", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Slug loja", "source": "native", "field_ref": ["field", "Slug loja", {"base-type": "type/Text"}], "name": "Slug loja", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "C<PERSON><PERSON> em", "source": "native", "field_ref": ["field", "C<PERSON><PERSON> em", {"base-type": "type/Instant"}], "name": "C<PERSON><PERSON> em", "base_type": "type/Instant", "effective_type": "type/Instant"}, {"display_name": "URL do carrinho", "source": "native", "field_ref": ["field", "URL do carrinho", {"base-type": "type/Text"}], "name": "URL do carrinho", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "ID template", "source": "native", "field_ref": ["field", "ID template", {"base-type": "type/Text"}], "name": "ID template", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Cupom de desconto", "source": "native", "field_ref": ["field", "Cupom de desconto", {"base-type": "type/Text"}], "name": "Cupom de desconto", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "<PERSON>or de des<PERSON>", "source": "native", "field_ref": ["field", "<PERSON>or de des<PERSON>", {"base-type": "type/Float"}], "name": "<PERSON>or de des<PERSON>", "base_type": "type/Float", "effective_type": "type/Float"}, {"display_name": "Nome completo", "source": "native", "field_ref": ["field", "Nome completo", {"base-type": "type/Text"}], "name": "Nome completo", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "CPF", "source": "native", "field_ref": ["field", "CPF", {"base-type": "type/Text"}], "name": "CPF", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Email", "source": "native", "field_ref": ["field", "Email", {"base-type": "type/Text"}], "name": "Email", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Telefone", "source": "native", "field_ref": ["field", "Telefone", {"base-type": "type/Text"}], "name": "Telefone", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Endereço", "source": "native", "field_ref": ["field", "Endereço", {"base-type": "type/Text"}], "name": "Endereço", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Número", "source": "native", "field_ref": ["field", "Número", {"base-type": "type/Text"}], "name": "Número", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Complemento", "source": "native", "field_ref": ["field", "Complemento", {"base-type": "type/Text"}], "name": "Complemento", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Bairro", "source": "native", "field_ref": ["field", "Bairro", {"base-type": "type/Text"}], "name": "Bairro", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Cidade", "source": "native", "field_ref": ["field", "Cidade", {"base-type": "type/Text"}], "name": "Cidade", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Estado", "source": "native", "field_ref": ["field", "Estado", {"base-type": "type/Text"}], "name": "Estado", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "CEP", "source": "native", "field_ref": ["field", "CEP", {"base-type": "type/Text"}], "name": "CEP", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Lista dos produtos", "source": "native", "field_ref": ["field", "Lista dos produtos", {"base-type": "type/Array"}], "name": "Lista dos produtos", "base_type": "type/Array", "effective_type": "type/Array"}, {"display_name": "Lista das variantes", "source": "native", "field_ref": ["field", "Lista das variantes", {"base-type": "type/Array"}], "name": "Lista das variantes", "base_type": "type/Array", "effective_type": "type/Array"}, {"display_name": "Lista das quantidades", "source": "native", "field_ref": ["field", "Lista das quantidades", {"base-type": "type/Array"}], "name": "Lista das quantidades", "base_type": "type/Array", "effective_type": "type/Array"}, {"display_name": "Lista dos preços", "source": "native", "field_ref": ["field", "Lista dos preços", {"base-type": "type/Array"}], "name": "Lista dos preços", "base_type": "type/Array", "effective_type": "type/Array"}, {"display_name": "Lista do preços originais", "source": "native", "field_ref": ["field", "Lista do preços originais", {"base-type": "type/Array"}], "name": "Lista do preços originais", "base_type": "type/Array", "effective_type": "type/Array"}, {"display_name": "Nome do frete selecionado", "source": "native", "field_ref": ["field", "Nome do frete selecionado", {"base-type": "type/Text"}], "name": "Nome do frete selecionado", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Label interno do frete selecionado", "source": "native", "field_ref": ["field", "Label interno do frete selecionado", {"base-type": "type/Text"}], "name": "Label interno do frete selecionado", "base_type": "type/Text", "effective_type": "type/Text"}, {"display_name": "Custo do frete selecionado", "source": "native", "field_ref": ["field", "Custo do frete selecionado", {"base-type": "type/Float"}], "name": "Custo do frete selecionado", "base_type": "type/Float", "effective_type": "type/Float"}, {"display_name": "Tempo (em dias) de entrega do frete selecionado", "source": "native", "field_ref": ["field", "Tempo (em dias) de entrega do frete selecionado", {"base-type": "type/Integer"}], "name": "Tempo (em dias) de entrega do frete selecionado", "base_type": "type/Integer", "effective_type": "type/Integer"}], "insights": null, "results_timezone": "GMT"}, "json_query": {"parameters": [{"type": "date/range", "value": "2024-08-07~2024-08-09", "id": "b0c61d26-4348-4332-95b1-58d0cf5f8c81", "target": ["dimension", ["template-tag", "var_createdAt"]]}]}, "status": "completed"}