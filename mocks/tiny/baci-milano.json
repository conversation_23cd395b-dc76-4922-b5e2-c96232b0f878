{"customers": {"city": {"fileColumn": "Município"}, "name": {"fileColumn": "Nome do contato"}, "email": {"fileColumn": "e-mail"}, "state": {"fileColumn": "UF"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Fone"}, "phoneNumberId2": {"fileColumn": "<PERSON><PERSON><PERSON>"}}, "orders": {"sourceId": {"fileColumn": "ID"}, "value": {"fileColumn": "Valor final dos produtos", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {"fileColumn": "Situação"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "Data"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Valor unitário", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalItemsQuantity": {"fileColumn": "Quantidade", "aggregationType": "sum", "multiplier": 1, "decimalSeparator": "."}, "totalShippingValue": {"fileColumn": "Frete pedido rateado", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalDiscountsValue": {}, "salesChannel": {}, "storeName": {}}}