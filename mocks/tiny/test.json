{"customers": {"city": {"fileColumn": "Município"}, "name": {"fileColumn": "Nome do contato"}, "email": {"fileColumn": "e-mail"}, "state": {"fileColumn": "UF"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Fone"}, "phoneNumberId2": {"fileColumn": "<PERSON><PERSON><PERSON>"}}, "orders": {"sourceId": {"fileColumn": "ID"}, "value": {"fileColumn": "Valor Efetivamente Pago com Frete", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {"fileColumn": "Situação"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd", "fileColumn": "Data"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {}, "totalDiscountsValue": {}, "salesChannel": {"fileColumn": "CANAL"}, "storeName": {}}}