{"responseId": "ed56af6c-f36c-42a2-819f-acef6bafa4aa-3d49d937", "queryResult": {"queryText": "gostaria de pedir uma pizza", "parameters": {"number": 1, "pizza_topping": "pedir", "size": ""}, "fulfillmentText": "Qual tamanho?", "fulfillmentMessages": [{"text": {"text": ["Qual tamanho?"]}}], "outputContexts": [{"name": "projects/pizza-bot-ppooye/agent/sessions/7afa20c0-f30b-8ea8-8639-e12e049c52eb/contexts/d55feaf4-bbcd-48eb-9482-49252b92cd5a_id_dialog_context", "lifespanCount": 2, "parameters": {"number": 1, "number.original": "uma", "pizza_topping": "pedir", "pizza_topping.original": "pedir", "size": "", "size.original": ""}}, {"name": "projects/pizza-bot-ppooye/agent/sessions/7afa20c0-f30b-8ea8-8639-e12e049c52eb/contexts/order_pizza_dialog_context", "lifespanCount": 2, "parameters": {"number": 1, "number.original": "uma", "pizza_topping": "pedir", "pizza_topping.original": "pedir", "size": "", "size.original": ""}}, {"name": "projects/pizza-bot-ppooye/agent/sessions/7afa20c0-f30b-8ea8-8639-e12e049c52eb/contexts/order_pizza_dialog_params_size", "lifespanCount": 1, "parameters": {"number": 1, "number.original": "uma", "pizza_topping": "pedir", "pizza_topping.original": "pedir", "size": "", "size.original": ""}}, {"name": "projects/pizza-bot-ppooye/agent/sessions/7afa20c0-f30b-8ea8-8639-e12e049c52eb/contexts/__system_counters__", "lifespanCount": 1, "parameters": {"no-input": 0, "no-match": 0, "number": 1, "number.original": "uma", "pizza_topping": "pedir", "pizza_topping.original": "pedir", "size": "", "size.original": ""}}], "intent": {"name": "projects/pizza-bot-ppooye/agent/intents/d55feaf4-bbcd-48eb-9482-49252b92cd5a", "displayName": "order.pizza"}, "intentDetectionConfidence": 0.6745012, "languageCode": "pt-br", "sentimentAnalysisResult": {"queryTextSentiment": {"score": 0.3, "magnitude": 0.3}}}, "originalDetectIntentRequest": {"source": "DIALOGFLOW_CONSOLE", "payload": {}}, "session": "projects/pizza-bot-ppooye/agent/sessions/7afa20c0-f30b-8ea8-8639-e12e049c52eb"}