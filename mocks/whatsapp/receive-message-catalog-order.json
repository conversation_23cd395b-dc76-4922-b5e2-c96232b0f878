{"app": "ReviApp2", "timestamp": 1752785462658, "version": 2, "type": "message", "payload": {"id": "wamid.HBgMNTU2Mjg1MTk0NDE1FQIAEhgUM0EwNTQ3RkQ3MDAwNjFFNTU2QjMA", "source": "556285194415", "type": "order", "payload": {"text": ""}, "sender": {"phone": "556285194415", "name": "<PERSON><PERSON>", "country_code": "55", "dial_code": "6285194415"}, "context": {"catalog": {"id": "1335604764803941", "order": {"items": [{"id": "mii8ookvqu", "currency": "BRL", "amount": "150", "quantity": "2"}, {"id": "zsr85haef6", "currency": "BRL", "amount": "18.5", "quantity": "1"}]}}, "forwarded": false, "frequently_forwarded": false}}}