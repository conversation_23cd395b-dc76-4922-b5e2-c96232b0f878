{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome"}, "email": {"fileColumn": "E Mail"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Fone"}}, "orders": {"sourceId": {"fileColumn": "CONCATENATE(Romaneio,Fone)"}, "value": {"fileColumn": "Valor", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"fileColumn": "Data", "dateFormat": "D/M/YY", "dateLocale": "ptBR"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "Qtde", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}}}