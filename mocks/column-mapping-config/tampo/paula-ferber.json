{"customers": {"city": {}, "name": {"fileColumn": "cliente"}, "email": {"fileColumn": "email"}, "state": {}, "sourceId": {"fileColumn": "numcli"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "dddcel"}, "phoneNumberId2": {"fileColumn": "dddtelef"}}, "orders": {"sourceId": {"fileColumn": "CONCATENATE(numvenda,numcli)"}, "value": {"fileColumn": "totvenda", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "M/d/yy", "fileColumn": "diavenda"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "quant", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}}}