{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do Cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}}, "orders": {"sourceId": {"fileColumn": "<PERSON><PERSON> <PERSON> Comp<PERSON>"}, "value": {"fileColumn": "Valor <PERSON>", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "Cupom"}, "status": {"fileColumn": "Status do Pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy HH:mm:ss", "fileColumn": "<PERSON> da Venda"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "Quantidade de produtos", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}, "storeName": {}, "salesChannel": {}}}