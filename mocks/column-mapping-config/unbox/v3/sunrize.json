{"customers": {"city": {}, "name": {"fileColumn": "Nome do Cliente"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "End - Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}}, "orders": {"value": {"fileColumn": "Valor do Pedido (R$)", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "Cupom de Desconto"}, "status": {"fileColumn": "Status do Pedido"}, "sourceId": {"fileColumn": "ID do Pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"fileColumn": "Data Pedido", "dateFormat": "dd/MM/yyyy HH:mm:ss"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Valor dos produtos (real) (R$)", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalItemsQuantity": {}, "totalShippingValue": {"fileColumn": "Custo do frete (storefront) (R$)", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalDiscountsValue": {"fileColumn": "Valor dos descontos (R$)", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}}}