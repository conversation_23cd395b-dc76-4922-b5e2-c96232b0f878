{"customers": {"city": {}, "name": {"fileColumn": "Comprador/a"}, "email": {"fileColumn": "E-mail"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}}, "orders": {"value": {"fileColumn": "(=) Total Pedido", "decimalSeparator": ".", "aggregationType": "first"}, "coupon": {"fileColumn": "Cupom de Desconto"}, "status": {"fileColumn": "Status Venda"}, "sourceId": {"fileColumn": "Id Pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"fileColumn": "Data da venda orig", "dateFormat": "d 'de' <PERSON><PERSON>. 'de' yyyy, HH:mm:ss"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {"fileColumn": "(-) Frete centralizado", "decimalSeparator": ".", "aggregationType": "first"}, "totalDiscountsValue": {}}}