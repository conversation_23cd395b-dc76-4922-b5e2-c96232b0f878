{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do Cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}}, "orders": {"value": {"fileColumn": "Valor do Pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "<PERSON><PERSON>"}, "status": {"fileColumn": "Status do Pedido"}, "sourceId": {"fileColumn": "Numero do Pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"fileColumn": "Data do Pedido Date", "dateFormat": "dd/MM/yyyy"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {}, "totalDiscountsValue": {}, "salesChannel": {}, "storeName": {}}}