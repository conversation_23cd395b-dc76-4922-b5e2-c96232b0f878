{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do Cliente"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}}, "orders": {"value": {"fileColumn": "Valor do Pedido (R$)", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "Cupom de desconto"}, "status": {"fileColumn": "Status do Pedido"}, "sourceId": {"fileColumn": "ID do Pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"fileColumn": "Data Pedido", "dateFormat": "yyyy-MM-dd"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Valor dos produtos (real) (R$)", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalItemsQuantity": {}, "totalShippingValue": {"fileColumn": "Custo do frete (storefront) (R$)", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalDiscountsValue": {"fileColumn": "Valor dos descontos (R$)", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "salesChannel": {}, "storeName": {}}}