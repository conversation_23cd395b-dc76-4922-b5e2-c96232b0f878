{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone 1"}, "phoneNumberId2": {"fileColumn": "Telefone 2"}}, "orders": {"sourceId": {"fileColumn": "Nº pedido"}, "value": {"fileColumn": "Total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {}, "status": {"fileColumn": "Status"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy HH:mm", "fileColumn": "Data e Hora", "timeOffset": 3}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Produ<PERSON>", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalItemsQuantity": {}, "totalShippingValue": {"fileColumn": "Frete", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalDiscountsValue": {"fileColumn": "Promoção_p", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "salesChannel": {}, "storeName": {}}}