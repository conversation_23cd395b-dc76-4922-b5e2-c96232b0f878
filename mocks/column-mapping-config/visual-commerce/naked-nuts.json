{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Cliente"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}}, "orders": {"sourceId": {"fileColumn": "ID Pedido"}, "value": {"fileColumn": "Total", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "coupon": {}, "status": {"fileColumn": "Status"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy HH:mm", "fileColumn": "Data Captura", "timeOffset": 3}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {"fileColumn": "Frete", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "totalDiscountsValue": {}}}