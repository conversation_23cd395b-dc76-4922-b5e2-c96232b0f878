{"orders": {"value": {"fileColumn": "value", "multiplier": 1, "aggregationType": "first", "decimalSeparator": "."}, "coupon": {"fileColumn": "coupon"}, "status": {"fileColumn": "status"}, "sourceId": {"fileColumn": "source_id"}, "storeName": {"fileColumn": "store_name"}, "salesChannel": {"fileColumn": "sales_channel"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ss.SSS", "fileColumn": "source_created_at"}, "sourceUpdatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ss.SSS", "fileColumn": "source_updated_at"}, "totalItemsValue": {"fileColumn": "total_items_value", "multiplier": 1, "aggregationType": "first", "decimalSeparator": "."}, "totalItemsQuantity": {"fileColumn": "total_items_quantity", "multiplier": 1, "aggregationType": "first", "decimalSeparator": "."}, "totalShippingValue": {"fileColumn": "total_shipping_value", "multiplier": 1, "aggregationType": "first", "decimalSeparator": "."}, "totalDiscountsValue": {"fileColumn": "total_discounts_value", "multiplier": 1, "aggregationType": "first", "decimalSeparator": "."}}, "customers": {"city": {"fileColumn": "city"}, "name": {"fileColumn": "customer_name"}, "email": {"fileColumn": "email"}, "state": {"fileColumn": "state"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "phone_number_id"}, "cpf": {}}}