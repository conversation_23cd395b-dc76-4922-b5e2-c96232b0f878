{"customers": {"city": {"fileColumn": "cidade"}, "name": {"fileColumn": "nome_cliente"}, "email": {"fileColumn": "email"}, "state": {"fileColumn": "estado"}, "sourceId": {"fileColumn": "id_cliente"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "telefone"}}, "orders": {"sourceId": {"fileColumn": "id_pedido"}, "value": {"fileColumn": "valor_pedido", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {"fileColumn": "cupom"}, "status": {"fileColumn": "status_pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd", "fileColumn": "data_pedido"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "valor_produto", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalItemsQuantity": {"fileColumn": "quantidade", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {"fileColumn": "desconto", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "storeName": {}, "salesChannel": {}}}