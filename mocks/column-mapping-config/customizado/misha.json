{"customers": {"city": {"fileColumn": "CIDADE_CLIENTE"}, "name": {"fileColumn": "NOME_CLIENTE"}, "email": {"fileColumn": "EMAIL_CLIENTE"}, "state": {"fileColumn": "UF_CLIENTE"}, "sourceId": {"fileColumn": "COD_CLIENTE"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "CEL_CLIENTE"}}, "orders": {"sourceId": {"fileColumn": "CONCATENATE(COD_CLIENTE,DOCUMENTO)"}, "value": {"fileColumn": "VLR_FATURADO", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "M/d/yyyy", "fileColumn": "DATA_FATURAMENTO", "dateLocale": "enUS"}, "sourceUpdatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ss", "fileColumn": "LASTUPDATEON", "dateLocale": "enUS"}, "totalItemsValue": {"fileColumn": "PRECO_VENDA", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalItemsQuantity": {"fileColumn": "QTD_PRODUTOS", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {"fileColumn": "VLR_FRETE", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalDiscountsValue": {"fileColumn": "VLR_DESCONTO", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "storeName": {"fileColumn": "NOME_LOJA"}, "salesChannel": {"fileColumn": "CANAL"}}}