{"customers": {"city": {"fileColumn": "UNIDADE"}, "name": {"fileColumn": "PACIENTE"}, "email": {}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "CELULAR"}, "phoneNumberId2": {"fileColumn": "TRABALHO"}}, "orders": {"sourceId": {"fileColumn": "ID_Atendimento"}, "value": {"fileColumn": "Valor", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {}, "status": {"fileColumn": "Status"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "DATA"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {}, "totalDiscountsValue": {}, "storeName": {}, "salesChannel": {}}}