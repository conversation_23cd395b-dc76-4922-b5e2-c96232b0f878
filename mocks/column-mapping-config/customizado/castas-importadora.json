{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do Cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {"fileColumn": "Código cliente"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}, "phoneNumberId2": {"fileColumn": "Telefone"}}, "orders": {"sourceId": {"fileColumn": "Pedido"}, "value": {"fileColumn": "Total", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "Cupom desconto"}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy HH:mm:ss", "fileColumn": "COMBINE(Data,Hora)"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Subtotal produtos", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "totalItemsQuantity": {}, "totalShippingValue": {"fileColumn": "Frete valor", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "totalDiscountsValue": {"fileColumn": "Desconto", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "storeName": {}, "salesChannel": {"fileColumn": "Canal de venda"}}}