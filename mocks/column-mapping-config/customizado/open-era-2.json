{"customers": {
    "city": {"fileColumn": "CIDADE_CLIENTE"},
    "name": {"fileColumn": "NOME_CLIENTE"},
    "email": {"fileColumn": "EMAIL_CLIENTE"},
    "state": {"fileColumn": "UF_CLIENTE"},
    "sourceId": {"fileColumn": "COD_CLIENTE"},
    "birthDate": {},
    "phoneNumberId": {"fileColumn": "CEL_CLIENTE"}
  },
  "orders": {
    "sourceId": {"fileColumn": "CONCATENATE(COD_CLIENTE,DOCUMENTO)"},
    "value": {
      "fileColumn": "VLR_FATURADO",
      "multiplier": 100,
      "aggregationType": "sum",
      "decimalSeparator": "."
    },
    "coupon": {},
    "status": {},
    "totalTaxValue": {},
    "sourceCreatedAt": {
      "dateFormat": "M/d/yyyy",
      "dateLocale": "enUS",
      "fileColumn": "DATA_FATURAMENTO",
      "timeOffset": 3
    },
    "sourceUpdatedAt": {
      "dateFormat": "yyyy-MM-dd HH:mm:ss.SSS",
      "dateLocale": "enUS",
      "fileColumn": "LASTUPDATEON",
      "timeOffset": 3
    },
    "totalItemsValue": {
      "fileColumn": "PRECO_VENDA",
      "multiplier": 100,
      "aggregationType": "sum",
      "decimalSeparator": "."
    },
    "totalItemsQuantity": {
      "fileColumn": "QTD_PRODUTOS",
      "multiplier": 1,
      "aggregationType": "sum",
      "decimalSeparator": "."
    },
    "totalShippingValue": {
      "fileColumn": "VLR_FRETE",
      "multiplier": 100,
      "aggregationType": "sum",
      "decimalSeparator": "."
    },
    "totalDiscountsValue": {
      "fileColumn": "VLR_DESCONTO",
      "multiplier": 100,
      "aggregationType": "sum",
      "decimalSeparator": "."
    },
    "storeName": {"fileColumn": "NOME_LOJA"},
    "salesChannel": {"fileColumn": "CANAL"},
  }
}
