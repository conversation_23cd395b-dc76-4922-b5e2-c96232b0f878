{"customers": {"city": {}, "name": {"fileColumn": "Nome do cliente"}, "email": {"fileColumn": "email"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "telefone"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "ID"}, "value": {"fileColumn": "valor", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd", "fileColumn": "Data Pedido"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {}, "totalDiscountsValue": {}, "salesChannel": {}, "storeName": {}}}