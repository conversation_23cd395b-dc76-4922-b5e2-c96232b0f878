{"orders": {"value": {"fileColumn": "PREÇO VENDA", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": "."}, "coupon": {}, "status": {}, "sourceId": {"fileColumn": "Pedido"}, "storeName": {}, "salesChannel": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "M/d/yyyy", "fileColumn": "Data"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "QUANTIDADE VENDA", "multiplier": 1, "aggregationType": "sum", "decimalSeparator": ","}, "totalShippingValue": {}, "totalDiscountsValue": {}}, "customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do Cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {"fileColumn": "Código cliente"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}, "phoneNumberId2": {"fileColumn": "Telefone"}}}