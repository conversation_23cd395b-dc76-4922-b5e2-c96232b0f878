{"customers": {"city": {"fileColumn": "Cidade de entrega"}, "name": {"fileColumn": "Nome do destinatário"}, "email": {"fileColumn": "Email de contato"}, "state": {"fileColumn": "Estado de entrega"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone do destinatário"}}, "orders": {"sourceId": {"fileColumn": "Número do pedido"}, "value": {"fileColumn": "Total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "Código de <PERSON>om"}, "status": {"fileColumn": "Status do pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"fileColumn": "Data de criação", "dateFormat": "MMM d, yyyy", "dateLocale": "enUS"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {"fileColumn": "<PERSON><PERSON><PERSON><PERSON> de frete", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalDiscountsValue": {}}}