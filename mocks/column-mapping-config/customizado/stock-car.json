{"customers": {"city": {"fileColumn": "cidade"}, "name": {"fileColumn": "fantasia"}, "email": {"fileColumn": "email"}, "state": {"fileColumn": "UF"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "TEL_1"}, "phoneNumberId2": {"fileColumn:": "cel"}}, "orders": {"sourceId": {"fileColumn": "Requisicao"}, "value": {"fileColumn": "Total", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd", "fileColumn": "Data"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "Qtdade", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}, "salesChannel": {}, "storeName": {}}}