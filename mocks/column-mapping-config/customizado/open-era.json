{"orders": {"value": {"fileColumn": "VLR_FATURADO", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": "."}, "coupon": {}, "status": {}, "sourceId": {"fileColumn": "CONCATENATE(COD_CLIENTE,DOCUMENTO)"}, "storeName": {"fileColumn": "NOME_LOJA"}, "salesChannel": {"fileColumn": "CANAL"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "M/d/yyyy", "dateLocale": "enUS", "fileColumn": "DATA_FATURAMENTO"}, "sourceUpdatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ss.SSS", "dateLocale": "enUS", "fileColumn": "LASTUPDATEON"}, "totalItemsValue": {"fileColumn": "PRECO_VENDA", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": "."}, "totalItemsQuantity": {"fileColumn": "QTD_PRODUTOS", "multiplier": 1, "aggregationType": "sum", "decimalSeparator": "."}, "totalShippingValue": {"fileColumn": "VLR_FRETE", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": "."}, "totalDiscountsValue": {"fileColumn": "VLR_DESCONTO", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": "."}}, "customers": {"city": {"fileColumn": "CIDADE_CLIENTE"}, "name": {"fileColumn": "NOME_CLIENTE"}, "email": {"fileColumn": "EMAIL_CLIENTE"}, "state": {"fileColumn": "UF_CLIENTE"}, "sourceId": {"fileColumn": "COD_CLIENTE"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "CEL_CLIENTE"}}}