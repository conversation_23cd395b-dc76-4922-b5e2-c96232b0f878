{"customers": {"city": {}, "name": {"fileColumn": "razao_cliente"}, "email": {"fileColumn": "email_cliente"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "cel_cliente"}, "phoneNumberId2": {"fileColumn": "fone_cliente"}}, "orders": {"sourceId": {"fileColumn": "CONCATENATE(cod_cliente,data_documento,valor_total)"}, "value": {"fileColumn": "valor_total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd'T'HH:mm:ss", "fileColumn": "data_documento"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {}, "totalDiscountsValue": {}}}