{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}, "phoneNumberId2": {"fileColumn:": "Telefone"}}, "orders": {"sourceId": {"fileColumn": "Número do pedido"}, "value": {"fileColumn": "Valor do pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {}, "status": {"fileColumn": "Status do pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "Data da realização do pedido"}, "sourceUpdatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "Data da última modificação"}, "totalItemsValue": {"fileColumn": "Preço", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {"fileColumn": "Valor do frete", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 1}, "totalDiscountsValue": {"fileColumn": "Valor do desconto", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 1}, "salesChannel": {}, "storeName": {}}}