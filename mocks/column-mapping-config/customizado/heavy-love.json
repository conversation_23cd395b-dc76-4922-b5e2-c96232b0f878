{"customers": {"city": {}, "name": {"fileColumn": "nome_comprador"}, "email": {"fileColumn": "email_comprador"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "fone_comprador"}}, "orders": {"sourceId": {"fileColumn": "id_transacao"}, "value": {"fileColumn": "price_ing", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "id_key"}, "status": {"fileColumn": "status"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd", "fileColumn": "dt_pedido"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "num_ing", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}}}