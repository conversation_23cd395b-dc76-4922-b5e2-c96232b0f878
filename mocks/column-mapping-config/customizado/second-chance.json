{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone 1"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "Nº pedido"}, "value": {"fileColumn": "Preço venda", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data - Recebimento", "dateFormat": "dd/MM/yyyy"}, "status": {"fileColumn": "Status"}, "coupon": {"fileColumn": "Cupom"}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalItemsValue": {}, "totalShippingValue": {}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {}, "salesChannel": {}, "storeName": {}}}