{"customers": {"city": {"fileColumn": "cidade_cliente"}, "name": {"fileColumn": "razao_cliente"}, "email": {"fileColumn": "email_cliente"}, "state": {}, "sourceId": {"fileColumn": "cod_cliente"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "cel_cliente"}, "phoneNumberId2": {"fileColumn": "fone_cliente"}}, "orders": {"sourceId": {"fileColumn": "CONCATENATE(cod_cliente,documento)"}, "value": {"fileColumn": "valor_total", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {"fileColumn": "status"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd'T'HH:mm:ss", "fileColumn": "data_documento"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "quantidade", "aggregationType": "sum", "decimalSeparator": ".", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}}}