{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}, "phoneNumberId2": {"fileColumn:": "Telefone"}}, "orders": {"sourceId": {"fileColumn": "Número do pedido"}, "value": {"fileColumn": "Valor do pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "Nome do Cupom"}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "Data de adição do último histórico"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Preço", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {"fileColumn": "Valor do desconto", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "salesChannel": {}, "storeName": {}}}