{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do Cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "Numero do Pedido"}, "value": {"fileColumn": "Valor do Pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "<PERSON><PERSON>"}, "status": {"fileColumn": "Status do Pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "Data do Pedido Date"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {}, "totalDiscountsValue": {}, "salesChannel": {}, "storeName": {}}}