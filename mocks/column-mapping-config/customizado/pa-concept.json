{"orders": {"value": {"fileColumn": "Total da Venda", "multiplier": 100, "aggregationType": "first", "decimalSeparator": ","}, "coupon": {"fileColumn": "Cupom"}, "status": {"fileColumn": "Status"}, "sourceId": {"fileColumn": "Pedido #"}, "storeName": {}, "salesChannel": {}, "totalTaxValue": {}, "sourceCreatedAt": {"locale": "enUS", "dateFormat": "yyyy-MM-dd HH:mm:ss", "fileColumn": "<PERSON> da Compra"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Gross Profit", "multiplier": 100, "aggregationType": "first", "decimalSeparator": ","}, "totalItemsQuantity": {"fileColumn": "Qtd Vendida", "multiplier": 1, "aggregationType": "first", "decimalSeparator": ","}, "totalShippingValue": {"fileColumn": "Frete", "multiplier": 100, "aggregationType": "first", "decimalSeparator": ","}, "totalDiscountsValue": {"fileColumn": "Desconto", "multiplier": 100, "aggregationType": "first", "decimalSeparator": ","}}, "customers": {"city": {"fileColumn": "fixed_city"}, "name": {"fileColumn": "COMBINE(firstname_fixed,lastname_fixed)"}, "email": {"fileColumn": "Email"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Cel Num Final"}, "phoneNumberId2": {}}}