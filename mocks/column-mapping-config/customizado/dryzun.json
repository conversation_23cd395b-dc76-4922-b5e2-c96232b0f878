{"orders": {"value": {"fileColumn": "Venda <PERSON>í<PERSON>", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": ","}, "coupon": {}, "status": {}, "sourceId": {"fileColumn": "CONCATENATE(Cupom,celular,Dt Emissão)"}, "storeName": {}, "salesChannel": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "Dt Emissão"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "Quantidade", "multiplier": 1, "aggregationType": "sum", "decimalSeparator": ","}, "totalShippingValue": {}, "totalDiscountsValue": {}}, "customers": {"city": {}, "name": {"fileColumn": "Cliente"}, "email": {}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "celular"}, "phoneNumberId2": {}}}