{"customers": {"city": {"fileColumn": "Cidade Comprador"}, "name": {"fileColumn": "Nome Comprador"}, "email": {"fileColumn": "E-mail Comprador"}, "state": {"fileColumn": "UF Comprador"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Celular Comprador"}, "phoneNumberId2": {"fileColumn:": "Telefone Comprador"}}, "orders": {"sourceId": {"fileColumn": "Número pedido"}, "value": {"fileColumn": "Total Pedido", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "Data"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Valor <PERSON>", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {"fileColumn": "Valor Desconto Pedido", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 1}, "salesChannel": {}, "storeName": {}}}