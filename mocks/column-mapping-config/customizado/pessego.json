{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do Cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "Numero do Pedido"}, "value": {"fileColumn": "Valor do Pedido", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data do Pedido Date", "dateFormat": "yyyy-MM-dd"}, "status": {"fileColumn": "Status do Pedido"}, "coupon": {"fileColumn": "<PERSON><PERSON>"}, "totalItemsQuantity": {"fileColumn": "Unidades do Item", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalItemsValue": {"fileColumn": "Valor do Produto", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}, "totalShippingValue": {}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {"fileColumn": "Valor do Desconto", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "salesChannel": {}, "storeName": {}}}