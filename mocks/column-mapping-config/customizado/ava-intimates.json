{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Cliente"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON>l"}, "phoneNumberId2": {"fileColumn": "Fone"}}, "orders": {"sourceId": {"fileColumn": "Pedido de venda"}, "value": {"fileColumn": "<PERSON><PERSON>", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd", "fileColumn": "Data"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "<PERSON><PERSON>", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalItemsQuantity": {"fileColumn": "Qtde Vendida", "multiplier": 1, "aggregationType": "sum", "decimalSeparator": "."}, "totalShippingValue": {"fileColumn": "Valor frete", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalDiscountsValue": {"fileColumn": "Valor <PERSON>", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "salesChannel": {}, "storeName": {}}}