{"orders": {"value": {"fileColumn": "Valor Total Pedido", "multiplier": 100, "aggregationType": "first", "decimalSeparator": ","}, "coupon": {}, "status": {"fileColumn": "Situação"}, "sourceId": {"fileColumn": "Código"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy HH:mm:ss", "fileColumn": "Data/Hora", "timeOffset": 3}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {}, "totalDiscountsValue": {}}, "customers": {"city": {}, "name": {"fileColumn": "Cliente"}, "email": {"fileColumn": "E-mail"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Con<PERSON>o Principal"}}}