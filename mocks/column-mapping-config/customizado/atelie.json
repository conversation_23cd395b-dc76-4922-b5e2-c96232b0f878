{"customers": {"city": {}, "name": {"fileColumn": "comprador"}, "email": {"fileColumn": "email"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "phone"}}, "orders": {"sourceId": {"fileColumn": "transactionId"}, "value": {"fileColumn": "price_ing", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd", "fileColumn": "data_pedido"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "num_ing", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}}}