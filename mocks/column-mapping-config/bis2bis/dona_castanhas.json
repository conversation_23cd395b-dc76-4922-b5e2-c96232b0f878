{"customers": {"city": {}, "name": {"fileColumn": "Cliente"}, "email": {"fileColumn": "E-mail"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Phone"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "Pedido #"}, "value": {"fileColumn": "Valor", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data", "dateFormat": "dd/MM/yyyy HH:mm:ss"}, "status": {}, "coupon": {}, "totalItemsQuantity": {}, "totalItemsValue": {}, "totalShippingValue": {}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {}, "storeName": {"fileColumn": "Marketplace"}, "salesChannel": {}}}