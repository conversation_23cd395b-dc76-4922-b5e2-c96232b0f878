{"customers": {"city": {"fileColumn": "Billing City"}, "name": {"fileColumn": "Billing Name"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Billing Province"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Billing Phone"}}, "orders": {"sourceId": {"fileColumn": "Name"}, "value": {"fileColumn": "Total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "Discount Code"}, "status": {"fileColumn": "Financial Status"}, "totalTaxValue": {"fileColumn": "Taxes", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ss XXXX", "fileColumn": "Created at"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Subtotal", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalItemsQuantity": {"fileColumn": "Lineitem quantity", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {"fileColumn": "Shipping", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalDiscountsValue": {"fileColumn": "Discount Amount", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "storeName": {"fileColumn": "<PERSON><PERSON><PERSON>"}, "salesChannel": {}}}