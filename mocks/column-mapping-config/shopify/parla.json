{"customers": {"city": {"fileColumn": "cidade"}, "name": {"fileColumn": "nome"}, "email": {"fileColumn": "email"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "telefone"}}, "orders": {"sourceId": {"fileColumn": "pedido"}, "value": {"fileColumn": "valor total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd", "fileColumn": "data"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {}, "totalDiscountsValue": {}}}