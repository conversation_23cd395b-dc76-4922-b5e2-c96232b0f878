{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Cliente (Nome Fantasia)"}, "email": {"fileColumn": "E-mail do Cliente"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone concatenar"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "CONCATENATE(Numero do Pedido,Nota Fiscal)"}, "value": {"fileColumn": "Total da Nota Fiscal", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data de Emissão (completa)", "dateFormat": "yyyy-MM-dd"}, "status": {"fileColumn": "Situação"}, "coupon": {}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 1}, "totalItemsValue": {"fileColumn": "Total de Mercadoria", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalShippingValue": {"fileColumn": "Frete", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {"fileColumn": "Desconto", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "salesChannel": {"fileColumn": "<PERSON><PERSON><PERSON>"}, "storeName": {}}}