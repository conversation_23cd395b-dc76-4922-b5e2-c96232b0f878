{"customers": {"city": {}, "name": {"fileColumn": "COMBINE(Billing First Name, Billing Last Name)"}, "email": {"fileColumn": "Email da Conta do Cliente"}, "state": {"fileColumn": "Billing State"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Billing Phone"}}, "orders": {"sourceId": {"fileColumn": "Order ID"}, "value": {"fileColumn": "Total do Pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data do Pedido", "dateFormat": "yyyy-MM-dd HH:mm:ss"}, "status": {"fileColumn": "Status do Pedido"}, "coupon": {"fileColumn": "Cupons Usados"}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalItemsValue": {"fileColumn": "Item Total", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalShippingValue": {}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {}}}