{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "COMBINE(Primeiro nome (cobrança),Sobrenome (cobrança))"}, "email": {"fileColumn": "E-mail (cobrança)"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "Número do pedido"}, "value": {"fileColumn": "Total do Pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data do pedido", "dateFormat": "yyyy-MM-dd HH:mm:ss"}, "status": {}, "coupon": {}, "totalItemsQuantity": {"fileColumn": "Total de itens", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 1}, "totalItemsValue": {}, "totalShippingValue": {}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {}, "salesChannel": {}, "storeName": {}}}