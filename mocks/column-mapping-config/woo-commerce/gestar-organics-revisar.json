{"customers": {"city": {"fileColumn": "_billing_city"}, "name": {"fileColumn": "customer_name"}, "email": {"fileColumn": "_billing_email"}, "state": {"fileColumn": "_billing_state"}, "sourceId": {"fileColumn": "Customer User ID"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "_billing_phone"}}, "orders": {"sourceId": {"fileColumn": "Order ID"}, "value": {"fileColumn": "Order Total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Order Date", "dateFormat": "yyyy-MM-dd HH:mm:ss"}, "status": {"fileColumn": "Order Status"}, "coupon": {"fileColumn": "Coupons Used"}, "totalItemsQuantity": {"fileColumn": "total_items_quantity", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalItemsValue": {}, "totalShippingValue": {"fileColumn": "_order_shipping", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {"fileColumn": "Total Discount Amount", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}}}