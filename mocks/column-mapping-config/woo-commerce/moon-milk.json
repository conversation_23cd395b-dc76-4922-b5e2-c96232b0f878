{"customers": {"city": {}, "name": {"fileColumn": "Cliente"}, "email": {"fileColumn": "Email"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "Pedido #"}, "value": {"fileColumn": "<PERSON><PERSON><PERSON> l<PERSON> (formatado)", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data", "dateFormat": "dd/MM/yyyy HH:mm"}, "status": {"fileColumn": "Status"}, "coupon": {}, "totalItemsQuantity": {"fileColumn": "<PERSON>ens vendidos", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 1}, "totalItemsValue": {}, "totalShippingValue": {}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {}}}