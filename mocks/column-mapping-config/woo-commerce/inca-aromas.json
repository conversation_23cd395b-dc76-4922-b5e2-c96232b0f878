{"customers": {"city": {"fileColumn": "shipping_city"}, "name": {"fileColumn": "COMBINE(billing_first_name,billing_last_name)"}, "email": {"fileColumn": "billing_email"}, "state": {"fileColumn": "shipping_state"}, "sourceId": {"fileColumn": "customer_id"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "billing_phone"}}, "orders": {"sourceId": {"fileColumn": "order_id"}, "value": {"fileColumn": "order_total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "coupon_items"}, "status": {"fileColumn": "status"}, "totalTaxValue": {}, "sourceCreatedAt": {"fileColumn": "order_date", "dateFormat": "yyyy-MM-dd HH:mm:ss", "dateLocale": "enUS"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "order_subtotal", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalItemsQuantity": {}, "totalShippingValue": {"fileColumn": "shipping_total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalDiscountsValue": {"fileColumn": "discount_total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}}}