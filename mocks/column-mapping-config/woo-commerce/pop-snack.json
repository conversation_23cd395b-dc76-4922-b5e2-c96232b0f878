{"customers": {"city": {"fileColumn": "City (Shipping)"}, "name": {"fileColumn": "COMBINE(First Name (Billing),Last Name (Billing))"}, "email": {"fileColumn": "E-mail (cobrança)"}, "state": {"fileColumn": "Nome do estado (entrega)"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone (cobrança)"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "Número do pedido"}, "value": {"fileColumn": "Valor total do pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data do pedido", "dateFormat": "yyyy-MM-dd HH:mm:ss"}, "status": {"fileColumn": "Status do pedido"}, "coupon": {"fileColumn": "Código do cupom"}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalItemsValue": {"fileColumn": "Preço atual do produto", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalShippingValue": {}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {"fileColumn": "<PERSON>or de des<PERSON>", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "salesChannel": {}, "storeName": {}}}