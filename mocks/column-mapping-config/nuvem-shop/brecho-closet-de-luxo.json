{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do comprador"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}, "phoneNumberId2": {"fileColumn": "Telefone para a entrega"}}, "orders": {"sourceId": {"fileColumn": "Número do Pedido"}, "value": {"fileColumn": "Total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data", "dateFormat": "dd/MM/yyyy"}, "status": {"fileColumn": "Status do Pedido"}, "coupon": {"fileColumn": "Cupom de Desconto"}, "totalItemsQuantity": {"fileColumn": "Quantidade Comprada", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalItemsValue": {"fileColumn": "Valor do Produto", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalShippingValue": {"fileColumn": "Valor do Frete", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {"fileColumn": "Desconto", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "storeName": {}, "salesChannel": {"fileColumn": "Canal"}}}