{"customers": {"city": {"fileColumn": "entrega_cidade"}, "name": {"fileColumn": "cliente"}, "email": {"fileColumn": "cliente_email"}, "state": {"fileColumn": "entrega_estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "cliente_telefone"}, "cpf": {"fileColumn": "cliente_document"}}, "orders": {"sourceId": {"fileColumn": "numero_pedido"}, "value": {"fileColumn": "total", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "data", "dateFormat": "dd/MM/yyyy HH:mm:ss", "timeOffset": 3}, "status": {"fileColumn": "status"}, "coupon": {}, "totalItemsQuantity": {"fileColumn": "quantidade", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalItemsValue": {"fileColumn": "total_produtos", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "totalShippingValue": {"fileColumn": "total_frete", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {}}}