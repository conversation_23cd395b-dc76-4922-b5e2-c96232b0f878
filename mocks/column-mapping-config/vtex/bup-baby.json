{"customers": {"city": {"fileColumn": "City"}, "name": {"fileColumn": "Receiver Name"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "UF"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Phone"}}, "orders": {"sourceId": {"fileColumn": "Order"}, "value": {"fileColumn": "Total Value", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "Coupon"}, "status": {"fileColumn": "Status"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ssXXX", "fileColumn": "Creation Date"}, "sourceUpdatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ssXXX", "fileColumn": "Last Change Date"}, "totalItemsValue": {"fileColumn": "SKU Total Price", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalItemsQuantity": {"fileColumn": "Quantity_SKU", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {"fileColumn": "Shipping Value", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalDiscountsValue": {"fileColumn": "Discounts Totals", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "storeName": {}, "salesChannel": {"fileColumn": "Origin"}}}