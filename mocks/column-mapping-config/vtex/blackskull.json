{"orders": {"value": {"fileColumn": "Total Value", "multiplier": 100, "aggregationType": "first", "decimalSeparator": "."}, "coupon": {"fileColumn": "Coupon"}, "status": {"fileColumn": "Status"}, "sourceId": {"fileColumn": "Order"}, "storeName": {"fileColumn": "Seller Name"}, "salesChannel": {"fileColumn": "Origin"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ssXXX", "fileColumn": "Creation Date", "timeOffset": 3}, "sourceUpdatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ssXXX", "fileColumn": "Last Change Date", "timeOffset": 3}, "totalItemsValue": {"fileColumn": "SKU Total Price", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": "."}, "totalItemsQuantity": {"fileColumn": "Quantity_SKU", "multiplier": 1, "aggregationType": "sum", "decimalSeparator": "."}, "totalShippingValue": {"fileColumn": "Shipping Value", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": "."}, "totalDiscountsValue": {"fileColumn": "Discounts Totals", "multiplier": 100, "aggregationType": "first", "decimalSeparator": "."}}, "customers": {"city": {"fileColumn": "City"}, "name": {"fileColumn": "COMBINE(Client Name,Client Last Name)"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "UF"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Phone"}}}