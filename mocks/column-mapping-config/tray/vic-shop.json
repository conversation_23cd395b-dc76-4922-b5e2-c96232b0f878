{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do Cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}, "phoneNumberId2": {"fileColumn": "Telefone"}}, "orders": {"sourceId": {"fileColumn": "Pedido"}, "value": {"fileColumn": "Total", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "Cupom desconto"}, "status": {"fileColumn": "Status pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"fileColumn": "data_hora", "dateFormat": "yyyy-MM-dd HH:mm:ss", "timeOffset": 3}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Subtotal produtos", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "totalItemsQuantity": {}, "totalShippingValue": {"fileColumn": "Frete valor", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "totalDiscountsValue": {"fileColumn": "Desconto", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "storeName": {}, "salesChannel": {"fileColumn": "Canal de venda"}}}