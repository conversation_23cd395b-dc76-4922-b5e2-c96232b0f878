{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do cliente"}, "email": {"fileColumn": "E-mail do cliente"}, "state": {"fileColumn": "UF"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "Número"}, "value": {"fileColumn": "Valor total da venda", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {}, "status": {"fileColumn": "Situação da venda"}, "totalTaxValue": {}, "sourceCreatedAt": {"fileColumn": "Data da venda", "dateFormat": "dd/MM/yyyy"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Preço de lista", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalItemsQuantity": {}, "totalShippingValue": {}, "totalDiscountsValue": {}, "storeName": {}, "salesChannel": {"fileColumn": "E-commerce"}}}