{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do Cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {"fileColumn": "Código cliente"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}, "phoneNumberId2": {"fileColumn": "Telefone"}}, "orders": {"sourceId": {"fileColumn": "PEDIDO"}, "value": {"fileColumn": "PREÇO VENDA", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"fileColumn": "Data", "dateFormat": "yyyy-MM-dd"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "QUANTIDADE COMPRA", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}, "salesChannel": {}, "storeName": {}}}