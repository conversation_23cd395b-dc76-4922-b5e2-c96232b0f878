{"customers": {"city": {"fileColumn": "Cidade de cobrança"}, "name": {"fileColumn": "Nome do cliente que realizou o pedido"}, "email": {"fileColumn": "Email do Cliente"}, "state": {"fileColumn": "Estado de cobrança"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone 1"}, "phoneNumberId2": {"fileColumn": "Telefone 2"}}, "orders": {"sourceId": {"fileColumn": "Id do Pedido"}, "value": {"fileColumn": "Valor Total do pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "COMBINE(Data da criação do pedido,Hora da criação do pedido)", "dateFormat": "dd/MM/yyyy HH:mm:ss.SSSSSS", "timeOffset": 3}, "status": {"fileColumn": "Status Resumido"}, "coupon": {"fileColumn": "Cupom"}, "totalItemsQuantity": {"fileColumn": "Quantidade de itens do pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 1}, "totalItemsValue": {"fileColumn": "Valor total de itens do pedido sem descontos", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalShippingValue": {"fileColumn": "Custo Frete Final", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {"fileColumn": "Valor total do desconto do pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "storeName": {}, "salesChannel": {}}}