{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}, "phoneNumberId2": {"fileColumn": "Telefone"}, "cpf": {"fileColumn": "CPF/CNPJ"}}, "orders": {"sourceId": {"fileColumn": "Número do pedido"}, "value": {"fileColumn": "Valor do pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "Nome do Cupom"}, "status": {"fileColumn": "Status do pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "Data da última modificação"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Sub-total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}, "storeName": {}, "salesChannel": {}}}