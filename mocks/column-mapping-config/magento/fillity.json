{"customers": {"city": {"fileColumn": "cidade"}, "name": {"fileColumn": "nome_cliente"}, "email": {"fileColumn": "email"}, "state": {"fileColumn": "estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "telefone"}}, "orders": {"sourceId": {"fileColumn": "numero_pedido"}, "value": {"fileColumn": "valor_pedido", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "cupom_usado"}, "status": {"fileColumn": "status_pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ss", "fileColumn": "data_pedido"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "valor_produto", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalItemsQuantity": {"fileColumn": "unidades_item", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}, "storeName": {}, "salesChannel": {}}}