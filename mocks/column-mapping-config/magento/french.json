{"customers": {"city": {"fileColumn": "Cidade do contato"}, "name": {"fileColumn": "Nome do contato"}, "email": {"fileColumn": "E-mail do contato"}, "state": {"fileColumn": "UF do Contato"}, "sourceId": {"fileColumn": "ID contato"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}}, "orders": {"sourceId": {"fileColumn": "Numero da Ordem"}, "value": {"fileColumn": "Preço de Venda", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {"fileColumn": "Situação"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd", "fileColumn": "Data"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {"fileColumn": "Frete item", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "totalDiscountsValue": {"fileColumn": "Desconto item", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "storeName": {}, "salesChannel": {}}}