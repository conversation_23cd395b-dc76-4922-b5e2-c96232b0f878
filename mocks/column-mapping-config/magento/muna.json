{"customers": {"city": {}, "name": {"fileColumn": "COMBINE(Firstname,Lastname)"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "Região"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}}, "orders": {"sourceId": {"fileColumn": "Pedido #"}, "value": {"fileColumn": "Total da Venda", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {}, "status": {"fileColumn": "Status"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ss", "fileColumn": "Comprado Em"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "Quantidade de itens", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {"fileColumn": "Desconto", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "storeName": {}, "salesChannel": {}}}