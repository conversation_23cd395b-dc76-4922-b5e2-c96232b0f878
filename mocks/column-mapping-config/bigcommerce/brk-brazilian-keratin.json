{"customers": {"city": {"fileColumn": "Bairro de cobrança"}, "name": {"fileColumn": "Nome do cliente"}, "email": {"fileColumn": "Email do cliente"}, "state": {"fileColumn": "Estado de cobrança"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}, "phoneNumberId2": {"fileColumn": "Telefone 2"}}, "orders": {"sourceId": {"fileColumn": "ID do pedido"}, "value": {"fileColumn": "Total do pedido (com impostos)", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "coupon": {"fileColumn": "Cupom"}, "status": {"fileColumn": "Status do pedido"}, "totalTaxValue": {"fileColumn": "Total de impostos", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd HH:mm:ss", "fileColumn": "Data e hora"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Subtotal (com impostos)", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalItemsQuantity": {"fileColumn": "Quantidade total", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {"fileColumn": "<PERSON><PERSON><PERSON> de envio (com impostos)", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalDiscountsValue": {}, "storeName": {}, "salesChannel": {"fileColumn": "Nome do canal"}}}