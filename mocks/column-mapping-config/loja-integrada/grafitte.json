{"customers": {"city": {"fileColumn": "end-entrega-cidade"}, "name": {"fileColumn": "cliente-nome"}, "email": {"fileColumn": "cliente-email"}, "state": {"fileColumn": "end-entrega-estado"}, "sourceId": {"fileColumn": "cliente-id"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "end-entrega-telefone-celular"}, "phoneNumberId2": {"fileColumn": "end-entrega-telefone-principal"}}, "orders": {"sourceId": {"fileColumn": "pedido-numero"}, "value": {"fileColumn": "pedido-valor-total", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "pedido-data-criacao", "dateFormat": "yyyy-MM-dd HH:mm:ss.SSSSSSX"}, "status": {"fileColumn": "pedido-situacao"}, "coupon": {}, "totalItemsQuantity": {}, "totalItemsValue": {"fileColumn": "pedido-valor-subtotal", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalShippingValue": {"fileColumn": "pedido-valor-envio", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {"fileColumn": "pedido-valor-desconto", "decimalSeparator": ".", "aggregationType": "first", "multiplier": 100}}}