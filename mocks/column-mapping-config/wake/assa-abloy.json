{
  "customers": {
    "city": { "fileColumn": "" },
    "name": { "fileColumn": "Cliente - Nome" },
    "email": { "fileColumn": "Cliente - Email" },
    "state": { "fileColumn": "" },
    "sourceId": {},
    "birthDate": {},
    "phoneNumberId": { "fileColumn": "Cliente - Telefone Celular" },
    "phoneNumberId2": { "fileColumn": "Cliente - Telefone Celular 2" }
  },
  "orders": {
    "sourceId": { "fileColumn": "Pedido Id" },
    "value": {
      "fileColumn": "Valor do Pedido",
      "decimalSeparator": ",",
      "aggregationType": "first",
      "multiplier": 100
    },
    "sourceCreatedAt": {
      "fileColumn": "Data",
      "dateFormat": "dd/MM/yyyy HH:mm"
    },
    "status": { "fileColumn": "Situacao - Nome" },
    "coupon": { "fileColumn": "Cupom" },
    "totalItemsQuantity": {
      "fileColumn": "Produto - Quantidade",
      "decimalSeparator": ",",
      "aggregationType": "sum",
      "multiplier": 1
    },
    "totalItemsValue": {
      "fileColumn": "Produto - Valor (R$)",
      "decimalSeparator": ",",
      "aggregationType": "sum",
      "multiplier": 100
    },
    "totalShippingValue": {},
    "totalTaxValue": {},
    "sourceUpdatedAt": {},
    "totalDiscountsValue": {},
    "salesChannel": {},
    "storeName": {}
  }
}


{"orders": {"value": {"fileColumn": "Valor do Pedido", "multiplier": 100, "aggregationType": "first", "decimalSeparator": "."},
"coupon": {"fileColumn": "Cupom"},
"status": {"fileColumn": "Situacao - Nome"},
"sourceId": {"fileColumn": "Pedido Id"},
"storeName": {},
"salesChannel": {},
"totalTaxValue": {},
"sourceCreatedAt": {"dateFormat": "yyyy-MM-dd", "fileColumn": "Data"},
"sourceUpdatedAt": {},
"totalItemsValue": {"fileColumn": "Produto - Valor (R$)", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": "."},
"totalItemsQuantity": {"fileColumn": "Produto - Quantidade", "multiplier": 1, "aggregationType": "sum", "decimalSeparator": "."},
"totalShippingValue": {},
"totalDiscountsValue": {}},
"customers": {"city": {"fileColumn": ""},
"name": {"fileColumn": "Cliente - Nome"},
"email": {"fileColumn": "Cliente - Email"},
"state": {"fileColumn": ""},
"sourceId": {},
"birthDate": {},
"phoneNumberId": {"fileColumn": "Cliente - Telefone Celular"},
"phoneNumberId2": {"fileColumn": "Cliente - Telefone Celular 2"}}}
