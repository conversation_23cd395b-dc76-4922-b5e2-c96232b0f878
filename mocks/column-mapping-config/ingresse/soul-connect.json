{"customers": {"city": {}, "name": {"fileColumn": "Nome"}, "email": {"fileColumn": "E-mail"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}}, "orders": {"sourceId": {"fileColumn": "ID da Transação"}, "value": {"fileColumn": "Valor Final", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "coupon": {"fileColumn": "Code"}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "yyyy-MM-dd", "fileColumn": "Data da compra"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "Qtd", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}, "storeName": {}, "salesChannel": {"fileColumn": "Nome do Cupom"}}}