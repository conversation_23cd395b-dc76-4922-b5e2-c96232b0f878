{"customers": {"city": {}, "name": {"fileColumn": "Nome"}, "email": {"fileColumn": "E-mail"}, "state": {}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "<PERSON><PERSON><PERSON>"}}, "orders": {"sourceId": {"fileColumn": "ID da Transação"}, "value": {"fileColumn": "Valor", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "MM/dd/yyyy", "fileColumn": "<PERSON> da Compra"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {}}}