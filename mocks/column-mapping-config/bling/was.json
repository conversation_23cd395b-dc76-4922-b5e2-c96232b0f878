{"customers": {"city": {"fileColumn": "Município"}, "name": {"fileColumn": "Nome do contato"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "Estado"}, "sourceId": {"fileColumn": "ID contato"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "ID"}, "value": {"fileColumn": "Preço Total", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}, "coupon": {}, "status": {"fileColumn": "Situação da NFe"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "Data"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Valor unitário", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalShippingValue": {}, "totalDiscountsValue": {"fileColumn": "Desconto pedido", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}}}