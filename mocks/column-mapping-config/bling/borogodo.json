{"customers": {"city": {"fileColumn": "Cidade Comprador"}, "name": {"fileColumn": "Nome Comprador"}, "email": {"fileColumn": "E-mail Comprador"}, "state": {"fileColumn": "UF Comprador"}, "sourceId": {"fileColumn": "Número Comprador"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Celular Comprador"}, "phoneNumberId2": {"fileColumn": "Telefone Comprador"}}, "orders": {"sourceId": {"fileColumn": "Número pedido"}, "value": {"fileColumn": "Total Pedido", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data", "dateFormat": "dd/MM/yyyy"}, "status": {}, "coupon": {}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalItemsValue": {"fileColumn": "Valor Total", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}, "totalShippingValue": {"fileColumn": "Valor Frete Pedido", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {"fileColumn": "Valor Desconto Pedido", "decimalSeparator": ",", "aggregationType": "first", "multiplier": 100}}}