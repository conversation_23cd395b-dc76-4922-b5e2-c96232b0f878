{"orders": {"value": {"fileColumn": "Total Pedido", "multiplier": 100, "aggregationType": "first", "decimalSeparator": ","}, "coupon": {}, "status": {}, "sourceId": {"fileColumn": "Número pedido"}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy", "fileColumn": "Data"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Valor Total", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": ","}, "totalItemsQuantity": {"fileColumn": "Quantidade", "multiplier": 1, "aggregationType": "sum", "decimalSeparator": ","}, "totalShippingValue": {"fileColumn": "Valor Frete Pedido", "multiplier": 100, "aggregationType": "first", "decimalSeparator": ","}, "totalDiscountsValue": {"fileColumn": "Valor Desconto Pedido", "multiplier": 100, "aggregationType": "first", "decimalSeparator": ","}}, "customers": {"city": {"fileColumn": "Cidade Comprador"}, "name": {"fileColumn": "Nome Comprador"}, "email": {"fileColumn": "E-mail Comprador"}, "state": {"fileColumn": "UF Comprador"}, "sourceId": {"fileColumn": "Número Comprador"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Celular Comprador"}, "phoneNumberId2": {"fileColumn": "Telefone Comprador"}}}