{"customers": {"city": {"fileColumn": "Município"}, "name": {"fileColumn": "Nome do contato"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "Estado"}, "sourceId": {"fileColumn": "ID contato"}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "N° do Pedido"}, "value": {"fileColumn": "Preço Total", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data", "dateFormat": "dd/MM/yyyy"}, "status": {"fileColumn": "Situação"}, "coupon": {}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalItemsValue": {}, "totalShippingValue": {"fileColumn": "Frete proporcional", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {"fileColumn": "Desconto proporcional", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}}}