{"customers": {"city": {"fileColumn": "Município"}, "name": {"fileColumn": "Nome"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "UF"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "Número", "formatFunction": "intToString"}, "value": {"fileColumn": "Valor total", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data de emissão", "dateFormat": "dd/MM/yyyy"}, "status": {}, "coupon": {}, "totalItemsQuantity": {"fileColumn": "Quantidade", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalItemsValue": {}, "totalShippingValue": {"fileColumn": "Frete", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {"fileColumn": "Desconto", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 100}, "storeName": {"fileColumn": "<PERSON><PERSON>"}, "salesChannel": {"fileColumn": "Canal de Venda"}}}