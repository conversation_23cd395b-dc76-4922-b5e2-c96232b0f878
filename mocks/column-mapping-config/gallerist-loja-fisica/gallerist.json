{"customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Cliente"}, "email": {"fileColumn": "E-mail"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}, "phoneNumberId2": {}}, "orders": {"sourceId": {"fileColumn": "Docto."}, "value": {"fileColumn": "Valor", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}, "sourceCreatedAt": {"fileColumn": "Data", "dateFormat": "M/d/yy"}, "status": {}, "coupon": {}, "totalItemsQuantity": {"fileColumn": "Qtde", "decimalSeparator": ",", "aggregationType": "sum", "multiplier": 1}, "totalItemsValue": {}, "totalShippingValue": {}, "totalTaxValue": {}, "sourceUpdatedAt": {}, "totalDiscountsValue": {"fileColumn": "Desconto total", "decimalSeparator": ".", "aggregationType": "sum", "multiplier": 100}}}