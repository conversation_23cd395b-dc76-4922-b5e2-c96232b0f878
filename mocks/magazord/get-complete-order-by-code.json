{"status": "success", "data": {"id": 476202, "id_loja": 1, "lojaNome": "Loja Principessa", "codigo": "0012406916387", "codigoSecundario": null, "dataHora": "2024-06-14 16:07:38-03", "valorProduto": "328.000000", "valorFrete": "0.00", "valorDesconto": "0.00", "valorAcrescimo": "0.00", "valorPresente": "0.00", "valorTotal": "328.00", "origem": 1, "pedidoIp": "************", "pessoaId": 42627, "pessoaNome": "Maria Aparecida", "pessoaCpfCnpj": "355.838.548-98", "pessoaEmail": "<EMAIL>", "pessoaTipo": 1, "pessoaDataNascimento": "1987-10-12", "pessoaSexo": 2, "formaPagamentoId": 3, "formaPagamentoNome": "Cartão - Visa", "condicaoPagamentoId": 3, "condicaoPagamentoNome": "2 Vezes", "condicaoPagamentoParcelas": 2, "marketplaceId": null, "marketplaceNome": null, "codigoMarketplace": null, "dataMarketplace": null, "pedidoSituacao": 7, "pedidoSituacaoEtapa": 4, "pedidoSituacaoTipo": 1, "pedidoSituacaoDescricao": "Transporte", "pedidoSituacaoDescricaoDetalhada": "Transporte", "dataPreVenda": null, "nomeDestinatario": "Soraia Nascimento", "logradouro": "Rua Dona Rosa <PERSON>ó<PERSON>", "numero": "283", "bairro": "<PERSON><PERSON>", "complemento": null, "cidadeNome": "São Paulo", "estadoSigla": "SP", "paisNome": null, "cep": "02964060", "pedidoTrackingSource": "adwords", "pedidoTrackingUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "pedidoTrackingParams": {"gad_source": "1", "gclid": "cjwkcajw1k-zbhbieiwawecof9yk3gdp3gyxjnd84-muiyfzaetuzqcx1pkzmy26xcrwqczewbu-ihocaj8qavd-bwe", "utm_referer": "https-www-google-com", "lc": "utm", "utm_source": "adwords", "start_buy": "1718364130", "pe": "", "dispositivo": 1}, "cupomCodigo": "16OX-G3S4-9IB5", "cupomValorDesconto": "198.00", "cupomTipoDesconto": 4, "lojaDoMarketplaceId": null, "lojaDoMarketplaceNome": null, "lojaMarketplaceId": null, "lojaMarketplaceNome": null, "formaRecebimentoId": 44, "formaRecebimentoNome": "MagaPay", "gatewayPagamentoId": 12, "tipoCadastroPM": null, "valorPersonalizacao": null, "lojaUrlImagem": "https://1259028l.ha.azioncdn.net", "lojaUrl": "https://www.lojaprincipessa.com.br", "creditoUtilizado": "198.00", "cashbackUtilizado": null, "pedidoAnaliseRisco": [{"id": 209967, "score": "11", "status": 2, "gateway": "<PERSON><PERSON><PERSON>", "operador": 3}], "valorTotalFinal": 130, "arrayPedidoRastreio": [{"id": 477019, "valorFrete": "0.00", "valorFreteTransportadora": "8.60", "diasUteis": 6, "codigoRastreio": "MZ202406366455LP", "link": "https://rastreio.transporte.magazord.com.br/MZ202406366455LP", "dataLimiteEntregaCliente": "2024-06-24", "dataLimitePostagem": "2024-06-19 16:07:47-03", "situacao": 1, "transportadoraServicoId": 68, "transportadoraServicoOpcoes": null, "transportadoraServicoTipo": 1, "transportadoraServicoDescricao": "<PERSON><PERSON><PERSON>", "transportadoraTextoEntrega": null, "transportadoraAgenciaNome": "<PERSON><PERSON><PERSON>", "transportadoraAgenciaCodigoServico": "LGG", "transportadoraId": 48, "transportadoraNome": "<PERSON><PERSON><PERSON>", "transportadoraTipo": 34, "transportadoraCodigoRastreioInterno": false, "pedidoSituacao": 7, "pedidoSituacaoEtapa": 4, "pedidoSituacaoTipo": 1, "pedidoSituacaoDescricao": "Transporte", "pedidoSituacaoDescricaoDetalhada": "Transporte", "pedidoLojistaCodigo": null, "lojistaNome": null, "pedidoItem": [{"id": 1, "produtoDerivacaoId": 35030, "descricao": "<PERSON><PERSON> em Tweed de Malha Preto/Off White Madisson - 40", "quantidade": 1, "valorUnitario": 328, "valorDesconto": 0, "valorAcrescimo": 0, "valorItem": 328, "valorFrete": 0, "valorTransportadora": 8.6, "dataPreVenda": null, "isPresente": false, "pedidoInfo": null, "deposito": 1, "depositoNome": "Depósito Deluxe", "produtoDerivacaoCodigo": "1100026440", "codigoPai": "11000264", "pedidoTerceiroSituacao": null, "produtoTitulo": "<PERSON><PERSON> em Tweed de Malha Preto/Off White Madisson", "produtoId": 4900, "produtoNome": "<PERSON><PERSON> em Tweed de Malha Preto/Off White Madisson", "produtoDerivacaoCodigoKit": null, "anuncioMarketplaceId": null, "ean": "7899955681173", "produtoDerivacaoNome": "40", "marcaNome": "Principessa", "midiaPath": "img/2024/05/produto/80158/", "midiaName": "foto-frente-de-perto-saia-madisson-salto.jpg", "midiaAlt": "foto frente de perto saia madisson salto", "midiaTipoFile": "1", "categoriaArvore": "Principessa - Roupas,Saias", "categoria_id": 5, "categoria": "<PERSON><PERSON>", "linkProduto": "saia-midi-de-tweed-madisson", "pedidoItemListaPresente": null}], "pedidoNotaFiscal": [{"id": 803375, "numero": 431698, "serieLegal": "1", "dataEmissao": "2024-06-19T14:45:46-03:00", "chave": "42240617896169000255550010004316981008033757", "valorTotal": 328, "situacao": 3, "tipo": 1, "cfopCodigo": 6108, "situacaoDescricao": "Autorizada", "pedidoRastreioId": 477019}], "pedidoRastreioHistorico": [{"id": 3428440, "dataHora": "2024-06-20T19:13:27-03:00", "situacao": 1, "descricao": "(A etiqueta de envio já foi criada e o pacote está pronto para a coleta.) - ", "nomeUsuario": "Sispostag", "codigoRastreio": "MZ202406366455LP", "dataHoraTransportadora": "2024-06-19T14:45:49-03:00"}, {"id": 3428441, "dataHora": "2024-06-20T19:13:27-03:00", "situacao": 1, "descricao": "(O pacote foi confirmado em uma base da Loggi e vai ser preparado para transferência.) - ", "nomeUsuario": "Sispostag", "codigoRastreio": "MZ202406366455LP", "dataHoraTransportadora": "2024-06-20T16:21:13-03:00"}, {"id": 3426324, "dataHora": "2024-06-19T19:09:30-03:00", "situacao": 1, "descricao": "(A etiqueta de envio já foi criada e o pacote está pronto para a coleta.) - ", "nomeUsuario": "Sispostag", "codigoRastreio": "MZ202406366455LP", "dataHoraTransportadora": "2024-06-19T14:45:49-03:00"}]}], "boletos": [], "pedidoPagamentoPix": null, "linkPagamento": null, "linkAvaliacao": "https://www.lojaprincipessa.com.br/avaliacao?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.mwdbpyuNNxqs2DRRrYKUyYkQhqPRqf4-JNB8aArRdqc", "pedidoPagamentoAme": null, "pedidoHistorico": [{"id": 3357448, "dataHora": "2024-06-14 16:07:41-03", "pedidoSituacao": 1, "pedidoSituacaoDescricao": "<PERSON><PERSON><PERSON><PERSON>", "pedidoSituacaoDescricaoDetalhada": "<PERSON><PERSON><PERSON><PERSON>", "tipo": 1, "etapa": 2, "situacao": 1, "nomeUsuario": "Maria Aparecida", "tipoUsuario": "E-Commerce", "pedidoRastreioId": "477019"}, {"id": 3357449, "dataHora": "2024-06-14 16:07:45-03", "pedidoSituacao": 3, "pedidoSituacaoDescricao": "<PERSON><PERSON><PERSON><PERSON>", "pedidoSituacaoDescricaoDetalhada": "<PERSON> anális<PERSON>", "tipo": 4, "etapa": 2, "situacao": 11, "nomeUsuario": "Maria Aparecida", "tipoUsuario": "E-Commerce", "pedidoRastreioId": "477019"}, {"id": 3357450, "dataHora": "2024-06-14 16:07:46-03", "pedidoSituacao": 12, "pedidoSituacaoDescricao": "<PERSON><PERSON><PERSON><PERSON>", "pedidoSituacaoDescricaoDetalhada": "<PERSON><PERSON><PERSON>", "tipo": 1, "etapa": 2, "situacao": 12, "nomeUsuario": "Maria Aparecida", "tipoUsuario": "E-Commerce", "pedidoRastreioId": "477019"}, {"id": 3357451, "dataHora": "2024-06-14 16:07:47-03", "pedidoSituacao": 4, "pedidoSituacaoDescricao": "Crédito e Cadastro Aprovados", "pedidoSituacaoDescricaoDetalhada": "<PERSON><PERSON><PERSON>", "tipo": 1, "etapa": 3, "situacao": 3, "nomeUsuario": "Maria Aparecida", "tipoUsuario": "E-Commerce", "pedidoRastreioId": "477019"}, {"id": 3360288, "dataHora": "2024-06-17 16:25:27-03", "pedidoSituacao": 5, "pedidoSituacaoDescricao": "Em separação", "pedidoSituacaoDescricaoDetalhada": "Aprovado e Integrado", "tipo": 1, "etapa": 3, "situacao": 4, "nomeUsuario": "<PERSON><PERSON>", "tipoUsuario": "Administrativo", "pedidoRastreioId": "477019"}, {"id": 3367024, "dataHora": "2024-06-19 14:45:46-03", "pedidoSituacao": 6, "pedidoSituacaoDescricao": "Nota Fiscal Emitida", "pedidoSituacaoDescricaoDetalhada": "Nota Fiscal Emitida", "tipo": 1, "etapa": 3, "situacao": 5, "nomeUsuario": "<PERSON><PERSON><PERSON>", "tipoUsuario": "Administrativo", "pedidoRastreioId": "477019"}, {"id": 3367025, "dataHora": "2024-06-19 14:45:47-03", "pedidoSituacao": 7, "pedidoSituacaoDescricao": "Transporte", "pedidoSituacaoDescricaoDetalhada": "Transporte", "tipo": 1, "etapa": 4, "situacao": 6, "nomeUsuario": "<PERSON><PERSON><PERSON>", "tipoUsuario": "Administrativo", "pedidoRastreioId": "477019"}], "pessoaContato": [{"tipo": 1, "contato": "(11) 99007-5355"}], "pedidoPromocoes": {"totalDesconto": 0, "totalPromocoesUtilizadas": 0, "promocoesUtilizadas": []}, "tags": [], "bloqueios": []}}