{"body": {"checkouts": [{"buyer_accepts_marketing": false, "cart_token": "c1-dbe34ff101dfbdcf197394897f116051", "closed_at": null, "completed_at": null, "created_at": "2023-08-28T16:49:39-04:00", "currency": "BRL", "presentment_currency": "BRL", "customer_locale": "en", "device_id": null, "email": "<EMAIL>", "gateway": null, "id": 36844637028628, "landing_site": "/password", "location_id": null, "note": null, "note_attributes": [], "phone": null, "referring_site": "", "source_identifier": null, "source_url": null, "taxes_included": false, "token": "957550bc32ba926ab0f6ad8913cb76d3", "total_weight": 4536, "updated_at": "2023-08-28T17:05:38-04:00", "user_id": null, "customer": {"id": 7197742629140, "email": "<EMAIL>", "accepts_marketing": false, "created_at": "2023-08-28T16:49:39-04:00", "updated_at": "2023-08-28T17:05:38-04:00", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "orders_count": 0, "state": "disabled", "total_spent": "0.00", "last_order_id": null, "note": null, "verified_email": true, "multipass_identifier": null, "tax_exempt": false, "tags": "", "last_order_name": null, "currency": "BRL", "phone": null, "accepts_marketing_updated_at": "2023-08-28T16:49:39-04:00", "marketing_opt_in_level": null, "tax_exemptions": [], "email_marketing_consent": {"state": "not_subscribed", "opt_in_level": "single_opt_in", "consent_updated_at": null}, "sms_marketing_consent": null, "admin_graphql_api_id": "gid://shopify/Customer/7197742629140", "default_address": {"id": 9385167683860, "customer_id": 7197742629140, "first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": null, "address1": "<PERSON><PERSON>", "address2": "824", "city": "Curitiba, Paraná, Brasil", "province": "Paraná", "country": "Brazil", "zip": "80230-100", "phone": "(41) 99579-4954", "name": "<PERSON><PERSON>", "province_code": "PR", "country_code": "BR", "country_name": "Brazil", "default": true}}, "source": null, "source_name": "web", "name": "#36844637028628", "discount_codes": [], "total_discounts": "0.00", "total_line_items_price": "699.95", "total_price": "818.94", "total_tax": "118.99", "subtotal_price": "699.95", "total_duties": null, "abandoned_checkout_url": "https://loja-do-revi.myshopify.com/80539320596/checkouts/ac/c1-dbe34ff101dfbdcf197394897f116051/recover?key=28862e4c128b0ae9c732c123ccb8d3ba", "shipping_lines": [{"code": "Standard", "price": "0.00", "source": "shopify", "title": "Standard", "presentment_title": "Standard", "validation_context": null, "phone": null, "markup": "0.00", "carrier_identifier": null, "carrier_service_id": null, "api_client_id": 580111, "delivery_option_group": {"token": "07c01f970ccb8c926ffc4756c70b03bf", "type": "one_time_purchase"}, "delivery_expectation_range": null, "delivery_expectation_type": null, "tax_lines": []}], "tax_lines": [{"position": 1, "price": "118.99", "rate": 0.17, "title": "VAT", "source": null, "compare_at": "118.99", "zone": null, "channel_liable": false, "identifier": null}], "shipping_address": {"first_name": "<PERSON><PERSON>", "address1": "<PERSON><PERSON>", "phone": "(41) 99579-4954", "city": "Curitiba, Paraná, Brasil", "zip": "80230-100", "province": "PR", "country": "BR", "last_name": "<PERSON><PERSON><PERSON>", "address2": "824", "company": null, "latitude": null, "longitude": null, "name": "<PERSON><PERSON>", "country_code": "BR", "province_code": "PR"}, "billing_address": {"first_name": "<PERSON><PERSON>", "address1": "<PERSON><PERSON>", "phone": "(41) 99579-4954", "city": "Curitiba, Paraná, Brasil", "zip": "80230-100", "province": "PR", "country": "BR", "last_name": "<PERSON><PERSON><PERSON>", "address2": "824", "company": null, "latitude": null, "longitude": null, "name": "<PERSON><PERSON>", "country_code": "BR", "province_code": "PR"}, "line_items": [{"key": null, "destination_location_id": null, "fulfillment_service": "manual", "gift_card": false, "grams": 4536, "origin_location_id": null, "presentment_title": "The Complete Snowboard", "presentment_variant_title": "Ice", "product_id": 8487104676116, "properties": [], "quantity": 1, "requires_shipping": true, "sku": "", "taxable": true, "title": "The Complete Snowboard", "variant_id": **************, "variant_title": "Ice", "variant_price": "699.95", "vendor": "Snowboard Vendor", "unit_price_measurement": {"measured_type": "", "quantity_value": 0, "quantity_unit": "", "reference_value": 0, "reference_unit": ""}, "rank": null, "line_price": "699.95", "price": "699.95", "applied_discounts": [], "tax_lines": [{"position": 0, "price": "118.99", "rate": 0.17, "title": "VAT", "source": null, "compare_at": "118.99", "zone": null, "channel_liable": false, "identifier": null}]}]}]}, "headers": {"Alt-Svc": ["h3=\":443\"; ma=86400"], "Cf-Cache-Status": ["DYNAMIC"], "Cf-Ray": ["8062584e2ed500ad-GRU"], "Connection": ["close"], "Content-Encoding": ["gzip"], "Content-Security-Policy": ["default-src 'self' data: blob: 'unsafe-inline' 'unsafe-eval' https://* shopify-pos://*; block-all-mixed-content; child-src 'self' https://* shopify-pos://*; connect-src 'self' wss://* https://*; frame-ancestors 'none'; img-src 'self' data: blob: https:; script-src https://cdn.shopify.com https://cdn.shopifycdn.net https://checkout.shopifycs.com https://api.stripe.com https://mpsnare.iesnare.com https://appcenter.intuit.com https://www.paypal.com https://js.braintreegateway.com https://c.paypal.com https://maps.googleapis.com https://www.google-analytics.com https://v.shopify.com 'self' 'unsafe-inline' 'unsafe-eval'; upgrade-insecure-requests; report-uri /csp-report?source%5Baction%5D=index&source%5Bapp%5D=Shopify&source%5Bcontroller%5D=admin%2Fabandoned_checkouts&source%5Bsection%5D=admin_api&source%5Buuid%5D=e8251ebb-65de-463c-975a-d9adeb03f1dc"], "Content-Type": ["application/json; charset=utf-8"], "Date": ["Wed, 13 Sep 2023 18:10:05 GMT"], "Nel": ["{\"success_fraction\":0.01,\"report_to\":\"cf-nel\",\"max_age\":604800}"], "Referrer-Policy": ["origin-when-cross-origin"], "Report-To": ["{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v3?s=GlmTI6wpshCl6m3%2Fd22W%2BS1Wrx1QOLg7m%2FmwBi%2F6Wqu1QaBxpYE3S7MWTl0JImeLNctjpRirKHPEdedwKKKvdybehM%2FTAvmjnbO%2FiWux0BbAtBZyBGq4zqTrpLCwHQRhznQgX8P3tBa3u9Sf\"}],\"group\":\"cf-nel\",\"max_age\":604800}"], "Server": ["cloudflare"], "Server-Timing": ["processing;dur=82, cfRequestDuration;dur=233.000040"], "Strict-Transport-Security": ["max-age=7889238"], "Transfer-Encoding": ["chunked"], "Vary": ["Accept-Encoding"], "X-Content-Type-Options": ["nosniff"], "X-Dc": ["gcp-southamerica-east1,gcp-us-east1,gcp-us-east1"], "X-Download-Options": ["noopen"], "X-Frame-Options": ["DENY"], "X-Permitted-Cross-Domain-Policies": ["none"], "X-Request-Id": ["e8251ebb-65de-463c-975a-d9adeb03f1dc"], "X-Shardid": ["275"], "X-Shopid": ["80539320596"], "X-Shopify-Api-Version": ["2022-10"], "X-Shopify-Api-Version-Warning": ["https://shopify.dev/concepts/about-apis/versioning"], "X-Shopify-Stage": ["production"], "X-Sorting-Hat-Podid": ["275"], "X-Sorting-Hat-Shopid": ["80539320596"], "X-Stats-Apiclientid": ["54174515201"], "X-Stats-Apipermissionid": ["************"], "X-Stats-Userid": [""], "X-Xss-Protection": ["1; mode=block; report=/xss-report?source%5Baction%5D=index&source%5Bapp%5D=Shopify&source%5Bcontroller%5D=admin%2Fabandoned_checkouts&source%5Bsection%5D=admin_api&source%5Buuid%5D=e8251ebb-65de-463c-975a-d9adeb03f1dc"]}}