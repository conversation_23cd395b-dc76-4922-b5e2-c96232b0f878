{
  "body": {
    "data": {
      "orders": {
        "edges": [
          {
            "node": {
              "id": "gid://shopify/Order/5430105243924",
              "name": "#1001",
              "createdAt": "2023-08-04T15:32:39Z",
              "displayFinancialStatus": "PAID",
              "displayFulfillmentStatus": "UNFULFILLED",
              "customer": null,
              "lineItems": {
                "edges": [
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530033428",
                      "title": "The Minimal Snowboard",
                      "quantity": 1,
                      "variant": {
                        "id": "gid://shopify/ProductVariant/45949476503828",
                        "title": "Default Title",
                        "price": "885.95"
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "node": {
              "id": "gid://shopify/Order/5430105276692",
              "name": "#1002",
              "createdAt": "2023-08-04T15:32:39Z",
              "displayFinancialStatus": "PARTIALLY_PAID",
              "displayFulfillmentStatus": "UNFULFILLED",
              "customer": null,
              "lineItems": {
                "edges": [
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530066196",
                      "title": "The Minimal Snowboard",
                      "quantity": 10,
                      "variant": {
                        "id": "gid://shopify/ProductVariant/45949476503828",
                        "title": "Default Title",
                        "price": "885.95"
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "node": {
              "id": "gid://shopify/Order/5430105309460",
              "name": "#1003",
              "createdAt": "2023-08-04T15:32:40Z",
              "displayFinancialStatus": "PAID",
              "displayFulfillmentStatus": "UNFULFILLED",
              "customer": {
                "id": "gid://shopify/Customer/7147002003732",
                "displayName": "Russell Winfield",
                "email": "<EMAIL>"
              },
              "lineItems": {
                "edges": [
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530098964",
                      "title": "Custom Snowboard",
                      "quantity": 1,
                      "variant": null
                    }
                  }
                ]
              }
            }
          },
          {
            "node": {
              "id": "gid://shopify/Order/5430105374996",
              "name": "#1004",
              "createdAt": "2023-08-04T15:32:40Z",
              "displayFinancialStatus": "PAID",
              "displayFulfillmentStatus": "UNFULFILLED",
              "customer": null,
              "lineItems": {
                "edges": [
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530131732",
                      "title": "The Complete Snowboard",
                      "quantity": 4,
                      "variant": {
                        "id": "gid://shopify/ProductVariant/45949476733204",
                        "title": "Ice",
                        "price": "699.95"
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "node": {
              "id": "gid://shopify/Order/5430105440532",
              "name": "#1005",
              "createdAt": "2023-08-04T15:32:41Z",
              "displayFinancialStatus": "PAID",
              "displayFulfillmentStatus": "UNFULFILLED",
              "customer": {
                "id": "gid://shopify/Customer/7147001938196",
                "displayName": "Karine Ruby",
                "email": "<EMAIL>"
              },
              "lineItems": {
                "edges": [
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530230036",
                      "title": "The Multi-location Snowboard",
                      "quantity": 1,
                      "variant": {
                        "id": "gid://shopify/ProductVariant/45949476798740",
                        "title": "Default Title",
                        "price": "729.95"
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "node": {
              "id": "gid://shopify/Order/5430105473300",
              "name": "#1006",
              "createdAt": "2023-08-04T15:32:41Z",
              "displayFinancialStatus": "PAID",
              "displayFulfillmentStatus": "UNFULFILLED",
              "customer": {
                "id": "gid://shopify/Customer/7147002003732",
                "displayName": "Russell Winfield",
                "email": "<EMAIL>"
              },
              "lineItems": {
                "edges": [
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530262804",
                      "title": "The Complete Snowboard",
                      "quantity": 2,
                      "variant": {
                        "id": "gid://shopify/ProductVariant/45949476765972",
                        "title": "Dawn",
                        "price": "699.95"
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "node": {
              "id": "gid://shopify/Order/5430105506068",
              "name": "#1007",
              "createdAt": "2023-08-04T15:32:41Z",
              "displayFinancialStatus": "PAID",
              "displayFulfillmentStatus": "ON_HOLD",
              "customer": {
                "id": "gid://shopify/Customer/7147001938196",
                "displayName": "Karine Ruby",
                "email": "<EMAIL>"
              },
              "lineItems": {
                "edges": [
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530295572",
                      "title": "The Complete Snowboard",
                      "quantity": 1,
                      "variant": {
                        "id": "gid://shopify/ProductVariant/45949476929812",
                        "title": "Sunset",
                        "price": "699.95"
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "node": {
              "id": "gid://shopify/Order/5430105637140",
              "name": "#1010",
              "createdAt": "2023-08-04T15:32:42Z",
              "displayFinancialStatus": "PAID",
              "displayFulfillmentStatus": "UNFULFILLED",
              "customer": {
                "id": "gid://shopify/Customer/7147002003732",
                "displayName": "Russell Winfield",
                "email": "<EMAIL>"
              },
              "lineItems": {
                "edges": [
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530426644",
                      "title": "The Complete Snowboard",
                      "quantity": 1,
                      "variant": {
                        "id": "gid://shopify/ProductVariant/45949476929812",
                        "title": "Sunset",
                        "price": "699.95"
                      }
                    }
                  },
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530459412",
                      "title": "The 3p Fulfilled Snowboard",
                      "quantity": 1,
                      "variant": {
                        "id": "gid://shopify/ProductVariant/45949477159188",
                        "title": "Default Title",
                        "price": "2629.95"
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "node": {
              "id": "gid://shopify/Order/5430105571604",
              "name": "#1008",
              "createdAt": "2023-08-04T15:32:41Z",
              "displayFinancialStatus": "PAID",
              "displayFulfillmentStatus": "UNFULFILLED",
              "customer": {
                "id": "gid://shopify/Customer/7147001938196",
                "displayName": "Karine Ruby",
                "email": "<EMAIL>"
              },
              "lineItems": {
                "edges": [
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530328340",
                      "title": "The Complete Snowboard",
                      "quantity": 4,
                      "variant": {
                        "id": "gid://shopify/ProductVariant/45949476733204",
                        "title": "Ice",
                        "price": "699.95"
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "node": {
              "id": "gid://shopify/Order/5430105604372",
              "name": "#1009",
              "createdAt": "2023-08-04T15:32:41Z",
              "displayFinancialStatus": "PAID",
              "displayFulfillmentStatus": "UNFULFILLED",
              "customer": {
                "id": "gid://shopify/Customer/7147002003732",
                "displayName": "Russell Winfield",
                "email": "<EMAIL>"
              },
              "lineItems": {
                "edges": [
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530361108",
                      "title": "The Complete Snowboard",
                      "quantity": 1,
                      "variant": {
                        "id": "gid://shopify/ProductVariant/45949476831508",
                        "title": "Powder",
                        "price": "699.95"
                      }
                    }
                  },
                  {
                    "node": {
                      "id": "gid://shopify/LineItem/14031530393876",
                      "title": "The Complete Snowboard",
                      "quantity": 2,
                      "variant": {
                        "id": "gid://shopify/ProductVariant/45949476897044",
                        "title": "Electric",
                        "price": "699.95"
                      }
                    }
                  }
                ]
              }
            }
          }
        ]
      }
    },
    "extensions": {
      "cost": {
        "requestedQueryCost": 242,
        "actualQueryCost": 66,
        "throttleStatus": {
          "maximumAvailable": 1000,
          "currentlyAvailable": 934,
          "restoreRate": 50
        }
      }
    }
  },
  "headers": {
    "Alt-Svc": ["h3=\":443\"; ma=86400"],
    "Cf-Cache-Status": ["DYNAMIC"],
    "Cf-Ray": ["7ff05c140b23a4b6-GRU"],
    "Connection": ["close"],
    "Content-Encoding": ["gzip"],
    "Content-Language": ["en"],
    "Content-Security-Policy": [
      "default-src 'self' data: blob: 'unsafe-inline' 'unsafe-eval' https://* shopify-pos://*; block-all-mixed-content; child-src 'self' https://* shopify-pos://*; connect-src 'self' wss://* https://*; frame-ancestors 'none'; img-src 'self' data: blob: https:; script-src https://cdn.shopify.com https://cdn.shopifycdn.net https://checkout.shopifycs.com https://api.stripe.com https://mpsnare.iesnare.com https://appcenter.intuit.com https://www.paypal.com https://js.braintreegateway.com https://c.paypal.com https://maps.googleapis.com https://www.google-analytics.com https://v.shopify.com 'self' 'unsafe-inline' 'unsafe-eval'; upgrade-insecure-requests; report-uri /csp-report?source%5Baction%5D=query&source%5Bapp%5D=Shopify&source%5Bcontroller%5D=admin%2Fgraphql&source%5Bsection%5D=admin_api&source%5Buuid%5D=739e7c72-ccc0-4805-8bd7-865293acc463"
    ],
    "Content-Type": ["application/json; charset=utf-8"],
    "Date": ["Wed, 30 Aug 2023 22:09:43 GMT"],
    "Nel": [
      "{\"success_fraction\":0.01,\"report_to\":\"cf-nel\",\"max_age\":604800}"
    ],
    "Referrer-Policy": ["origin-when-cross-origin"],
    "Report-To": [
      "{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v3?s=f8Ctkng4C16fIGOsmkNO8MHEnZPdKQEYnViV9RtvBAb%2FYEfrRSUjAvbbAz21ibeabetwIZZFoxHn1XJuA28sZaJlohx7GHJdy8UwEz1475G%2FFJiJoOgH0la6K4mxIHZdYyU2Pei73lN2J6fO\"}],\"group\":\"cf-nel\",\"max_age\":604800}"
    ],
    "Server": ["cloudflare"],
    "Server-Timing": [
      "processing;dur=168, graphql;desc=\"admin/query/other\", cfRequestDuration;dur=317.999840"
    ],
    "Strict-Transport-Security": ["max-age=7889238"],
    "Transfer-Encoding": ["chunked"],
    "Vary": ["Accept-Encoding"],
    "X-Content-Type-Options": ["nosniff"],
    "X-Dc": ["gcp-southamerica-east1,gcp-us-east1,gcp-us-east1"],
    "X-Download-Options": ["noopen"],
    "X-Frame-Options": ["DENY"],
    "X-Permitted-Cross-Domain-Policies": ["none"],
    "X-Request-Id": ["739e7c72-ccc0-4805-8bd7-865293acc463"],
    "X-Shardid": ["275"],
    "X-Shopid": ["80539320596"],
    "X-Shopify-Api-Version": ["2023-07"],
    "X-Shopify-Stage": ["production"],
    "X-Sorting-Hat-Podid": ["275"],
    "X-Sorting-Hat-Shopid": ["80539320596"],
    "X-Stats-Apiclientid": ["54174515201"],
    "X-Stats-Apipermissionid": ["************"],
    "X-Stats-Userid": [""],
    "X-Xss-Protection": [
      "1; mode=block; report=/xss-report?source%5Baction%5D=query&source%5Bapp%5D=Shopify&source%5Bcontroller%5D=admin%2Fgraphql&source%5Bsection%5D=admin_api&source%5Buuid%5D=739e7c72-ccc0-4805-8bd7-865293acc463"
    ]
  }
}

// const orders = await client.query({
//   data: `{
//     orders(first: 10) {
//       edges {
//         node {
//           id
//           name
//           createdAt
//           displayFinancialStatus
//           displayFulfillmentStatus
//           customer {
//             id
//             displayName
//             email
//           }
//           lineItems(first: 10) {
//             edges {
//               node {
//                 id
//                 title
//                 quantity
//                 variant {
//                   id
//                   title
//                   price
//                 }
//               }
//             }
//           }
//         }
//       }
//     }
//   }`,
// });