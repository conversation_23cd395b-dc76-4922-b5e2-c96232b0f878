{"body": {"orders": [{"id": 5430105637140, "created_at": "2023-08-04T11:32:42-04:00", "discount_codes": [], "financial_status": "paid", "total_discounts": "0.00", "total_line_items_price": "3329.90", "total_price": "3329.90", "total_shipping_price_set": {"shop_money": {"amount": "0.00", "currency_code": "BRL"}, "presentment_money": {"amount": "0.00", "currency_code": "BRL"}}, "total_tax": "0.00", "updated_at": "2023-08-04T11:32:43-04:00", "user_id": null, "customer": {"id": 7147002003732, "email": "<EMAIL>", "accepts_marketing": false, "created_at": "2023-08-04T11:32:37-04:00", "updated_at": "2023-08-04T11:32:42-04:00", "first_name": "<PERSON>", "last_name": "<PERSON>", "state": "disabled", "note": "This customer is created with most available fields", "verified_email": true, "multipass_identifier": null, "tax_exempt": false, "phone": "+16135550135", "email_marketing_consent": {"state": "not_subscribed", "opt_in_level": "single_opt_in", "consent_updated_at": null}, "sms_marketing_consent": {"state": "not_subscribed", "opt_in_level": "unknown", "consent_updated_at": null, "consent_collected_from": "OTHER"}, "tags": "VIP", "currency": "BRL", "accepts_marketing_updated_at": "2023-08-04T11:32:37-04:00", "marketing_opt_in_level": null, "tax_exemptions": [], "admin_graphql_api_id": "gid://shopify/Customer/7147002003732", "default_address": {"id": 9340209889556, "customer_id": 7147002003732, "first_name": "<PERSON>", "last_name": "<PERSON>", "company": "Company Name", "address1": "105 Victoria St", "address2": null, "city": "Toronto", "province": null, "country": "Canada", "zip": "M5C1N7", "phone": null, "name": "<PERSON>", "province_code": null, "country_code": "CA", "country_name": "Canada", "default": true}}, "line_items": [{"id": 14031530426644, "admin_graphql_api_id": "gid://shopify/LineItem/14031530426644", "fulfillable_quantity": 1, "fulfillment_service": "manual", "fulfillment_status": null, "gift_card": false, "grams": 4536, "name": "The Complete Snowboard - Sunset", "price": "699.95", "price_set": {"shop_money": {"amount": "699.95", "currency_code": "BRL"}, "presentment_money": {"amount": "699.95", "currency_code": "BRL"}}, "product_exists": true, "product_id": 8487104676116, "properties": [], "quantity": 1, "requires_shipping": true, "sku": "", "taxable": true, "title": "The Complete Snowboard", "total_discount": "0.00", "total_discount_set": {"shop_money": {"amount": "0.00", "currency_code": "BRL"}, "presentment_money": {"amount": "0.00", "currency_code": "BRL"}}, "variant_id": **************, "variant_inventory_management": "shopify", "variant_title": "Sunset", "vendor": "Snowboard Vendor", "tax_lines": [], "duties": [], "discount_allocations": []}, {"id": 14031530459412, "admin_graphql_api_id": "gid://shopify/LineItem/14031530459412", "fulfillable_quantity": 1, "fulfillment_service": "snow-city-warehouse", "fulfillment_status": null, "gift_card": false, "grams": 0, "name": "The 3p Fulfilled Snowboard", "price": "2629.95", "price_set": {"shop_money": {"amount": "2629.95", "currency_code": "BRL"}, "presentment_money": {"amount": "2629.95", "currency_code": "BRL"}}, "product_exists": true, "product_id": 8487104872724, "properties": [], "quantity": 1, "requires_shipping": true, "sku": "sku-hosted-1", "taxable": true, "title": "The 3p Fulfilled Snowboard", "total_discount": "0.00", "total_discount_set": {"shop_money": {"amount": "0.00", "currency_code": "BRL"}, "presentment_money": {"amount": "0.00", "currency_code": "BRL"}}, "variant_id": **************, "variant_inventory_management": "shopify", "variant_title": null, "vendor": "Loja do Revi", "tax_lines": [], "duties": [], "discount_allocations": []}], "shipping_address": {"first_name": "<PERSON>", "address1": "105 Victoria St", "phone": null, "city": "Toronto", "zip": "M5C1N7", "province": null, "country": "Canada", "last_name": "<PERSON>", "address2": null, "company": "Company Name", "latitude": 43.6522608, "longitude": -79.3776862, "name": "<PERSON>", "country_code": "CA", "province_code": null}}, {"id": 5430105604372, "created_at": "2023-08-04T11:32:41-04:00", "discount_codes": [{"code": "", "amount": "313.47", "type": "percentage"}], "financial_status": "paid", "total_discounts": "323.47", "total_line_items_price": "2099.85", "total_price": "1806.38", "total_shipping_price_set": {"shop_money": {"amount": "30.00", "currency_code": "BRL"}, "presentment_money": {"amount": "30.00", "currency_code": "BRL"}}, "total_tax": "0.00", "updated_at": "2023-08-04T11:32:42-04:00", "user_id": null, "customer": {"id": 7147002003732, "email": "<EMAIL>", "accepts_marketing": false, "created_at": "2023-08-04T11:32:37-04:00", "updated_at": "2023-08-04T11:32:42-04:00", "first_name": "<PERSON>", "last_name": "<PERSON>", "state": "disabled", "note": "This customer is created with most available fields", "verified_email": true, "multipass_identifier": null, "tax_exempt": false, "phone": "+16135550135", "email_marketing_consent": {"state": "not_subscribed", "opt_in_level": "single_opt_in", "consent_updated_at": null}, "sms_marketing_consent": {"state": "not_subscribed", "opt_in_level": "unknown", "consent_updated_at": null, "consent_collected_from": "OTHER"}, "tags": "VIP", "currency": "BRL", "accepts_marketing_updated_at": "2023-08-04T11:32:37-04:00", "marketing_opt_in_level": null, "tax_exemptions": [], "admin_graphql_api_id": "gid://shopify/Customer/7147002003732", "default_address": {"id": 9340209889556, "customer_id": 7147002003732, "first_name": "<PERSON>", "last_name": "<PERSON>", "company": "Company Name", "address1": "105 Victoria St", "address2": null, "city": "Toronto", "province": null, "country": "Canada", "zip": "M5C1N7", "phone": null, "name": "<PERSON>", "province_code": null, "country_code": "CA", "country_name": "Canada", "default": true}}, "line_items": [{"id": 14031530361108, "admin_graphql_api_id": "gid://shopify/LineItem/14031530361108", "fulfillable_quantity": 1, "fulfillment_service": "manual", "fulfillment_status": null, "gift_card": false, "grams": 4536, "name": "The Complete Snowboard - Powder", "price": "699.95", "price_set": {"shop_money": {"amount": "699.95", "currency_code": "BRL"}, "presentment_money": {"amount": "699.95", "currency_code": "BRL"}}, "product_exists": true, "product_id": 8487104676116, "properties": [], "quantity": 1, "requires_shipping": true, "sku": "", "taxable": true, "title": "The Complete Snowboard", "total_discount": "10.00", "total_discount_set": {"shop_money": {"amount": "10.00", "currency_code": "BRL"}, "presentment_money": {"amount": "10.00", "currency_code": "BRL"}}, "variant_id": **************, "variant_inventory_management": "shopify", "variant_title": "<PERSON><PERSON><PERSON>", "vendor": "Snowboard Vendor", "tax_lines": [], "duties": [], "discount_allocations": [{"amount": "10.00", "amount_set": {"shop_money": {"amount": "10.00", "currency_code": "BRL"}, "presentment_money": {"amount": "10.00", "currency_code": "BRL"}}, "discount_application_index": 0}, {"amount": "103.50", "amount_set": {"shop_money": {"amount": "103.50", "currency_code": "BRL"}, "presentment_money": {"amount": "103.50", "currency_code": "BRL"}}, "discount_application_index": 1}]}, {"id": 14031530393876, "admin_graphql_api_id": "gid://shopify/LineItem/14031530393876", "fulfillable_quantity": 2, "fulfillment_service": "manual", "fulfillment_status": null, "gift_card": false, "grams": 4536, "name": "The Complete Snowboard - Electric", "price": "699.95", "price_set": {"shop_money": {"amount": "699.95", "currency_code": "BRL"}, "presentment_money": {"amount": "699.95", "currency_code": "BRL"}}, "product_exists": true, "product_id": 8487104676116, "properties": [], "quantity": 2, "requires_shipping": true, "sku": "", "taxable": true, "title": "The Complete Snowboard", "total_discount": "0.00", "total_discount_set": {"shop_money": {"amount": "0.00", "currency_code": "BRL"}, "presentment_money": {"amount": "0.00", "currency_code": "BRL"}}, "variant_id": **************, "variant_inventory_management": "shopify", "variant_title": "Electric", "vendor": "Snowboard Vendor", "tax_lines": [], "duties": [], "discount_allocations": [{"amount": "209.97", "amount_set": {"shop_money": {"amount": "209.97", "currency_code": "BRL"}, "presentment_money": {"amount": "209.97", "currency_code": "BRL"}}, "discount_application_index": 1}]}], "shipping_address": {"first_name": "<PERSON>", "address1": "105 Victoria St", "phone": null, "city": "Toronto", "zip": "M5C1N7", "province": null, "country": "Canada", "last_name": "<PERSON>", "address2": null, "company": "Company Name", "latitude": 43.6522608, "longitude": -79.3776862, "name": "<PERSON>", "country_code": "CA", "province_code": null}}]}, "headers": {"Alt-Svc": ["h3=\":443\"; ma=86400"], "Cf-Cache-Status": ["DYNAMIC"], "Cf-Ray": ["7ffff0bdaf3402fb-GRU"], "Connection": ["close"], "Content-Encoding": ["gzip"], "Content-Security-Policy": ["default-src 'self' data: blob: 'unsafe-inline' 'unsafe-eval' https://* shopify-pos://*; block-all-mixed-content; child-src 'self' https://* shopify-pos://*; connect-src 'self' wss://* https://*; frame-ancestors 'none'; img-src 'self' data: blob: https:; script-src https://cdn.shopify.com https://cdn.shopifycdn.net https://checkout.shopifycs.com https://api.stripe.com https://mpsnare.iesnare.com https://appcenter.intuit.com https://www.paypal.com https://js.braintreegateway.com https://c.paypal.com https://maps.googleapis.com https://www.google-analytics.com https://v.shopify.com 'self' 'unsafe-inline' 'unsafe-eval'; upgrade-insecure-requests; report-uri /csp-report?source%5Baction%5D=index&source%5Bapp%5D=Shopify&source%5Bcontroller%5D=admin%2Forders&source%5Bsection%5D=admin_api&source%5Buuid%5D=413eecae-74c3-4280-8048-261b91fd0f12"], "Content-Type": ["application/json; charset=utf-8"], "Date": ["Fri, 01 Sep 2023 19:32:38 GMT"], "Http_x_shopify_shop_api_call_limit": ["1/40"], "Link": ["<https://loja-do-revi.myshopify.com/admin/api/2023-07/orders.json?limit=2&fields=customer%2Cid%2Cfinancial_status%2Cupdated_at%2Ccreated_at%2Ctotal_discounts%2Ctotal_line_items_price%2Ctotal_shipping_price_set%2Ctotal_tax%2Ctotal_price%2Cline_items%2Cdiscount_codes%2Cuser_id%2Cshipping_address&page_info=eyJsYXN0X2lkIjo1NDMwMTA1NjA0MzcyLCJsYXN0X3ZhbHVlIjoiMjAyMy0wOC0wNCAxNTozMjo0MS44NDEzMTYiLCJkaXJlY3Rpb24iOiJuZXh0In0>; rel=\"next\""], "Nel": ["{\"success_fraction\":0.01,\"report_to\":\"cf-nel\",\"max_age\":604800}"], "Referrer-Policy": ["origin-when-cross-origin"], "Report-To": ["{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v3?s=FetEcC7dQ5TW%2F%2B7%2BAXUTQWxjL4DQ3nDV1ovgIEB4As2pgfGXWZn2B8aTMUksulwQNDY%2BTKOiRaCjlC7v%2FdPKqr5MjKEMnerIGCWpFEQy5zzMbNlu6884J29G1P2o0zEqskpP7RTm%2FQ%2BetmYs\"}],\"group\":\"cf-nel\",\"max_age\":604800}"], "Server": ["cloudflare"], "Server-Timing": ["processing;dur=107, cfRequestDuration;dur=254.999876"], "Strict-Transport-Security": ["max-age=7889238"], "Transfer-Encoding": ["chunked"], "Vary": ["Accept-Encoding"], "X-Content-Type-Options": ["nosniff"], "X-Dc": ["gcp-southamerica-east1,gcp-us-east1,gcp-us-east1"], "X-Download-Options": ["noopen"], "X-Frame-Options": ["DENY"], "X-Permitted-Cross-Domain-Policies": ["none"], "X-Request-Id": ["413eecae-74c3-4280-8048-261b91fd0f12"], "X-Shardid": ["275"], "X-Shopid": ["80539320596"], "X-Shopify-Api-Deprecated-Reason": ["https://shopify.dev/changelog/property-deprecations-in-the-admin-api-order-and-lineitem-resource"], "X-Shopify-Api-Version": ["2023-07"], "X-Shopify-Shop-Api-Call-Limit": ["1/40"], "X-Shopify-Stage": ["production"], "X-Sorting-Hat-Podid": ["275"], "X-Sorting-Hat-Shopid": ["80539320596"], "X-Stats-Apiclientid": ["54174515201"], "X-Stats-Apipermissionid": ["************"], "X-Stats-Userid": [""], "X-Xss-Protection": ["1; mode=block; report=/xss-report?source%5Baction%5D=index&source%5Bapp%5D=Shopify&source%5Bcontroller%5D=admin%2Forders&source%5Bsection%5D=admin_api&source%5Buuid%5D=413eecae-74c3-4280-8048-261b91fd0f12"]}, "pageInfo": {"limit": "2", "fields": ["customer", "id", "financial_status", "updated_at", "created_at", "total_discounts", "total_line_items_price", "total_shipping_price_set", "total_tax", "total_price", "line_items", "discount_codes", "user_id", "shipping_address"], "nextPageUrl": "https://loja-do-revi.myshopify.com/admin/api/2023-07/orders.json?limit=2&fields=customer%2Cid%2Cfinancial_status%2Cupdated_at%2Ccreated_at%2Ctotal_discounts%2Ctotal_line_items_price%2Ctotal_shipping_price_set%2Ctotal_tax%2Ctotal_price%2Cline_items%2Cdiscount_codes%2Cuser_id%2Cshipping_address&page_info=eyJsYXN0X2lkIjo1NDMwMTA1NjA0MzcyLCJsYXN0X3ZhbHVlIjoiMjAyMy0wOC0wNCAxNTozMjo0MS44NDEzMTYiLCJkaXJlY3Rpb24iOiJuZXh0In0", "nextPage": {"path": "orders", "query": {"limit": "2", "fields": "customer,id,financial_status,updated_at,created_at,total_discounts,total_line_items_price,total_shipping_price_set,total_tax,total_price,line_items,discount_codes,user_id,shipping_address", "page_info": "eyJsYXN0X2lkIjo1NDMwMTA1NjA0MzcyLCJsYXN0X3ZhbHVlIjoiMjAyMy0wOC0wNCAxNTozMjo0MS44NDEzMTYiLCJkaXJlY3Rpb24iOiJuZXh0In0"}}}}