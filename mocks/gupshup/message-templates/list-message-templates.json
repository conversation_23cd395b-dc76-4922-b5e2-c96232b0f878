{"status": "success", "templates": [{"appId": "54343fd8-cc65-43a8-bee3-a19eda45f989", "category": "MARKETING", "containerMeta": "{\"appId\":\"54343fd8-cc65-43a8-bee3-a19eda45f989\",\"data\":\"<PERSON><PERSON><PERSON> {{1}}, esta é uma mensagem de teste contendo PDF.\",\"buttons\":[{\"type\":\"QUICK_REPLY\",\"text\":\"<PERSON><PERSON><PERSON><PERSON>\"}],\"sampleText\":\"<PERSON><PERSON><PERSON> [<PERSON><PERSON>], esta é uma mensagem de teste contendo PDF.\",\"sampleMedia\":\"64deae77-010c-448d-ac77-1fe5ae9e7990\",\"enableSample\":true,\"mediaId\":\"64deae77-010c-448d-ac77-1fe5ae9e7990\"}", "createdOn": 1682134508627, "data": "O<PERSON><PERSON> {{1}}, esta é uma mensagem de teste contendo PDF. | [Recebido]", "elementName": "revi_template_test_pdf", "externalId": "908279243757115", "id": "15902d62-c8a7-4507-bdf5-717eeabe37a9", "internalCategory": 0, "internalType": 0, "languageCode": "pt_BR", "languagePolicy": "deterministic", "meta": "{\"example\":\"<PERSON><PERSON><PERSON> [<PERSON><PERSON>], esta é uma mensagem de teste contendo PDF.\",\"mediaId\":\"64deae77-010c-448d-ac77-1fe5ae9e7990\"}", "modifiedOn": 1682134791275, "namespace": "e641b5d4_de05_43dc_b1ce_59d196126458", "priority": 1, "quality": "UNKNOWN", "retry": 0, "stage": "NONE", "status": "APPROVED", "templateType": "DOCUMENT", "vertical": "teste", "wabaId": "119202984450997"}]}