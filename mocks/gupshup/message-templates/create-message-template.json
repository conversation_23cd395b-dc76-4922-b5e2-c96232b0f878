{"status": "success", "template": {"appId": "54343fd8-cc65-43a8-bee3-a19eda45f989", "category": "MARKETING", "containerMeta": "{\"appId\":\"54343fd8-cc65-43a8-bee3-a19eda45f989\",\"data\":\"<PERSON><PERSON><PERSON> {{1}}. Este template foi criado pela API\",\"buttons\":[],\"header\":\"template_api_text\",\"footer\":\"test\",\"sampleText\":\"<PERSON><PERSON><PERSON> [<PERSON><PERSON>]. Este template foi criado pela API\",\"sampleHeader\":\"template_api_text\",\"enableSample\":true,\"editTemplate\":false,\"allowTemplateCategoryChange\":false,\"addSecurityRecommendation\":false}", "createdOn": 1685466262610, "data": "template_api_text\nOlá {{1}}. Este template foi criado pela API\ntest", "elementName": "test", "id": "64d0dd0f-d0d0-4377-bb5d-1f78331de2c8", "languageCode": "pt_BR", "languagePolicy": "deterministic", "meta": "{\"example\":\"<PERSON><PERSON><PERSON> [<PERSON><PERSON>]. Este template foi criado pela API\"}", "modifiedOn": 1685466262610, "namespace": "e641b5d4_de05_43dc_b1ce_59d196126458", "priority": 1, "retry": 0, "stage": "NONE", "status": "PENDING", "templateType": "TEXT", "vertical": "test", "wabaId": "119202984450997"}}