{"token": "user-id-abc", "tipo": "pedido_venda", "id": 753077, "id_externo": null, "numero": 7, "valor_desconto": 0, "valor_envio": 0, "valor_subtotal": 169.41, "valor_total": 169.41, "utm_campaign": null, "peso_real": 0.6, "cliente_obs": null, "id_anymarket": null, "data_criacao": "2023-03-23T22:48:05.582Z", "data_modificacao": "2023-03-23T22:48:39.301Z", "data_expiracao": "2023-03-29T22:48:05.665Z", "cliente": {"id": 56827792, "email": "<EMAIL>", "nome": "teste aa dddd", "telefone_celular": "31338893773", "telefone_comercial": null, "telefone_principal": "31938873772", "situacao": "pendente", "data_nascimento": "1990-12-12", "sexo": "m", "data_criacao": "2023-03-22T20:03:57.838283-03:00", "data_modificacao": "2023-03-22T20:03:57.838298-03:00"}, "cupom_desconto": {"id": null, "codigo": null, "tipo": null}, "endereco_entrega": {"id": 63198643, "tipo": "PF", "ie": null, "cnpj": null, "cpf": "12345678901", "rg": null, "nome": "teste aa dddd", "razao_social": null, "endereco": "Rua <PERSON>", "numero": "22", "complemento": "complemento", "referencia": "referencia", "bairro": "Jardim Europa", "cidade": "Sao Paulo", "estado": "SP", "cep": "01450000", "pais": "Brasil"}, "endereco_pagamento": {"id": 63198644, "tipo": null, "ie": null, "cnpj": null, "rg": null, "nome": "teste aa dddd", "razao_social": null, "endereco": "Rua dos Caquizeiros", "numero": "22", "complemento": "complemento", "referencia": "referencia", "bairro": "<PERSON><PERSON><PERSON>", "cidade": "Sinop", "estado": "MT", "pais": "Brasil"}, "envios": [{"id": 75213630, "objeto": null, "prazo": 1, "valor": null, "data_criacao": "2023-03-23T19:48:05.600305-03:00", "data_modificacao": "2023-03-23T19:48:05.600325-03:00", "forma_envio": {"id": 12015, "codigo": "retirar_pessoalmente", "nome": "<PERSON><PERSON><PERSON>"}}], "pagamentos": [{"id": 75213751, "identificador_id": null, "authorization_code": null, "bandeira": null, "mensagem_gateway": null, "codigo_retorno_gateway": null, "transacao_id": null, "valor": 169.41, "valor_pago": 169.41, "data_criacao": "2023-03-23T19:48:05.594093-03:00", "data_modificacao": "2023-03-23T19:48:20.088216-03:00", "forma_pagamento": {"id": 13, "codigo": "entrega", "nome": "Pagamento na entrega"}}], "situacao": {"id": 3, "codigo": "pagamento_em_analise", "nome": "Pagamento em análise", "aprovado": false, "cancelado": false, "final": false, "notificar_comprador": true, "padrao": false, "situacao_alterada": true}, "itens": [{"linha": 1, "id": 170235106, "produto_id": 209615743, "produto_id_pai": 209615737, "sku": "912321123", "nome": "Produto teste abc", "tipo": "atributo_opcao", "quantidade": 1, "preco_cheio": 169.41, "preco_custo": null, "preco_venda": 169.41, "preco_subtotal": 169.41}]}