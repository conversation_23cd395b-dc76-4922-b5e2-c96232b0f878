{"orders": {"value": {"fileColumn": "Valor do Pedido", "multiplier": 100, "aggregationType": "first", "decimalSeparator": ","}, "coupon": {}, "status": {"fileColumn": "Situacao - Nome"}, "sourceId": {"fileColumn": "Pedido Id"}, "storeName": {}, "salesChannel": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yyyy HH:mm", "fileColumn": "Data"}, "sourceUpdatedAt": {}, "totalItemsValue": {}, "totalItemsQuantity": {}, "totalShippingValue": {}, "totalDiscountsValue": {}}, "customers": {"city": {"fileColumn": "Endereco - Cidade"}, "name": {"fileColumn": "Cliente - Nome"}, "email": {"fileColumn": "Cliente - Email"}, "state": {"fileColumn": "Endereco - UF"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Cliente - Telefone Celular"}, "phoneNumberId2": {"fileColumn": "Cliente - Telefone Celular 2"}}}