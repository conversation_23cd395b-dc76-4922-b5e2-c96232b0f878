{"orders": {"value": {"fileColumn": "Valor do Pedido", "multiplier": 100, "aggregationType": "first", "decimalSeparator": "."}, "coupon": {}, "status": {"fileColumn": "Status do Pedido"}, "sourceId": {"fileColumn": "Numero do Pedido"}, "storeName": {}, "salesChannel": {}, "totalTaxValue": {}, "sourceCreatedAt": {"dateFormat": "dd/MM/yy", "fileColumn": "Data do Pedido Date"}, "sourceUpdatedAt": {}, "totalItemsValue": {"fileColumn": "Valor do Produto", "multiplier": 100, "aggregationType": "sum", "decimalSeparator": "."}, "totalItemsQuantity": {}, "totalShippingValue": {}, "totalDiscountsValue": {}}, "customers": {"city": {"fileColumn": "Cidade"}, "name": {"fileColumn": "Nome do Cliente"}, "email": {"fileColumn": "Email"}, "state": {"fileColumn": "Estado"}, "sourceId": {}, "birthDate": {}, "phoneNumberId": {"fileColumn": "Telefone"}, "cpf": {"fileColumn": "CPF"}}}