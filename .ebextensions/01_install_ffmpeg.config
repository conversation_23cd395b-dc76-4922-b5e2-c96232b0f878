commands:
  01-wget:
    command: "wget -O /tmp/ffmpeg.tar.xz https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz"

  02-mkdir:
    command: "mkdir -p /opt/ffmpeg"

  03-tar:
    command: "tar -xvf /tmp/ffmpeg.tar.xz -C /opt/ffmpeg --strip-components=1"

  04-list:
    command: "ls -la /opt/ffmpeg"

  05-ln-ffmpeg:
    command: "if [ -f /opt/ffmpeg/ffmpeg ]; then ln -sf /opt/ffmpeg/ffmpeg /usr/bin/ffmpeg; fi"

  06-ln-ffprobe:
    command: "if [ -f /opt/ffmpeg/ffprobe ]; then ln -sf /opt/ffmpeg/ffprobe /usr/bin/ffprobe; fi"
