# Database
DATABASE_URL=
DATABASE_READ_REPLICA_URL=

# Prisma log levels, eg: query
PRISMA_LOG_LEVEL=

# AWS Services
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=

AWS_S3_BUCKET=
AWS_SQS_GUPSHUP_EVENTS_QUEUE_URL=
AWS_SQS_WHATSAPP_MESSAGE_QUEUE_URL=
GUPSHUP_USE_AWS_SQS_QUEUE=true|false

# AWS Event Bridge api key for task dispatcher
AWS_EVENT_BRIDGE_API_KEY=

#Emails
AWS_SES_DEFAULT_CONFIGURATION_SET=
AWS_SQS_EMAIL_EVENTS_QUEUE_URL=
AWS_SQS_SG_EMAIL_EVENTS_QUEUE_URL=
SENDGRID_API_KEY=
EMAIL_OPT_OUT_URL=
SENDGRID_SUBUSER_PASSWORD_SUFFIX=

RABBIT_MQ_EMAILS_QUEUE_1=
RABBIT_MQ_EMAILS_QUEUE_2=
RABBIT_MQ_EMAILS_QUEUE_3=
SENDGRID_WEBHOOK_URL=

# Gupshup
GUPSHUP_API_KEY=
GUPSHUP_PARTNER_EMAIL=
GUPSHUP_PARTNER_PASSWORD=

# RabbitMQ
RABBIT_MQ_USER=
RABBIT_MQ_PASSWORD=
RABBIT_MQ_HOST=

RABBIT_MQ_GUPSHUP_EVENTS_QUEUE=

RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_1=
RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_2=
RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_3=
RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_4=
RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_5=
RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_6=

RABBIT_MQ_SMS_MESSAGES_QUEUE_1=
RABBIT_MQ_SMS_MESSAGES_QUEUE_2=
RABBIT_MQ_SMS_MESSAGES_QUEUE_3=

RABBIT_MQ_TIME_DELAY_EVENTS_QUEUE=

RABBIT_MQ_ORDER_EVENTS_QUEUE=

RABBIT_MQ_COUPON_EVENTS_QUEUE=

RABBIT_MQ_PRODUCT_EVENTS_QUEUE=

# Redis
REDIS_HOST=
REDIS_PORT=
REDIS_USERNAME=
REDIS_PASSWORD=
REDIS_QUEUE_PREFIX=

# Sentry
SENTRY_DSN=

# Shopify
SHOPIFY_API_KEY=
SHOPIFY_API_SECRET_KEY=
SHOPIFY_HOSTNAME=
SHOPIFY_WEBHOOK_SECRET=

# Revi
FRONTEND_URL=
BACKEND_URL=
LINK_REDIRECT_DOMAIN=

# Revi anayltics service
REVI_ANALYTICS_SERVICE_API_KEY=

# Revi integrator
REVI_INTEGRATOR_API_KEY=
REVI_INTEGRATOR_URL=

# OpenAI
OPENAI_API_KEY=

# Sms legal
SMS_LEGAL_API_TOKEN=

# E-you
EYOU_API_KEY=
EYOU_USERNAME=
EYOU_PASSWORD=
EYOU_WEBHOOK_API_KEY=

# Application
NODE_ENV=
AB_TESTING_MINIMUM_USERS=

NEW_RELIC_LICENSE_KEY=
NEW_RELIC_APP_NAME=

LOJA_INTEGRATION_APPLICATION_KEY=

ENCRYPTION_KEY=[32 characters]
ENCRYPTION_IV=[16 characters]

# Auth email provider
AUTH_EMAIL_PROVIDER_HOST=
AUTH_EMAIL_PROVIDER_PORT=
AUTH_EMAIL_PROVIDER_EMAIL=
AUTH_EMAIL_PROVIDER_USER=
AUTH_EMAIL_PROVIDER_PASSWORD=

NUVEM_SHOP_APP_ID=

# Debug tools, enable only for development
ENABLE_DEBUG_TOOLS=true|false

# Retool
RETOOL_API_KEY=

# Integrator
REVI_INTEGRATOR_URL=
REVI_INTEGRATOR_API_KEY=

JWT_SECRET=

# Service Workers
VAPID_PUBLIC_KEY=
VAPID_PRIVATE_KEY=

# N8N url
REVI_N8N_URL=
