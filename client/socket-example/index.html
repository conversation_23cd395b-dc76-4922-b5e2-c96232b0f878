<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script type="module">
    import { io } from "https://cdn.socket.io/4.4.1/socket.io.esm.min.js";
  
    const socket = io("ws://localhost:3000", {
      reconnectionDelayMax: 10000,
      auth: {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.HA32wexRFaqvLEgK5l5TGpLqcIMVtuIlZWjbbqKLj5Y'
      }
    });
    socket.on('connect', function() {
        console.log('Connected');

        socket.emit('messages', { test: 'test' });
        socket.emit('identity', 0, response =>
          console.log('Identity:', response),
        );
      });
      socket.on('NEW_MESSAGE', function(data) {
        console.log('event', data);
      });
      socket.on('exception', function(data) {
        console.log('event', data);
      });
      socket.on('disconnect', function() {
        console.log('Disconnected');
      });
  </script>
</body>

</html>