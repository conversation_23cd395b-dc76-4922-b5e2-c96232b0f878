name: Run TypeScript Compiler on Pull Request

on:
  pull_request:
    branches:
      - develop

jobs:
  build:
    name: Run tsc with Yarn
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the code
      - name: Checkout Code
        uses: actions/checkout@v4

      # Step 2: Setup Node.js with Yarn
      - name: Setup Node.js and Yarn
        uses: actions/setup-node@v4
        with:
          node-version: '20'  # Specify Node.js version
          cache: 'yarn'       # Enable caching for Yarn dependencies

      # Step 3: Install dependencies with Yarn
      - name: Install Dependencies
        run: yarn install --frozen-lockfile

      # Step 4: Run TypeScript Compiler
      - name: Run TypeScript Compiler
        run: yarn tsc --noEmit

      # Step 5: Report Success
      - name: Successful Compilation
        if: success()
        run: echo "TypeScript compilation completed successfully!"
