import { PrismaClient } from '@prisma/client';
import { writeFileSync } from 'fs';

const prisma = new PrismaClient();

async function main() {
  const templates = await prisma.messageTemplate.findMany({
    where: {
      company: {
        name: {
          contains: 'Revi',
        },
      },
    },
  });

  writeFileSync(
    `prisma/dump/outputs/${new Date().getTime()}-output-templates.json`,
    JSON.stringify(templates),
    'utf-8',
  );
  console.log({ templates });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
