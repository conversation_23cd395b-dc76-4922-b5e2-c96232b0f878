/*
  Warnings:

  - The values [TRANS<PERSON><PERSON>ON<PERSON>,OTP] on the enum `WhatsappTemplateCategory` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "WhatsappTemplateCategory_new" AS ENUM ('MARKETING', 'AUTHENTICATION', 'UTILITY');
ALTER TABLE "message_templates" ALTER COLUMN "whatsapp_template_category" DROP DEFAULT;
ALTER TABLE "message_templates" ALTER COLUMN "whatsapp_template_category" TYPE "WhatsappTemplateCategory_new" USING ("whatsapp_template_category"::text::"WhatsappTemplateCategory_new");
ALTER TABLE "gupshup_templates" ALTER COLUMN "category" TYPE "WhatsappTemplateCategory_new" USING ("category"::text::"WhatsappTemplateCategory_new");
ALTER TYPE "WhatsappTemplateCategory" RENAME TO "WhatsappTemplateCategory_old";
ALTER TYPE "WhatsappTemplateCategory_new" RENAME TO "WhatsappTemplateCategory";
DROP TYPE "WhatsappTemplateCategory_old";
ALTER TABLE "message_templates" ALTER COLUMN "whatsapp_template_category" SET DEFAULT 'MARKETING';
COMMIT;
