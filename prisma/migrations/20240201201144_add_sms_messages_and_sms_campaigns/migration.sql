-- DropForeign<PERSON>ey
ALTER TABLE "campaign_recipients" DROP CONSTRAINT "campaign_recipients_whatsapp_campaign_id_fkey";

-- AlterTable
ALTER TABLE "campaign_recipients" ADD COLUMN     "sms_campaign_id" TEXT,
ALTER COLUMN "whatsapp_campaign_id" DROP NOT NULL;

-- CreateTable
CREATE TABLE "sms_messages" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "recipient_phone_number_id" TEXT NOT NULL,
    "sms_campaign_id" TEXT,

    CONSTRAINT "sms_messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sms_campaigns" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "message_template_id" TEXT NOT NULL,
    "total_recipients" INTEGER NOT NULL,
    "total_processed" INTEGER NOT NULL DEFAULT 0,
    "status" "WhatsappCampaignStatus" NOT NULL DEFAULT E'in_progress',
    "filter_criteria" TEXT,
    "template_args" JSONB,
    "scheduled_execution_time" TIMESTAMP(3),
    "scheduled_job_id" TEXT,
    "created_by_user_id" TEXT,
    "error_message" TEXT,

    CONSTRAINT "sms_campaigns_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "sms_campaigns_company_id_idx" ON "sms_campaigns"("company_id");

-- AddForeignKey
ALTER TABLE "sms_messages" ADD CONSTRAINT "sms_messages_sms_campaign_id_fkey" FOREIGN KEY ("sms_campaign_id") REFERENCES "sms_campaigns"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sms_campaigns" ADD CONSTRAINT "sms_campaigns_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sms_campaigns" ADD CONSTRAINT "sms_campaigns_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sms_campaigns" ADD CONSTRAINT "sms_campaigns_message_template_id_fkey" FOREIGN KEY ("message_template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_recipients" ADD CONSTRAINT "campaign_recipients_sms_campaign_id_fkey" FOREIGN KEY ("sms_campaign_id") REFERENCES "sms_campaigns"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_recipients" ADD CONSTRAINT "campaign_recipients_whatsapp_campaign_id_fkey" FOREIGN KEY ("whatsapp_campaign_id") REFERENCES "whatsapp_campaigns"("id") ON DELETE SET NULL ON UPDATE CASCADE;
