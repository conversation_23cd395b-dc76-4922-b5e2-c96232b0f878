-- CreateTable
CREATE TABLE "integrations_config" (
    "id" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "source" "SourceIntegration" NOT NULL,
    "config" JSONB NOT NULL,
    "isOrderActive" BOOLEAN NOT NULL DEFAULT false,
    "isAbandonedCartActive" BOOLEAN NOT NULL DEFAULT false,
    "isProductActive" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "description" TEXT NOT NULL DEFAULT '',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "integrations_config_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "integrations_config" ADD CONSTRAINT "integrations_config_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
