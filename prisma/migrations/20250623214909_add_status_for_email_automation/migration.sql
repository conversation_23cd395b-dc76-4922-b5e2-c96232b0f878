-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "AbandonedCartStatus" ADD VALUE 'email_sent';
ALTER TYPE "AbandonedCartStatus" ADD VALUE 'whatsapp_and_email_sent';

-- AlterTable
ALTER TABLE "billing_settings" ALTER COLUMN "billing_contact_email" DROP NOT NULL;

-- AlterTable
ALTER TABLE "orders" ADD COLUMN     "is_tracking_code_sent_by_email" BOOLEAN DEFAULT false;
