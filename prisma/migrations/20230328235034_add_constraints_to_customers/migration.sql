/*
  Warnings:

  - A unique constraint covering the columns `[phone_number_id]` on the table `customers` will be added. If there are existing duplicate values, this will fail.
  - Made the column `phone_number_id` on table `customers` required. This step will fail if there are existing NULL values in that column.
  - Made the column `name` on table `customers` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "customers" ALTER COLUMN "phone_number_id" SET NOT NULL,
ALTER COLUMN "name" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "customers_phone_number_id_key" ON "customers"("phone_number_id");
