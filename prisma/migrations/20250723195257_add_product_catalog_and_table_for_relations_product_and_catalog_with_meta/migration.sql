-- AlterEnum
ALTER TYPE "LogType" ADD VALUE 'productEvents';

-- CreateTable meta_product_catalogs
CREATE TABLE "meta_product_catalogs" (
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "meta_catalog_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "meta_product_catalogs_pkey" PRIMARY KEY ("id")
);

-- CreateTable meta_catalog_product_mappings
CREATE TABLE "meta_catalog_product_mappings" (
    "id" TEXT NOT NULL,
    "meta_catalog_id" TEXT NOT NULL,
    "meta_product_id" TEXT NOT NULL,
    "meta_retailer_product_id" TEXT NOT NULL,
    "product_variant_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "meta_catalog_product_mappings_pkey" PRIMARY KEY ("id")
);

-- Create indexes
CREATE INDEX "meta_product_catalogs_company_id_idx" ON "meta_product_catalogs" ("company_id");
CREATE INDEX "meta_product_catalogs_meta_catalog_id_idx" ON "meta_product_catalogs" ("meta_catalog_id");

CREATE INDEX "meta_catalog_product_mappings_product_variant_id_idx" ON "meta_catalog_product_mappings" ("product_variant_id");
CREATE INDEX "meta_catalog_product_mappings_meta_catalog_id_idx" ON "meta_catalog_product_mappings" ("meta_catalog_id");

CREATE UNIQUE INDEX "meta_catalog_product_mappings_meta_catalog_id_product_variant_id_key" ON "meta_catalog_product_mappings" ("meta_catalog_id", "product_variant_id");

-- Add foreign keys
ALTER TABLE "meta_product_catalogs" ADD CONSTRAINT "meta_product_catalogs_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "meta_catalog_product_mappings" ADD CONSTRAINT "meta_catalog_product_mappings_meta_catalog_id_fkey" FOREIGN KEY ("meta_catalog_id") REFERENCES "meta_product_catalogs" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "meta_catalog_product_mappings" ADD CONSTRAINT "meta_catalog_product_mappings_product_variant_id_fkey" FOREIGN KEY ("product_variant_id") REFERENCES "product_variants" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
