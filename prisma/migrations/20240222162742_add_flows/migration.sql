-- Create<PERSON>num
CREATE TYPE "FlowNodeButtonType" AS ENUM ('quick_reply');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "FlowNodeType" AS ENUM ('send_whatsapp_message');

-- AlterTable
ALTER TABLE "quick_replies" ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- CreateTable
CREATE TABLE "flows" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "flows_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "flow_nodes" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "type" "FlowNodeType" NOT NULL,
    "data" JSONB NOT NULL,
    "posX" INTEGER NOT NULL,
    "posY" INTEGER NOT NULL,
    "flow_id" TEXT NOT NULL,

    CONSTRAINT "flow_nodes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FlowNodeButton" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "type" "FlowNodeButtonType" NOT NULL,
    "text" TEXT NOT NULL,
    "url" TEXT,
    "flow_node_id" TEXT NOT NULL,
    "target_flow_node_id" TEXT NOT NULL,

    CONSTRAINT "FlowNodeButton_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "flow_nodes" ADD CONSTRAINT "flow_nodes_flow_id_fkey" FOREIGN KEY ("flow_id") REFERENCES "flows"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FlowNodeButton" ADD CONSTRAINT "FlowNodeButton_flow_node_id_fkey" FOREIGN KEY ("flow_node_id") REFERENCES "flow_nodes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FlowNodeButton" ADD CONSTRAINT "FlowNodeButton_target_flow_node_id_fkey" FOREIGN KEY ("target_flow_node_id") REFERENCES "flow_nodes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
