-- AlterEnum
ALTER TYPE "WhatsappCampaignStatus" ADD VALUE 'scheduled';

-- AlterTable
ALTER TABLE "whatsapp_campaigns" ADD COLUMN     "created_by_user_id" TEXT,
ADD COLUMN     "scheduled_execution_time" TIMESTAMP(3),
ADD COLUMN     "template_args" JSONB;

-- CreateTable
CREATE TABLE "campaign_recipients" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "whatsapp_campaign_id" TEXT NOT NULL,
    "customer_id" TEXT NOT NULL,

    CONSTRAINT "campaign_recipients_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "campaign_recipients_whatsapp_campaign_id_customer_id_key" ON "campaign_recipients"("whatsapp_campaign_id", "customer_id");

-- AddForeignKey
ALTER TABLE "whatsapp_campaigns" ADD CONSTRAINT "whatsapp_campaigns_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_recipients" ADD CONSTRAINT "campaign_recipients_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_recipients" ADD CONSTRAINT "campaign_recipients_whatsapp_campaign_id_fkey" FOREIGN KEY ("whatsapp_campaign_id") REFERENCES "whatsapp_campaigns"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
