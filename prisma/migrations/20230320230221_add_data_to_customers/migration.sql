/*
  Warnings:

  - Made the column `customer_id` on table `conversations` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "conversations" DROP CONSTRAINT "conversations_customer_id_fkey";

create extension if not exists "uuid-ossp";

insert
	into
	customers (id,
	phone_number_id,
	"name",
	company_id)
select
	uuid_generate_v4(),
	c.recipient_phone_number_id,
	c.recipient_name,
	c.company_id
from
	conversations c;

UPDATE conversations 
  SET customer_id =(SELECT id FROM customers  WHERE customers.phone_number_id = conversations.recipient_phone_number_id limit 1);

-- AlterTable
ALTER TABLE "conversations" ALTER COLUMN "customer_id" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
