-- CreateTable
CREATE TABLE "flow_events" (
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "conversation_id" TEXT NOT NULL,
    "flow_id" TEXT,
    "flow_node_id" TEXT,
    "flow_trigger_id" TEXT,
    "flow_node_button_id" TEXT,
    "message_id" TEXT NOT NULL,
    "metadata" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "flow_events_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "flow_events" ADD CONSTRAINT "flow_events_flow_node_id_fkey" FOREIGN KEY ("flow_node_id") REFERENCES "flow_nodes"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "flow_events" ADD CONSTRAINT "flow_events_flow_node_button_id_fkey" FOREIGN KEY ("flow_node_button_id") REFERENCES "flow_node_buttons"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "flow_events" ADD CONSTRAINT "flow_events_flow_trigger_id_fkey" FOREIGN KEY ("flow_trigger_id") REFERENCES "flow_triggers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "flow_events" ADD CONSTRAINT "flow_events_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "flow_events" ADD CONSTRAINT "flow_events_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "flow_events" ADD CONSTRAINT "flow_events_flow_id_fkey" FOREIGN KEY ("flow_id") REFERENCES "flows"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "flow_events" ADD CONSTRAINT "flow_events_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "messages"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
