/*
  Warnings:

  - A unique constraint covering the columns `[name]` on the table `message_templates` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name,company_id]` on the table `message_templates` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[template_text,company_id]` on the table `message_templates` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE UNIQUE INDEX "message_templates_name_key" ON "message_templates"("name");

-- CreateIndex
CREATE UNIQUE INDEX "message_templates_name_company_id_key" ON "message_templates"("name", "company_id");

-- CreateIndex
CREATE UNIQUE INDEX "message_templates_template_text_company_id_key" ON "message_templates"("template_text", "company_id");
