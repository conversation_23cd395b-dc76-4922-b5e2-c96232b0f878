/*
  Warnings:

  - You are about to drop the `FlowNodeButton` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "FlowNodeButton" DROP CONSTRAINT "FlowNodeButton_flow_node_id_fkey";

-- DropForeignKey
ALTER TABLE "FlowNodeButton" DROP CONSTRAINT "FlowNodeButton_target_flow_node_id_fkey";

-- DropTable
DROP TABLE "FlowNodeButton";

-- CreateTable
CREATE TABLE "flow_node_buttons" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "type" "FlowNodeButtonType" NOT NULL,
    "text" TEXT NOT NULL,
    "url" TEXT,
    "flow_node_id" TEXT NOT NULL,
    "target_flow_node_id" TEXT NOT NULL,

    CONSTRAINT "flow_node_buttons_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "flow_node_buttons_flow_node_id_text_type_key" ON "flow_node_buttons"("flow_node_id", "text", "type");

-- AddForeignKey
ALTER TABLE "flow_node_buttons" ADD CONSTRAINT "flow_node_buttons_flow_node_id_fkey" FOREIGN KEY ("flow_node_id") REFERENCES "flow_nodes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "flow_node_buttons" ADD CONSTRAINT "flow_node_buttons_target_flow_node_id_fkey" FOREIGN KEY ("target_flow_node_id") REFERENCES "flow_nodes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
