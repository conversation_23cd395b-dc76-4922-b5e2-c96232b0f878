-- CreateEnum
CREATE TYPE "LogType" AS ENUM ('error');

-- CreateEnum
CREATE TYPE "LogSource" AS ENUM ('gupshup', 'internal');

-- CreateTable
CREATE TABLE "logs" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "type" "LogType" NOT NULL,
    "source" "LogSource",
    "meta" JSONB,

    CONSTRAINT "logs_pkey" PRIMARY KEY ("id")
);
