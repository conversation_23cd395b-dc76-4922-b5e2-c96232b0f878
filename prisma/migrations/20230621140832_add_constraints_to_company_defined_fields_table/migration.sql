/*
  Warnings:

  - The values [customer] on the enum `CompanyDefinedFieldTable` will be removed. If these variants are still used in the database, this will fail.
  - A unique constraint covering the columns `[company_id,table,name]` on the table `company_defined_fields` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "CompanyDefinedFieldTable_new" AS ENUM ('customers');
ALTER TABLE "company_defined_fields" ALTER COLUMN "table" TYPE "CompanyDefinedFieldTable_new" USING ("table"::text::"CompanyDefinedFieldTable_new");
ALTER TYPE "CompanyDefinedFieldTable" RENAME TO "CompanyDefinedFieldTable_old";
ALTER TYPE "CompanyDefinedFieldTable_new" RENAME TO "CompanyDefinedFieldTable";
DROP TYPE "CompanyDefinedFieldTable_old";
COMMIT;

-- CreateIndex
CREATE UNIQUE INDEX "company_defined_fields_company_id_table_name_key" ON "company_defined_fields"("company_id", "table", "name");
