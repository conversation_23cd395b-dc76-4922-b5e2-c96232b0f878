/*
  Warnings:

  - The values [sendMessageTemplate] on the enum `LogType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "LogType_new" AS ENUM ('syncOrders', 'sendAbandonedCartMessages', 'executeCompanyCustomPeriodicTask', 'connectingDatabase', 'sendTrackingCodeMessages', 'reviPublicApiCall');
ALTER TABLE "logs" ALTER COLUMN "type" TYPE "LogType_new" USING ("type"::text::"LogType_new");
ALTER TYPE "LogType" RENAME TO "LogType_old";
ALTER TYPE "LogType_new" RENAME TO "LogType";
DROP TYPE "LogType_old";
COMMIT;
