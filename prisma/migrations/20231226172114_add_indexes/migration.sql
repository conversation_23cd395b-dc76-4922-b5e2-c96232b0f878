/*
  Warnings:

  - You are about to drop the column `finished_at` on the `whatsapp_campaigns` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[company_id,name]` on the table `categories` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[company_id,name]` on the table `conversation_categories` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[company_id,phone_number_id]` on the table `customers` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[company_id,name]` on the table `message_templates` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[gupshup_template_id,template_text]` on the table `message_templates` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[company_id,source_id]` on the table `orders` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "WhatsappCampaignStatus" ADD VALUE 'canceled';
ALTER TYPE "WhatsappCampaignStatus" ADD VALUE 'failed';

-- DropIndex
DROP INDEX "categories_name_company_id_key";

-- DropIndex
DROP INDEX "conversation_categories_name_company_id_key";

-- DropIndex
DROP INDEX "customers_phone_number_id_company_id_key";

-- DropIndex
DROP INDEX "message_templates_name_company_id_key";

-- DropIndex
DROP INDEX "message_templates_template_text_gupshup_template_id_key";

-- DropIndex
DROP INDEX "messages_from_system_conversation_id_created_at_idx";

-- DropIndex
DROP INDEX "orders_source_id_company_id_key";

-- AlterTable
ALTER TABLE "whatsapp_campaigns" DROP COLUMN "finished_at";

-- CreateIndex
CREATE UNIQUE INDEX "categories_company_id_name_key" ON "categories"("company_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "conversation_categories_company_id_name_key" ON "conversation_categories"("company_id", "name");

-- CreateIndex
CREATE INDEX "conversations_company_id_idx" ON "conversations"("company_id");

-- CreateIndex
CREATE INDEX "conversations_customer_id_idx" ON "conversations"("customer_id");

-- CreateIndex
CREATE INDEX "customers_company_id_is_opted_out_idx" ON "customers"("company_id", "is_opted_out");

-- CreateIndex
CREATE UNIQUE INDEX "customers_company_id_phone_number_id_key" ON "customers"("company_id", "phone_number_id");

-- CreateIndex
CREATE UNIQUE INDEX "message_templates_company_id_name_key" ON "message_templates"("company_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "message_templates_gupshup_template_id_template_text_key" ON "message_templates"("gupshup_template_id", "template_text");

-- CreateIndex
CREATE INDEX "messages_whatsapp_campaign_id_idx" ON "messages"("whatsapp_campaign_id");

-- CreateIndex
CREATE INDEX "messages_conversation_id_from_system_created_at_idx" ON "messages"("conversation_id", "from_system", "created_at" DESC);

-- CreateIndex
CREATE INDEX "orders_company_id_customer_id_idx" ON "orders"("company_id", "customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "orders_company_id_source_id_key" ON "orders"("company_id", "source_id");

-- CreateIndex
CREATE INDEX "whatsapp_campaigns_company_id_idx" ON "whatsapp_campaigns"("company_id");
