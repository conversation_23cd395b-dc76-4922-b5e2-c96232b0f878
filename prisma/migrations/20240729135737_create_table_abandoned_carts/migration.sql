-- CreateEnum
CREATE TYPE "AbandonedCartStatus" AS ENUM ('abandoned', 'whatsapp_sent', 'failed');

-- CreateTable
CREATE TABLE "abandoned_carts" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "source_created_at" TIMESTAMP(3) NOT NULL,
    "source_id" TEXT,
    "source_order_id" TEXT,
    "customer_phone_number_id" TEXT,
    "customer_email" TEXT,
    "customer_name" TEXT,
    "phone_number_id" TEXT,
    "company_id" TEXT NOT NULL,
    "source" "SourceIntegration" NOT NULL DEFAULT 'unknown',
    "status" "AbandonedCartStatus" NOT NULL DEFAULT 'abandoned',
    "recovery_message_id" TEXT,
    "value" INTEGER NOT NULL,

    CONSTRAINT "abandoned_carts_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "abandoned_carts" ADD CONSTRAINT "abandoned_carts_recovery_message_id_fkey" FOREIGN KEY ("recovery_message_id") REFERENCES "messages"("id") ON DELETE SET NULL ON UPDATE CASCADE;
