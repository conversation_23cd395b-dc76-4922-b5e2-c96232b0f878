-- DropForeignKey
ALTER TABLE "emails" DROP CONSTRAINT "emails_email_campaign_id_fkey";

-- CreateTable
CREATE TABLE "email_campaign_results" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "email_campaign_id" TEXT NOT NULL,
    "total_sent" INTEGER NOT NULL DEFAULT 0,
    "total_reads" INTEGER NOT NULL DEFAULT 0,
    "total_opens" INTEGER NOT NULL DEFAULT 0,
    "total_clicks" INTEGER NOT NULL DEFAULT 0,
    "total_bounces" INTEGER NOT NULL DEFAULT 0,
    "total_unsubscribes" INTEGER NOT NULL DEFAULT 0,
    "total_spam_reports" INTEGER NOT NULL DEFAULT 0,
    "total_failures" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "email_campaign_results_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "email_campaign_results_email_campaign_id_key" ON "email_campaign_results"("email_campaign_id");

-- CreateIndex
CREATE INDEX "email_campaign_results_email_campaign_id_idx" ON "email_campaign_results"("email_campaign_id");

-- AddForeignKey
ALTER TABLE "emails" ADD CONSTRAINT "emails_email_campaign_id_fkey" FOREIGN KEY ("email_campaign_id") REFERENCES "email_campaigns"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_campaign_results" ADD CONSTRAINT "email_campaign_results_email_campaign_id_fkey" FOREIGN KEY ("email_campaign_id") REFERENCES "email_campaigns"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
