-- CreateEnum
CREATE TYPE "WhatsappCampaignStatus" AS ENUM ('in_progress', 'completed');

-- AlterTable
ALTER TABLE "messages" ADD COLUMN     "whatsapp_campaign_id" TEXT;

-- CreateTable
CREATE TABLE "whatsapp_campaigns" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "template_id" TEXT NOT NULL,
    "total_recipients" INTEGER NOT NULL DEFAULT 0,
    "status" "WhatsappCampaignStatus" NOT NULL DEFAULT E'in_progress',
    "finished_at" TIMESTAMP(3),

    CONSTRAINT "whatsapp_campaigns_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "messages" ADD CONSTRAINT "messages_whatsapp_campaign_id_fkey" FOREIGN KEY ("whatsapp_campaign_id") REFERENCES "whatsapp_campaigns"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "whatsapp_campaigns" ADD CONSTRAINT "whatsapp_campaigns_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "whatsapp_campaigns" ADD CONSTRAINT "whatsapp_campaigns_template_id_fkey" FOREIGN KEY ("template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
