/*
  Warnings:

  - The values [shopifyCartSync] on the enum `LogSource` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "LogSource_new" AS ENUM ('gupshup', 'internal', 'vtex', 'shopifyOrderSync', 'cartRecovery');
ALTER TABLE "logs" ALTER COLUMN "source" TYPE "LogSource_new" USING ("source"::text::"LogSource_new");
ALTER TYPE "LogSource" RENAME TO "LogSource_old";
ALTER TYPE "LogSource_new" RENAME TO "LogSource";
DROP TYPE "LogSource_old";
COMMIT;
