/*
  Warnings:

  - You are about to drop the column `category` on the `message_templates` table. All the data in the column will be lost.

*/
-- <PERSON><PERSON><PERSON>num
CREATE TYPE "WhatsappTemplateCategory" AS ENUM ('TRANSACTIONAL', 'MARKETING', 'OTP');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "MessageTemplateType" AS ENUM ('MARKETING', 'REVIEW_REQUEST', 'INITIAL_MESSAGE');

-- AlterTable
ALTER TABLE "message_templates" DROP COLUMN "category",
ADD COLUMN     "type" "MessageTemplateType" NOT NULL DEFAULT E'INITIAL_MESSAGE',
ADD COLUMN     "whatsapp_template_category" "WhatsappTemplateCategory" NOT NULL DEFAULT E'MARKETING';

-- DropEnum
DROP TYPE "MessageTemplateCategory";
