-- CreateTable
CREATE TABLE "flow_node_scheduled_actions" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "flow_node_id" TEXT NOT NULL,
    "customer_id" TEXT NOT NULL,
    "execution_time" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "flow_node_scheduled_actions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "flow_node_scheduled_actions_execution_time_idx" ON "flow_node_scheduled_actions"("execution_time");

-- AddForeignKey
ALTER TABLE "flow_node_scheduled_actions" ADD CONSTRAINT "flow_node_scheduled_actions_flow_node_id_fkey" FOREIGN KEY ("flow_node_id") REFERENCES "flow_nodes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "flow_node_scheduled_actions" ADD CONSTRAINT "flow_node_scheduled_actions_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
