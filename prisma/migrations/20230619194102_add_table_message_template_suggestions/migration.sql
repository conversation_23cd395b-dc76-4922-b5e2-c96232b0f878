-- AlterTable
ALTER TABLE "message_templates" ADD COLUMN     "message_template_suggestion_id" TEXT;

-- CreateTable
CREATE TABLE "message_template_suggestions" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "template_text" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "message_template_suggestions_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "message_templates" ADD CONSTRAINT "message_templates_message_template_suggestion_id_fkey" FOREIGN KEY ("message_template_suggestion_id") REFERENCES "message_template_suggestions"("id") ON DELETE SET NULL ON UPDATE CASCADE;
