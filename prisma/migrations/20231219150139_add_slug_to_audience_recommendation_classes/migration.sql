/*
  Warnings:

  - A unique constraint covering the columns `[slug]` on the table `audience_recommendation_classes` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `slug` to the `audience_recommendation_classes` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "AudienceRecommendationClassSlug" AS ENUM ('champion', 'new', 'inactive');

-- AlterTable
ALTER TABLE "audience_recommendation_classes" ADD COLUMN     "slug" "AudienceRecommendationClassSlug" NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "audience_recommendation_classes_slug_key" ON "audience_recommendation_classes"("slug");
