/*
  Warnings:

  - You are about to drop the column `customer_service_cost` on the `billing_settings` table. All the data in the column will be lost.
  - You are about to drop the column `monthly_cost` on the `billing_settings` table. All the data in the column will be lost.
  - You are about to drop the column `description` on the `invoices` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[company_id]` on the table `billing_settings` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `billing_contact_email` to the `billing_settings` table without a default value. This is not possible if the table is not empty.
  - Added the required column `customer_service_fee` to the `billing_settings` table without a default value. This is not possible if the table is not empty.
  - Added the required column `reference_month` to the `invoices` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "billing_settings" DROP COLUMN "customer_service_cost",
DROP COLUMN "monthly_cost",
ADD COLUMN     "billing_contact_email" TEXT NOT NULL,
ADD COLUMN     "customer_service_fee" INTEGER NOT NULL,
ADD COLUMN     "payment_method" "PaymentMethod" NOT NULL DEFAULT 'boleto',
ADD COLUMN     "sms_extra_message_fee" INTEGER,
ADD COLUMN     "sms_package_limit" INTEGER,
ADD COLUMN     "whatsapp_marketing_extra_message_fee" INTEGER,
ADD COLUMN     "whatsapp_marketing_package_limit" INTEGER,
ADD COLUMN     "whatsapp_service_extra_message_fee" INTEGER,
ADD COLUMN     "whatsapp_service_message_cost" INTEGER,
ADD COLUMN     "whatsapp_service_package_limit" INTEGER,
ADD COLUMN     "whatsapp_utility_extra_message_fee" INTEGER,
ADD COLUMN     "whatsapp_utility_package_limit" INTEGER,
ALTER COLUMN "whatsapp_marketing_message_cost" DROP NOT NULL,
ALTER COLUMN "whatsapp_utility_message_cost" DROP NOT NULL,
ALTER COLUMN "sms_message_cost" DROP NOT NULL;

-- AlterTable
ALTER TABLE "companies" ADD COLUMN     "cnpj" TEXT,
ADD COLUMN     "razao_social" TEXT;

-- AlterTable
ALTER TABLE "invoices" DROP COLUMN "description",
ADD COLUMN     "reference_month" TIMESTAMP(3) NOT NULL;

-- CreateTable
CREATE TABLE "billing_settings_history" (
    "id" TEXT NOT NULL,
    "billing_settings_id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "customer_service_fee" INTEGER NOT NULL,
    "whatsapp_marketing_package_limit" INTEGER,
    "whatsapp_utility_package_limit" INTEGER,
    "whatsapp_service_package_limit" INTEGER,
    "whatsapp_marketing_message_cost" INTEGER,
    "whatsapp_utility_message_cost" INTEGER,
    "whatsapp_service_message_cost" INTEGER,
    "whatsapp_marketing_extra_message_fee" INTEGER,
    "whatsapp_utility_extra_message_fee" INTEGER,
    "whatsapp_service_extra_message_fee" INTEGER,
    "billing_contact_email" TEXT NOT NULL,
    "payment_method" "PaymentMethod" NOT NULL DEFAULT 'boleto',
    "sms_message_cost" INTEGER,
    "sms_package_limit" INTEGER,
    "sms_extra_message_fee" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "billing_settings_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "billing_settings_company_id_key" ON "billing_settings"("company_id");

-- AddForeignKey
ALTER TABLE "billing_settings_history" ADD CONSTRAINT "billing_settings_history_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "billing_settings_history" ADD CONSTRAINT "billing_settings_history_billing_settings_id_fkey" FOREIGN KEY ("billing_settings_id") REFERENCES "billing_settings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
