-- CreateTable
CREATE TABLE "user_conversation_sectors" (
    "user_id" TEXT NOT NULL,
    "conversation_sector_id" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "user_conversation_sectors_user_id_conversation_sector_id_key" ON "user_conversation_sectors"("user_id", "conversation_sector_id");

-- AddForeignKey
ALTER TABLE "user_conversation_sectors" ADD CONSTRAINT "user_conversation_sectors_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddF<PERSON>ignKey
ALTER TABLE "user_conversation_sectors" ADD CONSTRAINT "user_conversation_sectors_conversation_sector_id_fkey" FOREIGN KEY ("conversation_sector_id") REFERENCES "conversation_sectors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
