-- AlterEnum
ALTER TYPE "LogType" ADD VALUE 'apiTracking';

-- CreateTable
CREATE TABLE "api_requests_logs" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "controller" TEXT NOT NULL,
    "handler" TEXT NOT NULL,
    "url" TEXT,
    "method" TEXT,
    "company_id" TEXT,
    "user_id" TEXT,
    "user_ip" TEXT,
    "user_agent" TEXT,
    "request_headers" JSONB,
    "request_body" JSONB,
    "request_query_params" JSONB,
    "response_body" JSONB,
    "error" JSONB,

    CONSTRAINT "api_requests_logs_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "api_requests_logs" ADD CONSTRAINT "api_requests_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "api_requests_logs" ADD CONSTRAINT "api_requests_logs_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE SET NULL ON UPDATE CASCADE;
