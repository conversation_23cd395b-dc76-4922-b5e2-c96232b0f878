/*
  Warnings:

  - The values [recover_canceled_order] on the enum `AutomationTypeSlug` will be removed. If these variants are still used in the database, this will fail.
  - The values [sendCanceledOrderRecoveryMessages] on the enum `LogType` will be removed. If these variants are still used in the database, this will fail.
  - The values [RECOVER_CANCELED_ORDER] on the enum `MessageTemplateType` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `is_canceled_order_recovery_message_sent` on the `orders` table. All the data in the column will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "AutomationTypeSlug_new" AS ENUM ('tracking_code', 'abandoned_cart', 'new_order', 'welcome_registration', 'order_confirmation', 'order_payment_confirmation', 'custom', 'order_status_update');
ALTER TABLE "automation_types" ALTER COLUMN "slug" TYPE "AutomationTypeSlug_new" USING ("slug"::text::"AutomationTypeSlug_new");
ALTER TYPE "AutomationTypeSlug" RENAME TO "AutomationTypeSlug_old";
ALTER TYPE "AutomationTypeSlug_new" RENAME TO "AutomationTypeSlug";
DROP TYPE "AutomationTypeSlug_old";
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "LogType_new" AS ENUM ('syncOrders', 'syncAbandonedCarts', 'sendAbandonedCartMessages', 'executeCompanyCustomPeriodicTask', 'connectingDatabase', 'sendTrackingCodeMessages', 'reviPublicApiCall', 'orderEvents', 'syncCustomers', 'scheduleAbandonedCarts', 'userActivity', 'sendOrderStatusUpdateMessages');
ALTER TABLE "logs" ALTER COLUMN "type" TYPE "LogType_new" USING ("type"::text::"LogType_new");
ALTER TYPE "LogType" RENAME TO "LogType_old";
ALTER TYPE "LogType_new" RENAME TO "LogType";
DROP TYPE "LogType_old";
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "MessageTemplateType_new" AS ENUM ('MARKETING', 'REVIEW_REQUEST', 'INITIAL_MESSAGE', 'ABANDONED_CART', 'TRACKING_CODE', 'NEW_ORDER', 'WELCOME_REGISTRATION', 'ORDER_CONFIRMATION', 'ORDER_PAYMENT_CONFIRMATION', 'ORDER_STATUS_UPDATE');
ALTER TABLE "message_templates" ALTER COLUMN "type" DROP DEFAULT;
ALTER TABLE "message_templates" ALTER COLUMN "type" TYPE "MessageTemplateType_new" USING ("type"::text::"MessageTemplateType_new");
ALTER TYPE "MessageTemplateType" RENAME TO "MessageTemplateType_old";
ALTER TYPE "MessageTemplateType_new" RENAME TO "MessageTemplateType";
DROP TYPE "MessageTemplateType_old";
ALTER TABLE "message_templates" ALTER COLUMN "type" SET DEFAULT 'INITIAL_MESSAGE';
COMMIT;

-- AlterTable
ALTER TABLE "automations" ADD COLUMN "data" JSONB;

-- AlterTable
ALTER TABLE "orders" DROP COLUMN "is_canceled_order_recovery_message_sent";
