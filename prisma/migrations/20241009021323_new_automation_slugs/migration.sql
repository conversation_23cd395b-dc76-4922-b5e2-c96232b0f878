-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "AutomationTypeSlug" ADD VALUE 'new_order';
ALTER TYPE "AutomationTypeSlug" ADD VALUE 'welcome_registration';
ALTER TYPE "AutomationTypeSlug" ADD VALUE 'order_confirmation';
ALTER TYPE "AutomationTypeSlug" ADD VALUE 'order_payment_confirmation';
