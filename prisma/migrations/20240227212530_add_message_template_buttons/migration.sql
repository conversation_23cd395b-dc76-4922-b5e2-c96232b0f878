/*
  Warnings:

  - Changed the type of `type` on the `flow_node_buttons` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "ButtonType" AS ENUM ('QUICK_REPLY', 'URL');

-- AlterTable
ALTER TABLE "flow_node_buttons" DROP COLUMN "type",
ADD COLUMN     "type" "ButtonType" NOT NULL;

-- AlterTable
ALTER TABLE "message_templates" ADD COLUMN     "footer_text" TEXT;

-- DropEnum
DROP TYPE "FlowNodeButtonType";

-- CreateTable
CREATE TABLE "message_template_buttons" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "message_template_id" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "type" "ButtonType" NOT NULL,

    CONSTRAINT "message_template_buttons_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "flow_node_buttons_flow_node_id_text_type_key" ON "flow_node_buttons"("flow_node_id", "text", "type");

-- AddForeignKey
ALTER TABLE "message_template_buttons" ADD CONSTRAINT "message_template_buttons_message_template_id_fkey" FOREIGN KEY ("message_template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
