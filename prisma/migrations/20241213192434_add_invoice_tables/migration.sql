-- CreateEnum
CREATE TYPE "InvoiceStatus" AS ENUM ('pending', 'paid', 'canceled');

-- Create<PERSON>num
CREATE TYPE "PaymentMethod" AS ENUM ('boleto', 'credit_card', 'pix');

-- CreateTable
CREATE TABLE "billing_settings" (
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "customer_service_cost" INTEGER NOT NULL,
    "monthly_cost" INTEGER NOT NULL,
    "whatsapp_marketing_message_cost" INTEGER NOT NULL,
    "whatsapp_utility_message_cost" INTEGER NOT NULL,
    "sms_message_cost" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "billing_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "invoices" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "payment_method" "PaymentMethod" NOT NULL DEFAULT 'boleto',
    "value" INTEGER NOT NULL,
    "due_date" TIMESTAMP(3) NOT NULL,
    "description" TEXT,

    CONSTRAINT "invoices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "invoice_items" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "invoice_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "unit_price" INTEGER NOT NULL,
    "quantity" INTEGER NOT NULL,
    "total_price" INTEGER NOT NULL,
    "discount" INTEGER NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "invoice_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "gupshup_daily_app_usage" (
    "id" TEXT NOT NULL,
    "app_id" TEXT NOT NULL,
    "app_name" TEXT NOT NULL,
    "authentication" INTEGER NOT NULL,
    "cumulative_bill" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "discount" DOUBLE PRECISION NOT NULL,
    "fep" INTEGER NOT NULL,
    "ftc" INTEGER NOT NULL,
    "gs_cap" DOUBLE PRECISION NOT NULL,
    "gs_fees" DOUBLE PRECISION NOT NULL,
    "incoming_msg" INTEGER NOT NULL,
    "outgoing_msg" INTEGER NOT NULL,
    "outgoing_media_msg" INTEGER NOT NULL,
    "marketing" INTEGER NOT NULL,
    "service" INTEGER NOT NULL,
    "template_msg" INTEGER NOT NULL,
    "template_media_msg" INTEGER NOT NULL,
    "total_fees" DOUBLE PRECISION NOT NULL,
    "total_msg" INTEGER NOT NULL,
    "utility" INTEGER NOT NULL,
    "wa_fees" DOUBLE PRECISION NOT NULL,
    "international_authentication" INTEGER NOT NULL,

    CONSTRAINT "gupshup_daily_app_usage_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "billing_settings" ADD CONSTRAINT "billing_settings_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invoice_items" ADD CONSTRAINT "invoice_items_invoice_id_fkey" FOREIGN KEY ("invoice_id") REFERENCES "invoices"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "gupshup_daily_app_usage" ADD CONSTRAINT "gupshup_daily_app_usage_app_name_fkey" FOREIGN KEY ("app_name") REFERENCES "companies"("gupshup_app_name") ON DELETE RESTRICT ON UPDATE CASCADE;
