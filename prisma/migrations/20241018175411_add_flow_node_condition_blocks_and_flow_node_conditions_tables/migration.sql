-- CreateEnum
CREATE TYPE "ConditionalJoinType" AS ENUM ('AND', 'OR');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "flowNodeConditionsType" AS ENUM ('default', 'total_orders', 'total_purchases', 'days_since_last_purchase');

-- Create<PERSON>num
CREATE TYPE "ComparisonOperator" AS ENUM ('=', '>', '<', '>=', '<=', 'IN', 'BETWEEN');

-- AlterEnum
ALTER TYPE "FlowNodeType" ADD VALUE 'conditions_check';

-- CreateTable
CREATE TABLE "flow_node_condition_blocks" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "conditional_join_type" "ConditionalJoinType" NOT NULL,
    "priority" INTEGER NOT NULL,
    "target_flow_node_id" TEXT,
    "flow_node_id" TEXT NOT NULL,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "flow_node_condition_blocks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "flow_node_conditions" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "type" "flowNodeConditionsType" NOT NULL,
    "value" JSONB NOT NULL,
    "comparison_operator" "ComparisonOperator" NOT NULL,
    "flow_node_condition_block_id" TEXT NOT NULL,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "flow_node_conditions_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "flow_node_condition_blocks" ADD CONSTRAINT "flow_node_condition_blocks_target_flow_node_id_fkey" FOREIGN KEY ("target_flow_node_id") REFERENCES "flow_nodes"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "flow_node_condition_blocks" ADD CONSTRAINT "flow_node_condition_blocks_flow_node_id_fkey" FOREIGN KEY ("flow_node_id") REFERENCES "flow_nodes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "flow_node_conditions" ADD CONSTRAINT "flow_node_conditions_flow_node_condition_block_id_fkey" FOREIGN KEY ("flow_node_condition_block_id") REFERENCES "flow_node_condition_blocks"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
