/*
  Warnings:

  - The values [ticket_finished_with_csat] on the enum `FlowTriggerType` will be removed. If these variants are still used in the database, this will fail.
  - The values [ticket_finished_with_csat] on the enum `FlowType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "FlowTriggerType_new" AS ENUM ('exact_match', 'keyword_match', 'abandoned_cart', 'quick_reply_message_template', 'csat');
ALTER TABLE "flow_triggers" ALTER COLUMN "type" TYPE "FlowTriggerType_new" USING ("type"::text::"FlowTriggerType_new");
ALTER TYPE "FlowTriggerType" RENAME TO "FlowTriggerType_old";
ALTER TYPE "FlowTriggerType_new" RENAME TO "FlowTriggerType";
DROP TYPE "FlowTriggerType_old";
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "FlowType_new" AS ENUM ('default', 'abandoned_cart', 'csat');
ALTER TABLE "flows" ALTER COLUMN "type" DROP DEFAULT;
ALTER TABLE "flows" ALTER COLUMN "type" TYPE "FlowType_new" USING ("type"::text::"FlowType_new");
ALTER TYPE "FlowType" RENAME TO "FlowType_old";
ALTER TYPE "FlowType_new" RENAME TO "FlowType";
DROP TYPE "FlowType_old";
ALTER TABLE "flows" ALTER COLUMN "type" SET DEFAULT 'default';
COMMIT;
