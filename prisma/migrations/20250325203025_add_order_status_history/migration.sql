-- CreateTable
CREATE TABLE "order_status_history" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "order_id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "source_updated_at" TIMESTAMP(3),
    "previous_status" TEXT,
    "new_status" TEXT NOT NULL,
    "is_whatsapp_sent" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "order_status_history_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "order_status_history" ADD CONSTRAINT "order_status_history_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "order_status_history" ADD CONSTRAINT "order_status_history_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- CreateIndex
CREATE INDEX "order_status_history_order_id_new_status_idx" ON "order_status_history"("order_id", "new_status");

-- CreateIndex
CREATE UNIQUE INDEX "order_status_history_order_id_new_status_key" ON "order_status_history"("order_id", "new_status");