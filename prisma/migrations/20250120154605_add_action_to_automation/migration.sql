-- CreateEnum
CREATE TYPE "AutomationAction" AS ENUM ('send_message_template', 'trigger_flow');

-- DropForeignKey
ALTER TABLE "automations" DROP CONSTRAINT "automations_message_template_id_fkey";

-- AlterTable
ALTER TABLE "automations" ADD COLUMN     "action" "AutomationAction" NOT NULL DEFAULT 'send_message_template',
ADD COLUMN     "flow_id" TEXT,
ALTER COLUMN "message_template_id" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "automations" ADD CONSTRAINT "automations_message_template_id_fkey" FOREIGN KEY ("message_template_id") REFERENCES "message_templates"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "automations" ADD CONSTRAINT "automations_flow_id_fkey" FOREIGN KEY ("flow_id") REFERENCES "flows"("id") ON DELETE SET NULL ON UPDATE CASCADE;
