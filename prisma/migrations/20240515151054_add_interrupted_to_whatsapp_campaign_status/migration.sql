/*
  Warnings:

  - The values [failed] on the enum `WhatsappCampaignStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "WhatsappCampaignStatus_new" AS ENUM ('in_progress', 'completed', 'interrupted', 'scheduled', 'canceled');
ALTER TABLE "whatsapp_campaigns" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "whatsapp_campaigns" ALTER COLUMN "status" TYPE "WhatsappCampaignStatus_new" USING ("status"::text::"WhatsappCampaignStatus_new");
ALTER TYPE "WhatsappCampaignStatus" RENAME TO "WhatsappCampaignStatus_old";
ALTER TYPE "WhatsappCampaignStatus_new" RENAME TO "WhatsappCampaignStatus";
DROP TYPE "WhatsappCampaignStatus_old";
ALTER TABLE "whatsapp_campaigns" ALTER COLUMN "status" SET DEFAULT 'in_progress';
COMMIT;
