/*
  Warnings:

  - You are about to drop the `custom_fields` table. If the table is not empty, all the data it contains will be lost.

*/
-- <PERSON>reateEnum
CREATE TYPE "CompanyDefinedFieldDataType" AS ENUM ('string', 'number', 'boolean', 'date');

-- CreateEnum
CREATE TYPE "CompanyDefinedFieldTable" AS ENUM ('customer');

-- DropForeignKey
ALTER TABLE "custom_fields" DROP CONSTRAINT "custom_fields_company_id_fkey";

-- DropTable
DROP TABLE "custom_fields";

-- DropEnum
DROP TYPE "CustomFieldDataType";

-- DropEnum
DROP TYPE "CustomFieldTable";

-- CreateTable
CREATE TABLE "company_defined_fields" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "table" "CompanyDefinedFieldTable" NOT NULL,
    "name" TEXT NOT NULL,
    "data_type" "CompanyDefinedFieldDataType" NOT NULL,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "company_defined_fields_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "company_defined_fields" ADD CONSTRAINT "company_defined_fields_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
