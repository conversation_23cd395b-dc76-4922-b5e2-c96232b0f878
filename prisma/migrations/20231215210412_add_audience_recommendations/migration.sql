-- CreateTable
CREATE TABLE "audience_recommendations" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "criteria" TEXT NOT NULL,
    "audience_recommendation_class_id" TEXT NOT NULL,

    CONSTRAINT "audience_recommendations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "audience_recommendation_classes" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "audience_recommendation_classes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "audience_recommendation_classes_name_key" ON "audience_recommendation_classes"("name");

-- AddForeignKey
ALTER TABLE "audience_recommendations" ADD CONSTRAINT "audience_recommendations_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "audience_recommendations" ADD CONSTRAINT "audience_recommendations_audience_recommendation_class_id_fkey" FOREIGN KEY ("audience_recommendation_class_id") REFERENCES "audience_recommendation_classes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
