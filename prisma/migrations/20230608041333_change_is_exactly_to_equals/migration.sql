/*
  Warnings:

  - The values [isExactly] on the enum `AutomaticReplyCondition` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "AutomaticReplyCondition_new" AS ENUM ('contains', 'equals');
ALTER TABLE "automatic_replies" ALTER COLUMN "condition" TYPE "AutomaticReplyCondition_new" USING ("condition"::text::"AutomaticReplyCondition_new");
ALTER TYPE "AutomaticReplyCondition" RENAME TO "AutomaticReplyCondition_old";
ALTER TYPE "AutomaticReplyCondition_new" RENAME TO "AutomaticReplyCondition";
DROP TYPE "AutomaticReplyCondition_old";
COMMIT;
