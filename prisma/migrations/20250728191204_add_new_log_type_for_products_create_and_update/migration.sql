-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "LogType" ADD VALUE 'handleProductUpdate';
ALTER TYPE "LogType" ADD VALUE 'handleProductCreate';

-- RenameIndex
ALTER INDEX "meta_catalog_product_mappings_meta_catalog_id_product_variant_i" 
RENAME TO "idx_meta_catalog_product_variant";
