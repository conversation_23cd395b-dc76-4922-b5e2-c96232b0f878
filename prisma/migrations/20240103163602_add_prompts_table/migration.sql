-- CreateEnum
CREATE TYPE "PromptType" AS ENUM ('generate_whatsapp_message_template');

-- AlterTable
ALTER TABLE "companies" ADD COLUMN     "productDescription" TEXT;

-- CreateTable
CREATE TABLE "prompts" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "type" "PromptType" NOT NULL,
    "prompt_text" TEXT NOT NULL,

    CONSTRAINT "prompts_pkey" PRIMARY KEY ("id")
);
