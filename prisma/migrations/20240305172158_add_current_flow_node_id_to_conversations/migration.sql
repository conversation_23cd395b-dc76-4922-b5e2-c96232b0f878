/*
  Warnings:

  - Made the column `target_flow_node_id` on table `flow_triggers` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "flow_triggers" DROP CONSTRAINT "flow_triggers_target_flow_node_id_fkey";

-- AlterTable
ALTER TABLE "conversations" ADD COLUMN     "current_flow_node_id" TEXT;

-- AlterTable
ALTER TABLE "flow_triggers" ADD COLUMN     "invocation_count" INTEGER NOT NULL DEFAULT 0,
ALTER COLUMN "target_flow_node_id" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_current_flow_node_id_fkey" FOREIGN KEY ("current_flow_node_id") REFERENCES "flow_nodes"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "flow_triggers" ADD CONSTRAINT "flow_triggers_target_flow_node_id_fkey" FOREIGN KEY ("target_flow_node_id") REFERENCES "flow_nodes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
