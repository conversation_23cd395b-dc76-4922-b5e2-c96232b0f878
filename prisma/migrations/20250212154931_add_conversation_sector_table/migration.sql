-- AlterTable
ALTER TABLE "conversation_categories" ADD COLUMN     "conversation_sector_id" TEXT;

-- CreateTable
CREATE TABLE "conversation_sectors" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "position" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    "company_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "conversation_sectors_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "conversation_sectors_company_id_name_key" ON "conversation_sectors"("company_id", "name");

-- AddForeignKey
ALTER TABLE "conversation_categories" ADD CONSTRAINT "conversation_categories_conversation_sector_id_fkey" FOREIGN KEY ("conversation_sector_id") REFERENCES "conversation_sectors"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON>Key
ALTER TABLE "conversation_sectors" ADD CONSTRAINT "conversation_sectors_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
