/*
  Warnings:

  - Changed the type of `type` on the `flow_node_buttons` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `type` on the `message_template_buttons` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "MessageButtonType" AS ENUM ('QUICK_REPLY', 'URL');

-- AlterTable
ALTER TABLE "flow_node_buttons" DROP COLUMN "type",
ADD COLUMN     "type" "MessageButtonType" NOT NULL;

-- AlterTable
ALTER TABLE "message_template_buttons" DROP COLUMN "type",
ADD COLUMN     "type" "MessageButtonType" NOT NULL;

-- DropEnum
DROP TYPE "ButtonType";

-- CreateIndex
CREATE UNIQUE INDEX "flow_node_buttons_flow_node_id_text_type_key" ON "flow_node_buttons"("flow_node_id", "text", "type");
