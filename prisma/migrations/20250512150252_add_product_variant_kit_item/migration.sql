-- CreateTable
CREATE TABLE "product_variant_kit_items" (
    "id" TEXT NOT NULL,
    "product_variant_id" TEXT NOT NULL,
    "kit_item_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "product_variant_kit_items_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "product_variant_kit_items_product_variant_id_idx" ON "product_variant_kit_items"("product_variant_id");

-- AddForeignKey
ALTER TABLE "product_variant_kit_items" ADD CONSTRAINT "product_variant_kit_items_product_variant_id_fkey" FOREIGN KEY ("product_variant_id") REFERENCES "product_variants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
