-- CreateTable
CREATE TABLE "prompt_completion_threads" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "prompt_id" TEXT NOT NULL,
    "user_id" TEXT
);

-- CreateTable
CREATE TABLE "prompt_completitions" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "prompt_completion_thread_id" TEXT NOT NULL,
    "prompt_text" TEXT NOT NULL,
    "completition" TEXT NOT NULL,
    "prompt_tokens" INTEGER,
    "completition_tokens" INTEGER,
    "finish_reason" TEXT
);

-- CreateIndex
CREATE UNIQUE INDEX "prompt_completion_threads_id_key" ON "prompt_completion_threads"("id");

-- CreateIndex
CREATE UNIQUE INDEX "prompt_completitions_id_key" ON "prompt_completitions"("id");

-- AddForeignKey
ALTER TABLE "prompt_completion_threads" ADD CONSTRAINT "prompt_completion_threads_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prompt_completion_threads" ADD CONSTRAINT "prompt_completion_threads_prompt_id_fkey" FOREIGN KEY ("prompt_id") REFERENCES "prompts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prompt_completitions" ADD CONSTRAINT "prompt_completitions_prompt_completion_thread_id_fkey" FOREIGN KEY ("prompt_completion_thread_id") REFERENCES "prompt_completion_threads"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
