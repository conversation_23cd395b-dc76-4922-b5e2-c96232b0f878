/*
  Warnings:

  - The values [cart_abandonment] on the enum `AutomationTypeSlug` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "AutomationTypeSlug_new" AS ENUM ('send_tracking_code', 'cart_recovery');
ALTER TABLE "automation_types" ALTER COLUMN "slug" TYPE "AutomationTypeSlug_new" USING ("slug"::text::"AutomationTypeSlug_new");
ALTER TYPE "AutomationTypeSlug" RENAME TO "AutomationTypeSlug_old";
ALTER TYPE "AutomationTypeSlug_new" RENAME TO "AutomationTypeSlug";
DROP TYPE "AutomationTypeSlug_old";
COMMIT;
