/*
  Warnings:

  - The values [EMAIL_TEMPLATES] on the enum `AppModule` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "AppModule_new" AS ENUM ('HOME', 'CHAT', 'CUSTOMERS', 'CAMPAIGNS', 'TEMPLATES', 'AUTOMATIONS', 'SETTINGS', 'REPORTS', 'DEBUG_TOOLS');
ALTER TABLE "roles" ALTER COLUMN "app_module_permissions" DROP DEFAULT;
ALTER TABLE "roles" ALTER COLUMN "app_module_permissions" TYPE "AppModule_new"[] USING ("app_module_permissions"::text::"AppModule_new"[]);
ALTER TYPE "AppModule" RENAME TO "AppModule_old";
ALTER TYPE "AppModule_new" RENAME TO "AppModule";
DROP TYPE "AppModule_old";
ALTER TABLE "roles" ALTER COLUMN "app_module_permissions" SET DEFAULT ARRAY[]::"AppModule"[];
COMMIT;
