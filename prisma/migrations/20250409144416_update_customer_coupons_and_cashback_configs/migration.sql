/*
  Warnings:

  - You are about to drop the column `creation_template_id` on the `cashback_configs` table. All the data in the column will be lost.
  - You are about to drop the column `last_day_template_id` on the `cashback_configs` table. All the data in the column will be lost.
  - You are about to drop the column `reminder_template_id` on the `cashback_configs` table. All the data in the column will be lost.
  - You are about to alter the column `min_order_value` on the `cashback_configs` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Integer`.
  - You are about to alter the column `max_order_value` on the `cashback_configs` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Integer`.
  - You are about to drop the column `scheduled_send_time` on the `coupons` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `coupons` table. All the data in the column will be lost.
  - You are about to drop the column `usage_per_customer` on the `coupons` table. All the data in the column will be lost.
  - Added the required column `creation_message_template_id` to the `cashback_configs` table without a default value. This is not possible if the table is not empty.
  - Added the required column `last_day_message_template_id` to the `cashback_configs` table without a default value. This is not possible if the table is not empty.
  - Made the column `min_order_value` on table `cashback_configs` required. This step will fail if there are existing NULL values in that column.
  - Made the column `max_order_value` on table `cashback_configs` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "CouponStep" AS ENUM ('pending', 'creation_whatsapp_sent', 'reminder_whatsapp_sent', 'last_day_whatsapp_sent');

-- DropForeignKey
ALTER TABLE "cashback_configs" DROP CONSTRAINT "cashback_configs_creation_template_id_fkey";

-- DropForeignKey
ALTER TABLE "cashback_configs" DROP CONSTRAINT "cashback_configs_last_day_template_id_fkey";

-- DropForeignKey
ALTER TABLE "cashback_configs" DROP CONSTRAINT "cashback_configs_reminder_template_id_fkey";

-- AlterTable
ALTER TABLE "cashback_configs" DROP COLUMN "creation_template_id",
DROP COLUMN "last_day_template_id",
DROP COLUMN "reminder_template_id",
ADD COLUMN     "creation_message_template_id" TEXT NOT NULL,
ADD COLUMN     "daily_message_limit_on_whatsapp" INTEGER NOT NULL DEFAULT 100,
ADD COLUMN     "last_day_message_template_id" TEXT NOT NULL,
ADD COLUMN     "reminder_message_template_id" TEXT,
ALTER COLUMN "min_order_value" SET NOT NULL,
ALTER COLUMN "min_order_value" SET DATA TYPE INTEGER,
ALTER COLUMN "max_order_value" SET NOT NULL,
ALTER COLUMN "max_order_value" SET DATA TYPE INTEGER;

-- AlterTable
ALTER TABLE "coupons" DROP COLUMN "scheduled_send_time",
DROP COLUMN "status",
DROP COLUMN "usage_per_customer",
ADD COLUMN     "usage_limit_per_customer" INTEGER;

-- AlterTable
ALTER TABLE "customer_coupons" ADD COLUMN     "coupon_step" "CouponStep" NOT NULL DEFAULT 'pending',
ADD COLUMN     "is_active" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "scheduled_send_time" TIMESTAMP(3);

-- DropEnum
DROP TYPE "StatusCoupon";

-- AddForeignKey
ALTER TABLE "cashback_configs" ADD CONSTRAINT "cashback_configs_creation_message_template_id_fkey" FOREIGN KEY ("creation_message_template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cashback_configs" ADD CONSTRAINT "cashback_configs_reminder_message_template_id_fkey" FOREIGN KEY ("reminder_message_template_id") REFERENCES "message_templates"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cashback_configs" ADD CONSTRAINT "cashback_configs_last_day_message_template_id_fkey" FOREIGN KEY ("last_day_message_template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
