/*
  Warnings:

  - You are about to drop the column `message_id` on the `sms_messages` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[smsCampaignId,customer_id]` on the table `campaign_recipients` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `sms_id` to the `sms_messages` table without a default value. This is not possible if the table is not empty.
  - Made the column `sms_campaign_id` on table `sms_messages` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "sms_messages" DROP CONSTRAINT "sms_messages_sms_campaign_id_fkey";

-- AlterTable
ALTER TABLE "short_url_clicks" ADD COLUMN     "user_agent" TEXT;

-- AlterTable
ALTER TABLE "sms_messages" DROP COLUMN "message_id",
ADD COLUMN     "sms_id" TEXT NOT NULL,
ALTER COLUMN "sms_campaign_id" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "campaign_recipients_smsCampaignId_customer_id_key" ON "campaign_recipients"("smsCampaignId", "customer_id");

-- AddForeignKey
ALTER TABLE "sms_messages" ADD CONSTRAINT "sms_messages_sms_campaign_id_fkey" FOREIGN KEY ("sms_campaign_id") REFERENCES "sms_campaigns"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
