-- CreateEnum
CREATE TYPE "CustomFieldDataType" AS ENUM ('string', 'number', 'boolean', 'date');

-- CreateEnum
CREATE TYPE "CustomFieldTable" AS ENUM ('customer');

-- AlterTable
ALTER TABLE "customers" ADD COLUMN     "customFields" JSONB;

-- CreateTable
CREATE TABLE "custom_fields" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "table" "CustomFieldTable" NOT NULL,
    "name" TEXT NOT NULL,
    "data_type" "CustomFieldDataType" NOT NULL,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "custom_fields_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "custom_fields" ADD CONSTRAINT "custom_fields_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
