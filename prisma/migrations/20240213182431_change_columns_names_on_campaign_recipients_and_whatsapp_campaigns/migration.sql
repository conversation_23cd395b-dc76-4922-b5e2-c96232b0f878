/*
  Warnings:

  - You are about to drop the column `smsCampaignId` on the `campaign_recipients` table. All the data in the column will be lost.
  - You are about to drop the column `template_id` on the `whatsapp_campaigns` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[sms_campaign_id,customer_id]` on the table `campaign_recipients` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `message_template_id` to the `whatsapp_campaigns` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "campaign_recipients" DROP CONSTRAINT "campaign_recipients_smsCampaignId_fkey";

-- DropForeignKey
ALTER TABLE "whatsapp_campaigns" DROP CONSTRAINT "whatsapp_campaigns_template_id_fkey";

-- DropIndex
DROP INDEX "campaign_recipients_smsCampaignId_customer_id_key";

ALTER TABLE "campaign_recipients"
RENAME COLUMN "smsCampaignId" TO "sms_campaign_id";

-- AlterTable
ALTER TABLE "whatsapp_campaigns"
RENAME COLUMN "template_id" TO "message_template_id";

-- CreateIndex
CREATE UNIQUE INDEX "campaign_recipients_sms_campaign_id_customer_id_key" ON "campaign_recipients"("sms_campaign_id", "customer_id");

-- AddForeignKey
ALTER TABLE "whatsapp_campaigns" ADD CONSTRAINT "whatsapp_campaigns_message_template_id_fkey" FOREIGN KEY ("message_template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_recipients" ADD CONSTRAINT "campaign_recipients_sms_campaign_id_fkey" FOREIGN KEY ("sms_campaign_id") REFERENCES "sms_campaigns"("id") ON DELETE SET NULL ON UPDATE CASCADE;
