/*
  Warnings:

  - You are about to drop the column `sms_campaign_id` on the `campaign_recipients` table. All the data in the column will be lost.
  - The `status` column on the `sms_campaigns` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `recipient_phone_number_id` on the `sms_messages` table. All the data in the column will be lost.
  - Added the required column `recipientPhoneNumberId` to the `sms_messages` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "SmsCampaignStatus" AS ENUM ('in_progress', 'completed', 'scheduled', 'canceled', 'failed');

-- DropForeignKey
ALTER TABLE "campaign_recipients" DROP CONSTRAINT "campaign_recipients_sms_campaign_id_fkey";

-- AlterTable
ALTER TABLE "campaign_recipients" DROP COLUMN "sms_campaign_id",
ADD COLUMN     "smsCampaignId" TEXT;

-- AlterTable
ALTER TABLE "sms_campaigns" DROP COLUMN "status",
ADD COLUMN     "status" "SmsCampaignStatus" NOT NULL DEFAULT E'in_progress';

-- AlterTable
ALTER TABLE "sms_messages" DROP COLUMN "recipient_phone_number_id",
ADD COLUMN     "message_id" TEXT,
ADD COLUMN     "recipientPhoneNumberId" TEXT NOT NULL;

-- AddForeignKey
ALTER TABLE "campaign_recipients" ADD CONSTRAINT "campaign_recipients_smsCampaignId_fkey" FOREIGN KEY ("smsCampaignId") REFERENCES "sms_campaigns"("id") ON DELETE SET NULL ON UPDATE CASCADE;
