-- CreateTable
CREATE TABLE "faq" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "question" TEXT NOT NULL,
    "answer" TEXT NOT NULL,
    "search_vector" tsvector,
    "company_id" TEXT NOT NULL,

    CONSTRAINT "faq_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "faq" ADD CONSTRAINT "faq_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Criar a função para atualizar o search_vector
CREATE OR REPLACE FUNCTION faq_update_search_vector() RETURNS trigger AS $$
BEGIN
  NEW.search_vector :=
    to_tsvector('portuguese', coalesce(NEW.question, '') || ' ' || coalesce(NEW.answer, ''));
  RETURN NEW;
END
$$ LANGUAGE plpgsql;

-- Criar o trigger para chamar a função antes de INSERT ou UPDATE
CREATE TRIGGER faq_vector_update
BEFORE INSERT OR UPDATE ON "faq"
FOR EACH ROW EXECUTE FUNCTION faq_update_search_vector();

-- Criar índice GIN na coluna search_vector para performance
CREATE INDEX "faq_search_vector_idx" ON "faq" USING GIN("search_vector");

-- Criar a função de consulta (por causa do n8n)
CREATE OR REPLACE FUNCTION search_faq(
    p_raw_query TEXT,  -- Se refere a pergunta do usuário
    p_limit INTEGER     -- Limite de resultados esperado para evitar extrapolar a memória do agente
)
RETURNS TABLE(
    questions TEXT,
    answers TEXT,
    company_id TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    relevance REAL
)
LANGUAGE sql
STABLE -- A função não modifica o banco de dados e retorna os mesmos resultados
       -- para os mesmos argumentos dentro de uma varredura de tabela.
AS $$
    WITH UserInput AS (
        SELECT p_raw_query::text AS raw_query
    ),
    GreetingPattern AS (
        -- Padrão Regex para detectar saudações no início da string (case-insensitive)
        SELECT '^(olá|oi|bom dia|boa tarde|boa noite)\s*,?\s*'::text AS pattern
    ),
    FinalTsQuery AS (
        SELECT
            CASE
                -- Se a consulta bruta corresponder ao padrão de saudação...
                WHEN ui.raw_query ~* gp.pattern THEN
                    -- ...remove a saudação e converte o resto para tsquery
                    plainto_tsquery('portuguese', regexp_replace(ui.raw_query, gp.pattern, '', 'i'))
                ELSE
                    -- ...senão, converte a consulta bruta inteira para tsquery
                    plainto_tsquery('portuguese', ui.raw_query)
            END AS ts_query_result
        FROM UserInput ui, GreetingPattern gp
    )
    SELECT
        f.question,
        f.answer,
        f.company_id,
        f.created_at,
        f.updated_at,
        -- Calcula a relevância usando ts_rank_cd
        ts_rank_cd(f.search_vector, ftq.ts_query_result)::REAL AS relevance
    FROM
        faq f,
        FinalTsQuery ftq
    WHERE
        -- Garante que a tsquery não seja nula (pode acontecer com strings vazias/stop words)
        ftq.ts_query_result IS NOT NULL
        -- A condição principal da busca full-text: o vetor do documento corresponde à consulta
        AND f.search_vector @@ ftq.ts_query_result
    ORDER BY
        relevance DESC -- Ordena pelos mais relevantes primeiro
    LIMIT p_limit;     -- Limita o número de resultados
$$;