-- CreateEnum
CREATE TYPE "GupshupTemplateType" AS ENUM ('TEXT', 'IMAGE', 'VIDEO', 'DOCUMENT');

-- CreateTable
CREATE TABLE "gupshup_templates" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "message_template_id" TEXT NOT NULL,
    "element_name" TEXT NOT NULL,
    "language_code" TEXT NOT NULL,
    "category" "WhatsappTemplateCategory" NOT NULL,
    "templateType" "GupshupTemplateType" NOT NULL,
    "content" TEXT NOT NULL,
    "buttons" JSONB,
    "example" TEXT NOT NULL,
    "vertical" TEXT NOT NULL,
    "header" TEXT NOT NULL,
    "footer" TEXT NOT NULL,
    "enable_sample" BOOLEAN NOT NULL,
    "example_header" TEXT NOT NULL,

    CONSTRAINT "gupshup_templates_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "gupshup_templates_message_template_id_key" ON "gupshup_templates"("message_template_id");

-- AddForeignKey
ALTER TABLE "gupshup_templates" ADD CONSTRAINT "gupshup_templates_message_template_id_fkey" FOREIGN KEY ("message_template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
