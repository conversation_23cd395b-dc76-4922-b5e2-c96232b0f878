/*
  Warnings:

  - Added the required column `category` to the `message_templates` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "MessageTemplateCategory" AS ENUM ('TRANSACTIONAL', 'MARKETING', 'OTP');

-- AlterTable
ALTER TABLE "message_templates" ADD COLUMN     "category" "MessageTemplateCategory" NOT NULL DEFAULT 'MARKETING';

ALTER TABLE "message_templates" ALTER COLUMN "category" DROP DEFAULT;
