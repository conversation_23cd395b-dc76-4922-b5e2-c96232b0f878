-- AlterTable: customer_coupons
ALTER TABLE "customer_coupons" 
ADD COLUMN "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- <PERSON><PERSON><PERSON> created_at e updated_at em customer_coupons com a data de criação do cupom relacionado,
-- para registros que estão com esses campos nulos ou com valores padrão iguais
UPDATE customer_coupons cc
SET 
  created_at = c.created_at,
  updated_at = c.created_at
FROM coupons c
WHERE cc.coupon_id = c.id
  AND (cc.created_at IS NULL OR cc.updated_at IS NULL OR cc.created_at = cc.updated_at);

-- AlterTable: orders
ALTER TABLE "orders" 
ADD COLUMN "cancel_reason" TEXT,
ADD COLUMN "cancelled_at" TIMESTAMP(3),
ADD COLUMN "is_subscription" BOOLEAN DEFAULT false,
ADD COLUMN "payment_methods" TEXT;
