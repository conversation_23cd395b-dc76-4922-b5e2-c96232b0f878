
ALTER TABLE "product_variants" ADD COLUMN     "description_search_vector" tsvector,
ADD COLUMN     "is_kit" BOOLEAN DEFAULT false,
ADD COLUMN     "specifications" J<PERSON>N<PERSON>,
ADD COLUMN      "keywords" TEXT DEFAULT '',
ADD COLUMN     "title_search_vector" tsvector,
ADD COLUMN     "keywords_search_vector" tsvector;

CREATE INDEX "product_variants_title_search_vector_idx" ON "product_variants" USING GIN ("title_search_vector");

CREATE INDEX "product_variants_description_search_vector_idx" ON "product_variants" USING GIN ("description_search_vector");

CREATE INDEX "product_variants_keywords_search_vector_idx" ON "product_variants" USING GIN ("keywords_search_vector");

CREATE EXTENSION IF NOT EXISTS unaccent;

CREATE TEXT SEARCH CONFIGURATION public.portuguese_unaccent (COPY = pg_catalog.portuguese);

ALTER TEXT SEARCH CONFIGURATION public.portuguese_unaccent
    ALTER MAPPING FOR hword, hword_part, word
    WITH unaccent, portuguese_stem;

CREATE OR REPLACE FUNCTION product_variants_update_search_vectors() RETURNS trigger AS $$
BEGIN
  NEW.title_search_vector :=
    to_tsvector('public.portuguese_unaccent', coalesce(NEW.title, ''));

  NEW.keywords_search_vector :=
    to_tsvector('public.portuguese_unaccent', coalesce(NEW.keywords, ''));

  NEW.description_search_vector :=
    to_tsvector('public.portuguese_unaccent', coalesce(NEW.description, ''));

  RETURN NEW;
END
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS product_variants_vector_update ON product_variants;

CREATE TRIGGER product_variants_vector_update
BEFORE INSERT OR UPDATE ON product_variants
FOR EACH ROW EXECUTE FUNCTION product_variants_update_search_vectors();