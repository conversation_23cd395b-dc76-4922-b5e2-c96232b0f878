-- AlterEnum
ALTER TYPE "LogType" ADD VALUE 'syncProducts';

-- CreateTable
CREATE TABLE "products" (
    "id" TEXT NOT NULL,
    "source_id" TEXT,
    "source" "SourceIntegration" NOT NULL DEFAULT 'unknown',
    "source_created_at" TIMESTAMP(3),
    "source_updated_at" TIMESTAMP(3),
    "status" TEXT,
    "metadata" JSONB,
    "company_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_variants" (
    "id" TEXT NOT NULL,
    "product_id" TEXT NOT NULL,
    "name" TEXT NOT NULL DEFAULT '',
    "description" TEXT NOT NULL DEFAULT '',
    "sku" TEXT,
    "source_id" TEXT,
    "barcode" TEXT,
    "price" INTEGER,
    "sale_price" INTEGER,
    "cost_price" INTEGER,
    "stock_quantity" INTEGER,
    "weight" INTEGER,
    "width" INTEGER,
    "height" INTEGER,
    "length" INTEGER,
    "image_url" TEXT,
    "url" TEXT,
    "variant_code" TEXT,
    "metadata" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "product_variants_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "products_company_id_idx" ON "products"("company_id");

-- CreateIndex
CREATE UNIQUE INDEX "product_variants_sku_key" ON "product_variants"("sku");

-- CreateIndex
CREATE INDEX "product_variants_product_id_idx" ON "product_variants"("product_id");

-- AddForeignKey
ALTER TABLE "products" ADD CONSTRAINT "products_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_variants" ADD CONSTRAINT "product_variants_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
