/*
  Warnings:

  - Added the required column `company_id` to the `customer_coupons` table without a default value. This is not possible if the table is not empty.
*/

-- AlterTable
ALTER TABLE "cashback_configs" ADD COLUMN "is_reminder_enabled" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "customer_coupons" ADD COLUMN "company_id" TEXT NOT NULL,
ADD COLUMN "is_used" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN "used_at" TIMESTAMP(3);

-- CreateIndex
CREATE INDEX "customer_coupons_scheduled_send_time_is_active_company_id_idx" ON "customer_coupons"("scheduled_send_time", "is_active", "company_id");

-- AddForeignKey
ALTER TABLE "customer_coupons" ADD CONSTRAINT "customer_coupons_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AlterTable
ALTER TABLE "messages" ADD COLUMN "customer_coupon_id" TEXT;

-- C<PERSON>Index (Índices adicionais)
CREATE INDEX "cashback_configs_company_id_integration_is_active_idx" ON "cashback_configs"("company_id", "integration", "is_active");

CREATE INDEX "coupons_generated_by_order_id_idx" ON "coupons"("generated_by_order_id");

-- AddForeignKey
ALTER TABLE "messages" ADD CONSTRAINT "messages_customer_coupon_id_fkey" FOREIGN KEY ("customer_coupon_id") REFERENCES "customer_coupons"("id") ON DELETE SET NULL ON UPDATE CASCADE;
