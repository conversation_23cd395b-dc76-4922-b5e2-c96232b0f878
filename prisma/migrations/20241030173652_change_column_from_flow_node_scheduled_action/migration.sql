/*
  Warnings:

  - You are about to drop the column `customer_id` on the `flow_node_scheduled_actions` table. All the data in the column will be lost.
  - Added the required column `conversation_id` to the `flow_node_scheduled_actions` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "flow_node_scheduled_actions" DROP CONSTRAINT "flow_node_scheduled_actions_customer_id_fkey";

-- AlterTable
ALTER TABLE "flow_node_scheduled_actions" DROP COLUMN "customer_id",
ADD COLUMN     "conversation_id" TEXT NOT NULL;

-- AddForeignKey
ALTER TABLE "flow_node_scheduled_actions" ADD CONSTRAINT "flow_node_scheduled_actions_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
