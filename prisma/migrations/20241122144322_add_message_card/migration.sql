-- CreateTable
CREATE TABLE "message_cards" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "card_index" INTEGER NOT NULL,
    "message_id" TEXT NOT NULL,
    "message_template_card_id" TEXT NOT NULL,

    CONSTRAINT "message_cards_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "message_cards" ADD CONSTRAINT "message_cards_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "messages"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddFore<PERSON>Key
ALTER TABLE "message_cards" ADD CONSTRAINT "message_cards_message_template_card_id_fkey" FOREIGN KEY ("message_template_card_id") REFERENCES "message_template_cards"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
