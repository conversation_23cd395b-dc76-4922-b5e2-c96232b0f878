-- AlterTable
ALTER TABLE "email_domains" ADD COLUMN     "external_id" TEXT,
ADD COLUMN     "sendgrid_subuser_id" TEXT;

-- CreateTable
CREATE TABLE "sendgrid_subusers" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "external_id" INTEGER NOT NULL,
    "username" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,

    CONSTRAINT "sendgrid_subusers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sendgrid_subuser_dedicated_ips" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "sendgrid_subuser_id" TEXT NOT NULL,
    "sendgrid_dedicated_ip_id" TEXT NOT NULL,

    CONSTRAINT "sendgrid_subuser_dedicated_ips_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sendgrid_dedicated_ips" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "ip" TEXT NOT NULL,

    CONSTRAINT "sendgrid_dedicated_ips_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "sendgrid_subuser_dedicated_ips_sendgrid_subuser_id_sendgrid_key" ON "sendgrid_subuser_dedicated_ips"("sendgrid_subuser_id", "sendgrid_dedicated_ip_id");

-- AddForeignKey
ALTER TABLE "email_domains" ADD CONSTRAINT "email_domains_sendgrid_subuser_id_fkey" FOREIGN KEY ("sendgrid_subuser_id") REFERENCES "sendgrid_subusers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sendgrid_subuser_dedicated_ips" ADD CONSTRAINT "sendgrid_subuser_dedicated_ips_sendgrid_subuser_id_fkey" FOREIGN KEY ("sendgrid_subuser_id") REFERENCES "sendgrid_subusers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sendgrid_subuser_dedicated_ips" ADD CONSTRAINT "sendgrid_subuser_dedicated_ips_sendgrid_dedicated_ip_id_fkey" FOREIGN KEY ("sendgrid_dedicated_ip_id") REFERENCES "sendgrid_dedicated_ips"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
