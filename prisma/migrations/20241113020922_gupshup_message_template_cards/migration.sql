-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "GupshupTemplateType" ADD VALUE 'LOCATION';
ALTER TYPE "GupshupTemplateType" ADD VALUE 'PRODUCT';
ALTER TYPE "GupshupTemplateType" ADD VALUE 'CATALOG';
ALTER TYPE "GupshupTemplateType" ADD VALUE 'LTO';
ALTER TYPE "GupshupTemplateType" ADD VALUE 'CAROUSEL';

-- CreateTable
CREATE TABLE "gupshup_cards" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "headerType" "MediaType" NOT NULL,
    "media_url" TEXT,
    "media_id" TEXT,
    "example_media" TEXT,
    "body" TEXT NOT NULL,
    "sample_text" TEXT,
    "buttons" JSONB,
    "gupshup_template_id" TEXT NOT NULL,

    CONSTRAINT "gupshup_cards_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "gupshup_cards" ADD CONSTRAINT "gupshup_cards_gupshup_template_id_fkey" FOREIGN KEY ("gupshup_template_id") REFERENCES "gupshup_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
