-- AlterEnum
ALTER TYPE "AppModule" ADD VALUE 'PRODUCTS';

-- AlterTable
ALTER TABLE "product_variants" ADD COLUMN     "source_created_at" TIMESTAMP(3),
ADD COLUMN     "source_updated_at" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "products" ADD COLUMN     "integrations_config_id" TEXT;

-- AddForeignKey
ALTER TABLE "products" ADD CONSTRAINT "products_integrations_config_id_fkey" FOREIGN KEY ("integrations_config_id") REFERENCES "integrations_config"("id") ON DELETE SET NULL ON UPDATE CASCADE;
