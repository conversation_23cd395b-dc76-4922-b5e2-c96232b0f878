-- Create<PERSON><PERSON>
CREATE TYPE "EmailTemplateType" AS ENUM ('MARKETING', 'TRANSACTIONAL', 'ABANDONED_CART', 'TRACKING_CODE', 'ORDER_STATUS_UPDATE');

-- AlterEnum
ALTER TYPE "EmailCategory" ADD VALUE 'UTILITY';

-- AlterTable
ALTER TABLE "automations" ADD COLUMN     "daily_email_limit" INTEGER,
ADD COLUMN     "email_template_args" JSONB,
ADD COLUMN     "email_template_id" TEXT,
ADD COLUMN     "monthly_email_limit" INTEGER;

-- AlterTable
ALTER TABLE "email_campaigns" ADD COLUMN     "automation_id" TEXT;

-- AlterTable
ALTER TABLE "email_templates" ADD COLUMN     "type" "EmailTemplateType" NOT NULL DEFAULT 'MARKETING';

-- AlterTable
ALTER TABLE "emails" ADD COLUMN     "automation_id" TEXT;

-- AlterTable
ALTER TABLE "order_status_history" ADD COLUMN     "is_email_sent" BOOLEAN NOT NULL DEFAULT false;

-- AddF<PERSON><PERSON>Key
ALTER TABLE "automations" ADD CONSTRAINT "automations_email_template_id_fkey" FOREIGN KEY ("email_template_id") REFERENCES "email_templates"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "emails" ADD CONSTRAINT "emails_automation_id_fkey" FOREIGN KEY ("automation_id") REFERENCES "automations"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_campaigns" ADD CONSTRAINT "email_campaigns_automation_id_fkey" FOREIGN KEY ("automation_id") REFERENCES "automations"("id") ON DELETE SET NULL ON UPDATE CASCADE;
