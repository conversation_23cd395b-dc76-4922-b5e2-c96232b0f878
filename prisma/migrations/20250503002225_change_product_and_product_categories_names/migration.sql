ALTER TABLE "order_items" DROP CONSTRAINT IF EXISTS "order_items_product_id_fkey";
ALTER TABLE "product_categories" DROP CONSTRAINT IF EXISTS "product_categories_category_id_fkey";
ALTER TABLE "product_categories" DROP CONSTRAINT IF EXISTS "product_categories_product_id_fkey";
ALTER TABLE "products" DROP CONSTRAINT IF EXISTS "products_company_id_fkey";
DROP INDEX IF EXISTS "products_company_id_idx";

ALTER TABLE "products" RENAME TO "order_products";
ALTER TABLE "product_categories" RENAME TO "order_product_categories";

ALTER TABLE "order_items" ALTER COLUMN "product_id" DROP NOT NULL;

ALTER TABLE "order_items" ADD CONSTRAINT "order_items_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "order_products"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "order_products" ADD CONSTRAINT "order_products_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
CREATE INDEX "order_products_company_id_idx" ON "order_products"("company_id");
ALTER TABLE "order_product_categories" ADD CONSTRAINT "order_product_categories_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "order_product_categories" ADD CONSTRAINT "order_product_categories_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "order_products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "order_product_categories" RENAME CONSTRAINT "product_categories_pkey" TO "order_product_categories_pkey";
ALTER TABLE "order_products" RENAME CONSTRAINT "products_pkey" TO "order_products_pkey";
