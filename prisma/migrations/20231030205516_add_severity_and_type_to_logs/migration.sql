/*
  Warnings:

  - The values [shopifyOrderSync,cartRecovery] on the enum `LogSource` will be removed. If these variants are still used in the database, this will fail.
  - The values [error,info,warning] on the enum `LogType` will be removed. If these variants are still used in the database, this will fail.
  - Added the required column `severity` to the `logs` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "LogSeverity" AS ENUM ('error', 'info', 'warning');

-- AlterEnum
BEGIN;
CREATE TYPE "LogSource_new" AS ENUM ('gupshup', 'internal', 'vtex', 'shopify');
ALTER TABLE "logs" ALTER COLUMN "source" TYPE "LogSource_new" USING ("source"::text::"LogSource_new");
ALTER TYPE "LogSource" RENAME TO "LogSource_old";
ALTER TYPE "LogSource_new" RENAME TO "LogSource";
DROP TYPE "LogSource_old";
COMMIT;

-- Alter<PERSON><PERSON>
BEGIN;
CREATE TYPE "LogType_new" AS ENUM ('syncOrders', 'sendCartRecoveryMessages');
ALTER TABLE "logs" ALTER COLUMN "type" TYPE "LogType_new" USING ("type"::text::"LogType_new");
ALTER TYPE "LogType" RENAME TO "LogType_old";
ALTER TYPE "LogType_new" RENAME TO "LogType";
DROP TYPE "LogType_old";
COMMIT;

-- AlterTable
ALTER TABLE "logs" ADD COLUMN     "severity" "LogSeverity" NOT NULL,
ALTER COLUMN "type" DROP NOT NULL;
