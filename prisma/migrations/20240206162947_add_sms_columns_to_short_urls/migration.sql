/*
  Warnings:

  - Added the required column `message_template_id` to the `sms_messages` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "CommunicationChannel" AS ENUM ('whatsapp', 'sms');

-- DropForeignKey
ALTER TABLE "short_urls" DROP CONSTRAINT "short_urls_message_id_fkey";

-- AlterTable
ALTER TABLE "message_templates" ADD COLUMN     "communication_channel" "CommunicationChannel" NOT NULL DEFAULT E'whatsapp',
ALTER COLUMN "gupshup_template_id" DROP NOT NULL;

-- AlterTable
ALTER TABLE "short_urls" ADD COLUMN     "sms_message_id" TEXT,
ALTER COLUMN "message_id" DROP NOT NULL;

-- AlterTable
ALTER TABLE "sms_messages" ADD COLUMN     "message_template_id" TEXT NOT NULL;

-- AddForeignKey
ALTER TABLE "short_urls" ADD CONSTRAINT "short_urls_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "messages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "short_urls" ADD CONSTRAINT "short_urls_sms_message_id_fkey" FOREIGN KEY ("sms_message_id") REFERENCES "sms_messages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sms_messages" ADD CONSTRAINT "sms_messages_message_template_id_fkey" FOREIGN KEY ("message_template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
