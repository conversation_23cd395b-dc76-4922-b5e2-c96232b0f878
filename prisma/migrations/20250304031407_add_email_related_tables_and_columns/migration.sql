-- Create<PERSON>num
CREATE TYPE "EmailDomainVerificationStatus" AS ENUM ('pending', 'success', 'failed');

-- CreateEnum
CREATE TYPE "CustomerEmailState" AS ENUM ('pending', 'verified', 'opted_out');

-- CreateEnum
CREATE TYPE "EmailTemplateCategory" AS ENUM ('MARKETING', 'TRANSACTIONAL');

-- CreateEnum
CREATE TYPE "EmailDomainCategory" AS ENUM ('MARKETING', 'TRANSACTIONAL');

-- CreateEnum
CREATE TYPE "EmailStatus" AS ENUM ('sent', 'failed', 'opened', 'engaged', 'tagged_as_spam');

-- Create<PERSON>num
CREATE TYPE "EmailCampaignStatus" AS ENUM ('in_progress', 'completed', 'interrupted', 'scheduled', 'canceled', 'failed');

-- AlterTable
ALTER TABLE "campaign_recipients" ADD COLUMN     "email_campaign_id" TEXT;

-- AlterTable
ALTER TABLE "companies" ADD COLUMN     "daily_email_limit" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "monthly_email_limit" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "customers" ADD COLUMN     "email_state" "CustomerEmailState" NOT NULL DEFAULT 'pending';

-- AlterTable
ALTER TABLE "short_urls" ADD COLUMN     "email_id" TEXT;

-- CreateTable
CREATE TABLE "email_templates" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "html" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "category" "EmailTemplateCategory" NOT NULL DEFAULT 'MARKETING',

    CONSTRAINT "email_templates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "email_domains" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "company_id" TEXT NOT NULL,
    "domain" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "category" "EmailDomainCategory" NOT NULL,
    "mail_from_domain" TEXT NOT NULL,
    "verification_token" TEXT,
    "domain_verification_status" "EmailDomainVerificationStatus" NOT NULL DEFAULT 'pending',
    "last_verified_at" TIMESTAMP(3),
    "dns_records" JSONB,
    "configuration_set" TEXT,

    CONSTRAINT "email_domains_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "emails" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "external_id" TEXT,
    "customer_id" TEXT NOT NULL,
    "recipient_email" TEXT NOT NULL,
    "sender_email" TEXT NOT NULL,
    "email_template_id" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "html" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "status" "EmailStatus" NOT NULL DEFAULT 'sent',
    "failure_reason" TEXT,
    "email_campaign_id" TEXT,

    CONSTRAINT "emails_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "email_campaigns" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "email_template_id" TEXT NOT NULL,
    "total_recipients" INTEGER NOT NULL,
    "total_processed" INTEGER NOT NULL DEFAULT 0,
    "filter_criteria" TEXT,
    "status" "EmailCampaignStatus" NOT NULL DEFAULT 'scheduled',
    "created_by_user_id" TEXT,
    "scheduled_execution_time" TIMESTAMP(3),
    "scheduled_job_id" TEXT,
    "error_message" TEXT,
    "template_args" JSONB,

    CONSTRAINT "email_campaigns_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "email_templates_company_id_name_key" ON "email_templates"("company_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "email_domains_domain_key" ON "email_domains"("domain");

-- CreateIndex
CREATE UNIQUE INDEX "email_domains_address_key" ON "email_domains"("address");

-- CreateIndex
CREATE UNIQUE INDEX "email_domains_company_id_category_key" ON "email_domains"("company_id", "category");

-- CreateIndex
CREATE UNIQUE INDEX "emails_external_id_key" ON "emails"("external_id");

-- CreateIndex
CREATE INDEX "emails_email_campaign_id_idx" ON "emails"("email_campaign_id");

-- CreateIndex
CREATE INDEX "email_campaigns_company_id_idx" ON "email_campaigns"("company_id");

-- AddForeignKey
ALTER TABLE "email_templates" ADD CONSTRAINT "email_templates_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_domains" ADD CONSTRAINT "email_domains_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "emails" ADD CONSTRAINT "emails_email_campaign_id_fkey" FOREIGN KEY ("email_campaign_id") REFERENCES "email_campaigns"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "emails" ADD CONSTRAINT "emails_email_template_id_fkey" FOREIGN KEY ("email_template_id") REFERENCES "email_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "short_urls" ADD CONSTRAINT "short_urls_email_id_fkey" FOREIGN KEY ("email_id") REFERENCES "emails"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_campaigns" ADD CONSTRAINT "email_campaigns_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_campaigns" ADD CONSTRAINT "email_campaigns_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "email_campaigns" ADD CONSTRAINT "email_campaigns_email_template_id_fkey" FOREIGN KEY ("email_template_id") REFERENCES "email_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_recipients" ADD CONSTRAINT "campaign_recipients_email_campaign_id_fkey" FOREIGN KEY ("email_campaign_id") REFERENCES "email_campaigns"("id") ON DELETE SET NULL ON UPDATE CASCADE;
