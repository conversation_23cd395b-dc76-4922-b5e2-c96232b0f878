/*
  Warnings:

  - Made the column `source_id` on table `orders` required. This step will fail if there are existing NULL values in that column.
  - Made the column `value` on table `orders` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "ColumnMappingConfigTable" AS ENUM ('orders');

-- AlterTable
ALTER TABLE "orders" ALTER COLUMN "source_id" SET NOT NULL,
ALTER COLUMN "value" SET NOT NULL;

-- CreateTable
CREATE TABLE "column_mapping_configs" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "table" "ColumnMappingConfigTable" NOT NULL,
    "mapping" JSONB NOT NULL,

    CONSTRAINT "column_mapping_configs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "socket_io_attachments" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "payload" BYTEA NOT NULL,

    CONSTRAINT "socket_io_attachments_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "column_mapping_configs_company_id_table_key" ON "column_mapping_configs"("company_id", "table");

-- AddForeignKey
ALTER TABLE "column_mapping_configs" ADD CONSTRAINT "column_mapping_configs_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
