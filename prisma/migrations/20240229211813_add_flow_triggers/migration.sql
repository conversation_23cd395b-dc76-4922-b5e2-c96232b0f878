/*
  Warnings:

  - A unique constraint covering the columns `[company_id,name]` on the table `flows` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "FlowTriggerType" AS ENUM ('exact_match', 'keyword_match');

-- AlterEnum
ALTER TYPE "FlowNodeType" ADD VALUE 'trigger';

-- AlterTable
ALTER TABLE "flows" ADD COLUMN     "is_active" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "flow_triggers" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "type" "FlowTriggerType" NOT NULL,
    "text" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,

    CONSTRAINT "flow_triggers_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "flow_triggers_company_id_type_text_key" ON "flow_triggers"("company_id", "type", "text");

-- CreateIndex
CREATE UNIQUE INDEX "flows_company_id_name_key" ON "flows"("company_id", "name");

-- AddForeignKey
ALTER TABLE "flow_triggers" ADD CONSTRAINT "flow_triggers_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
