/*
  Warnings:

  - Changed the type of `slug` on the `automation_types` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "AutomationTypeSlug" AS ENUM ('send_tracking_code');

-- AlterTable
ALTER TABLE "automation_types" DROP COLUMN "slug",
ADD COLUMN     "slug" "AutomationTypeSlug" NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "automation_types_slug_key" ON "automation_types"("slug");
