/*
  Warnings:

  - A unique constraint covering the columns `[first_reply_id]` on the table `messages` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "messages" ADD COLUMN     "first_reply_id" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "messages_first_reply_id_key" ON "messages"("first_reply_id");

-- AddForeignKey
ALTER TABLE "messages" ADD CONSTRAINT "messages_first_reply_id_fkey" FOREIGN KEY ("first_reply_id") REFERENCES "messages"("id") ON DELETE SET NULL ON UPDATE CASCADE;
