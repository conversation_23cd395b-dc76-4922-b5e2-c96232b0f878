/*
  Warnings:

  - The `category` column on the `email_templates` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Changed the type of `category` on the `email_domains` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "EmailCategory" AS ENUM ('MARKETING', 'TRANSACTIONAL');

-- AlterTable
ALTER TABLE "email_domains" DROP COLUMN "category",
ADD COLUMN     "category" "EmailCategory" NOT NULL;

-- AlterTable
ALTER TABLE "email_templates" DROP COLUMN "category",
ADD COLUMN     "category" "EmailCategory" NOT NULL DEFAULT 'MARKETING';

-- DropEnum
DROP TYPE "EmailDomainCategory";

-- DropEnum
DROP TYPE "EmailTemplateCategory";

-- CreateIndex
CREATE UNIQUE INDEX "email_domains_company_id_category_key" ON "email_domains"("company_id", "category");
