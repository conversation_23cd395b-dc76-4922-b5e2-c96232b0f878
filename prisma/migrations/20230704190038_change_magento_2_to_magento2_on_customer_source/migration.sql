/*
  Warnings:

  - The values [magento_2_ecommerce] on the enum `CustomerSource` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "CustomerSource_new" AS ENUM ('hubspot_crm', 'magento2_ecommerce', 'vtex_ecommerce', 'file_import', 'direct_message', 'unknown');
ALTER TABLE "customers" ALTER COLUMN "source" DROP DEFAULT;
ALTER TABLE "customers" ALTER COLUMN "source" TYPE "CustomerSource_new" USING ("source"::text::"CustomerSource_new");
ALTER TYPE "CustomerSource" RENAME TO "CustomerSource_old";
ALTER TYPE "CustomerSource_new" RENAME TO "CustomerSource";
DROP TYPE "CustomerSource_old";
ALTER TABLE "customers" ALTER COLUMN "source" SET DEFAULT 'unknown';
COMMIT;
