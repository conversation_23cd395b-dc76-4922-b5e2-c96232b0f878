/*
  Warnings:

  - You are about to drop the column `completition` on the `prompt_completitions` table. All the data in the column will be lost.
  - You are about to drop the column `prompt_completion_thread_id` on the `prompt_completitions` table. All the data in the column will be lost.
  - You are about to drop the `prompt_completion_threads` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `prompts` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `prompt_thread_id` to the `prompt_completitions` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "InitialPromptType" AS ENUM ('generate_whatsapp_message_template');

-- DropForeignKey
ALTER TABLE "prompt_completion_threads" DROP CONSTRAINT "prompt_completion_threads_prompt_id_fkey";

-- DropForeignKey
ALTER TABLE "prompt_completion_threads" DROP CONSTRAINT "prompt_completion_threads_user_id_fkey";

-- DropForeign<PERSON>ey
ALTER TABLE "prompt_completitions" DROP CONSTRAINT "prompt_completitions_prompt_completion_thread_id_fkey";

-- AlterTable
ALTER TABLE "prompt_completitions" DROP COLUMN "completition",
DROP COLUMN "prompt_completion_thread_id",
ADD COLUMN     "completition_text" TEXT,
ADD COLUMN     "prompt_thread_id" TEXT NOT NULL;

-- DropTable
DROP TABLE "prompt_completion_threads";

-- DropTable
DROP TABLE "prompts";

-- DropEnum
DROP TYPE "PromptType";

-- CreateTable
CREATE TABLE "prompt_threads" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "initial_prompt_id" TEXT NOT NULL,
    "user_id" TEXT
);

-- CreateTable
CREATE TABLE "initial_prompts" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "type" "InitialPromptType" NOT NULL,
    "prompt_text" TEXT NOT NULL,

    CONSTRAINT "initial_prompts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "prompt_threads_id_key" ON "prompt_threads"("id");

-- AddForeignKey
ALTER TABLE "prompt_threads" ADD CONSTRAINT "prompt_threads_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prompt_threads" ADD CONSTRAINT "prompt_threads_initial_prompt_id_fkey" FOREIGN KEY ("initial_prompt_id") REFERENCES "initial_prompts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prompt_completitions" ADD CONSTRAINT "prompt_completitions_prompt_thread_id_fkey" FOREIGN KEY ("prompt_thread_id") REFERENCES "prompt_threads"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
