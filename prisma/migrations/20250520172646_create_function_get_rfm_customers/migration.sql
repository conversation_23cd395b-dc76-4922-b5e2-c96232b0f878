CREATE OR REPLACE FUNCTION public.calculate_customer_rfm_scores(p_company_id text, p_allowed_store_names text[], p_allowed_sales_channels text[], p_disallowed_store_names text[], p_disallowed_sales_channels text[], p_start_date date, p_end_date date, p_excluded_order_statuses text[] DEFAULT NULL::text[])
 RETURNS TABLE(customer_id text, recency integer, frequency integer, monetary integer, recency_score integer, frequency_score integer, monetary_score integer, rfm_group text)
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY
    WITH rfm_data AS (
        SELECT
            o.customer_id,
            CURRENT_DATE - MAX(o.source_created_at)::DATE AS recency,
            COUNT(*) AS frequency,
            FLOOR(SUM(o.value) / 100) * 100 AS monetary
        FROM orders o
        JOIN customers c ON c.id = o.customer_id
        WHERE
            o.company_id = p_company_id
            AND c.is_deleted = false
            AND o.source_created_at::date >= p_start_date AND o.source_created_at::date <= p_end_date
            AND (p_excluded_order_statuses IS NULL OR o.status IS NULL OR o.status NOT IN (SELECT unnest(p_excluded_order_statuses)))
            AND (p_allowed_store_names IS NULL OR o.store_name = ANY(p_allowed_store_names))
            AND (p_allowed_sales_channels IS NULL OR o.sales_channel = ANY(p_allowed_sales_channels))
            AND (p_disallowed_store_names IS NULL OR COALESCE(o.store_name, '') <> ALL(p_disallowed_store_names))
            AND (p_disallowed_sales_channels IS NULL OR COALESCE(o.sales_channel, '') <> ALL(p_disallowed_sales_channels))
            AND o.value > 0
            AND (o.total_items_quantity > 0 OR o.total_items_quantity IS NULL)
        GROUP BY o.customer_id
    ),
    days_interval AS (
        SELECT p_end_date - p_start_date AS days_interval
    ),
    monetary_percentile AS (
        SELECT
            PERCENTILE_CONT(0.2) WITHIN GROUP (ORDER BY rd.monetary) AS percentile_20,
            PERCENTILE_CONT(0.4) WITHIN GROUP (ORDER BY rd.monetary) AS percentile_40,
            PERCENTILE_CONT(0.6) WITHIN GROUP (ORDER BY rd.monetary) AS percentile_60,
            PERCENTILE_CONT(0.8) WITHIN GROUP (ORDER BY rd.monetary) AS percentile_80
        FROM rfm_data rd
    ),
    rfm_scores AS (
        SELECT
            rd.customer_id,
            rd.recency,
            rd.frequency,
            rd.monetary AS monetary,
            CASE
                WHEN rd.recency <= (days_interval.days_interval / 5.0) THEN 5
                WHEN rd.recency <= (days_interval.days_interval * 2 / 5.0) THEN 4
                WHEN rd.recency <= (days_interval.days_interval * 3 / 5.0) THEN 3
                WHEN rd.recency <= (days_interval.days_interval * 4 / 5.0) THEN 2
                WHEN rd.recency <= days_interval.days_interval THEN 1
                ELSE 1
            END AS recency_score,
            NTILE(5) OVER (ORDER BY rd.frequency ASC) AS frequency_score,
            CASE
                WHEN rd.monetary <= monetary_percentile.percentile_20 THEN 1
                WHEN rd.monetary <= monetary_percentile.percentile_40 THEN 2
                WHEN rd.monetary <= monetary_percentile.percentile_60 THEN 3
                WHEN rd.monetary <= monetary_percentile.percentile_80 THEN 4
                ELSE 5
            END AS monetary_score
        FROM rfm_data rd
        JOIN days_interval ON TRUE
        JOIN monetary_percentile ON TRUE
    )
    SELECT
        rs.customer_id,
        rs.recency::INT,
        rs.frequency::INT,
        rs.monetary::INT,
        rs.recency_score::INT,
        rs.frequency_score::INT,
        rs.monetary_score::INT,
        CASE
            WHEN rs.recency_score = 5 AND rs.monetary_score >= 4 THEN 'champions'
            WHEN rs.recency_score >= 3 AND rs.monetary_score >= 4 THEN 'loyal_customers'
            WHEN rs.recency_score >= 4 AND rs.monetary_score >= 2 THEN 'potential_loyalists'
            WHEN rs.recency_score = 5 AND rs.monetary_score = 1 THEN 'recent_customers'
            WHEN rs.recency_score = 4 AND rs.monetary_score = 1 THEN 'promising'
            WHEN rs.recency_score = 3 AND rs.monetary_score = 3 THEN 'need_attention'
            WHEN rs.recency_score = 3 AND rs.monetary_score <= 2 THEN 'about_to_sleep'
            WHEN rs.recency_score <= 2 AND rs.monetary_score = 5 THEN 'cannot_lose_them'
            WHEN rs.recency_score <= 2 AND rs.monetary_score >= 3 THEN 'at_risk'
            WHEN rs.recency_score = 2 AND rs.monetary_score <= 2 THEN 'hibernating'
            WHEN rs.recency_score = 1 AND rs.monetary_score <= 2 THEN 'lost_customers'
            ELSE NULL
        END AS rfm_group
    FROM rfm_scores rs
    WHERE rs.recency >= 14;
END;
$function$
;