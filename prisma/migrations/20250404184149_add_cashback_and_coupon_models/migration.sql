-- Create<PERSON>num
CREATE TYPE "CouponType" AS ENUM ('cashback');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "DiscountType" AS ENUM ('percentage', 'fixed_in_cents');

-- CreateEnum
CREATE TYPE "StatusCoupon" AS ENUM ('created', 'pending', 'finished');

-- CreateTable
CREATE TABLE "cashback_configs" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "discount_value" INTEGER NOT NULL,
    "discount_type" "DiscountType" NOT NULL,
    "days_to_expire" INTEGER NOT NULL,
    "cumulative" BOOLEAN NOT NULL DEFAULT false,
    "min_order_value" DOUBLE PRECISION,
    "max_order_value" DOUBLE PRECISION,
    "integration" "SourceIntegration" NOT NULL,
    "creation_template_id" TEXT NOT NULL,
    "reminder_template_id" TEXT NOT NULL,
    "last_day_template_id" TEXT NOT NULL,
    "reminder_days" INTEGER NOT NULL,
    "status_trigger" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "cashback_configs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "coupons" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "source_id" TEXT,
    "company_id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "discount_value" INTEGER NOT NULL,
    "discount_type" "DiscountType" NOT NULL,
    "usage_limit" INTEGER,
    "usage_per_customer" INTEGER,
    "starts_at" TIMESTAMP(3),
    "ends_at" TIMESTAMP(3),
    "type" "CouponType" NOT NULL DEFAULT 'cashback',
    "status" "StatusCoupon" NOT NULL DEFAULT 'created',
    "scheduled_send_time" TIMESTAMP(3),
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "cashback_config_id" TEXT NOT NULL,
    "generated_by_order_id" TEXT NOT NULL,

    CONSTRAINT "coupons_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_coupons" (
    "id" TEXT NOT NULL,
    "coupon_id" TEXT NOT NULL,
    "customer_id" TEXT NOT NULL,

    CONSTRAINT "customer_coupons_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "cashback_configs_company_id_integration_key" ON "cashback_configs"("company_id", "integration");

-- CreateIndex
CREATE UNIQUE INDEX "customer_coupons_coupon_id_customer_id_key" ON "customer_coupons"("coupon_id", "customer_id");

-- AddForeignKey
ALTER TABLE "cashback_configs" ADD CONSTRAINT "cashback_configs_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cashback_configs" ADD CONSTRAINT "cashback_configs_creation_template_id_fkey" FOREIGN KEY ("creation_template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cashback_configs" ADD CONSTRAINT "cashback_configs_reminder_template_id_fkey" FOREIGN KEY ("reminder_template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cashback_configs" ADD CONSTRAINT "cashback_configs_last_day_template_id_fkey" FOREIGN KEY ("last_day_template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupons" ADD CONSTRAINT "coupons_generated_by_order_id_fkey" FOREIGN KEY ("generated_by_order_id") REFERENCES "orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupons" ADD CONSTRAINT "coupons_cashback_config_id_fkey" FOREIGN KEY ("cashback_config_id") REFERENCES "cashback_configs"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupons" ADD CONSTRAINT "coupons_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_coupons" ADD CONSTRAINT "customer_coupons_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupons"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_coupons" ADD CONSTRAINT "customer_coupons_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TYPE "LogType" ADD VALUE IF NOT EXISTS 'couponEvents';

ALTER TYPE "LogType" ADD VALUE IF NOT EXISTS 'sendCouponMessage';

ALTER TYPE "MessageTemplateType" ADD VALUE IF NOT EXISTS 'CASHBACK';