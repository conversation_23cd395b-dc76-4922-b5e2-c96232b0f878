-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "MessageTemplateType" ADD VALUE 'TRACKING_CODE';
ALTER TYPE "MessageTemplateType" ADD VALUE 'NEW_ORDER';
ALTER TYPE "MessageTemplateType" ADD VALUE 'WELCOME_REGISTRATION';
ALTER TYPE "MessageTemplateType" ADD VALUE 'ORDER_CONFIRMATION';
ALTER TYPE "MessageTemplateType" ADD VALUE 'ORDER_PAYMENT_CONFIRMATION';
