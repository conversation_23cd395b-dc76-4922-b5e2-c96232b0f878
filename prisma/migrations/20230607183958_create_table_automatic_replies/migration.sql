-- CreateEnum
CREATE TYPE "AutomaticReplyCondition" AS ENUM ('contains', 'isExactly');

-- CreateTable
CREATE TABLE "automatic_replies" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "keyword" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "condition" "AutomaticReplyCondition" NOT NULL,

    CONSTRAINT "automatic_replies_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "automatic_replies_keyword_company_id_key" ON "automatic_replies"("keyword", "company_id");

-- AddForeignKey
ALTER TABLE "automatic_replies" ADD CONSTRAINT "automatic_replies_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
