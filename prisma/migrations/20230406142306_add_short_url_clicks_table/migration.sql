-- CreateTable
CREATE TABLE "short_url_clicks" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "short_url_id" TEXT NOT NULL,

    CONSTRAINT "short_url_clicks_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "short_url_clicks" ADD CONSTRAINT "short_url_clicks_short_url_id_fkey" FOREIGN KEY ("short_url_id") REFERENCES "short_urls"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
