-- Atualiza a função que atualiza o search_vector
CREATE OR REPLACE FUNCTION faq_update_search_vector() RETURNS trigger AS $$
BEGIN
  NEW.search_vector :=
    to_tsvector('public.portuguese_unaccent', coalesce(NEW.question, '') || ' ' || coalesce(NEW.answer, ''));
  RETURN NEW;
END
$$ LANGUAGE plpgsql;
-- Atualiza
CREATE OR REPLACE FUNCTION search_faq(
    p_raw_query TEXT,  -- Se refere a pergunta do usuário
    p_limit INTEGER,     -- Limite de resultados esperado para evitar extrapolar a memória do agente
    p_company_id TEXT
)
RETURNS TABLE(
    questions TEXT,
    answers TEXT,
    company_id TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    relevance REAL
)
LANGUAGE sql
STABLE -- A função não modifica o banco de dados e retorna os mesmos resultados
       -- para os mesmos argumentos dentro de uma varredura de tabela.
AS $$
    WITH UserInput AS (
        SELECT p_raw_query::text AS raw_query
    ),
    GreetingPattern AS (
        -- Padrão Regex para detectar saudações no início da string (case-insensitive)
        SELECT '^(olá|oi|bom dia|boa tarde|boa noite)\s*,?\s*'::text AS pattern
    ),
    FinalTsQuery AS (
        SELECT
            CASE
                -- Se a consulta bruta corresponder ao padrão de saudação...
                WHEN ui.raw_query ~* gp.pattern THEN
                    -- ...remove a saudação e converte o resto para tsquery
                    plainto_tsquery('public.portuguese_unaccent', regexp_replace(ui.raw_query, gp.pattern, '', 'i'))
                ELSE
                    -- ...senão, converte a consulta bruta inteira para tsquery
                    plainto_tsquery('public.portuguese_unaccent', ui.raw_query)
            END AS ts_query_result
        FROM UserInput ui, GreetingPattern gp
    )
    SELECT
        f.question,
        f.answer,
        f.company_id,
        f.created_at,
        f.updated_at,
        -- Calcula a relevância usando ts_rank_cd
        ts_rank_cd(f.search_vector, ftq.ts_query_result)::REAL AS relevance
    FROM
        faq f,
        FinalTsQuery ftq
    WHERE
        -- Garante que a tsquery não seja nula (pode acontecer com strings vazias/stop words)
        ftq.ts_query_result IS NOT NULL
        -- A condição principal da busca full-text: o vetor do documento corresponde à consulta
        AND f.search_vector @@ ftq.ts_query_result
        and f.company_id = p_company_id
    ORDER BY
        relevance DESC -- Ordena pelos mais relevantes primeiro
    LIMIT p_limit;     -- Limita o número de resultados
$$;