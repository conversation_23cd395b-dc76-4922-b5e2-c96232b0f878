-- CreateEnum
CREATE TYPE "FilterType" AS ENUM ('customer');

-- CreateTable
CREATE TABLE "filters" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "FilterType" NOT NULL,
    "criteria" TEXT NOT NULL,

    CONSTRAINT "filters_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "filters_company_id_name_key" ON "filters"("company_id", "name");

-- AddForeignKey
ALTER TABLE "filters" ADD CONSTRAINT "filters_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
