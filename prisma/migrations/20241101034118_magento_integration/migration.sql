-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "LogSource" ADD VALUE 'magento2_ecommerce';
ALTER TYPE "LogSource" ADD VALUE 'magento1_ecommerce';

-- AlterEnum
ALTER TYPE "SourceIntegration" ADD VALUE 'magento1_ecommerce';

-- AlterTable
ALTER TABLE "companies" ADD COLUMN     "is_magento_active" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "magento_api_key" TEXT,
ADD COLUMN     "magento_api_url" TEXT,
ADD COLUMN     "magento_user" TEXT,
ADD COLUMN     "magento_version" TEXT;
