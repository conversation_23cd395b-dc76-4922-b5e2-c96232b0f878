/*
  Warnings:

  - You are about to drop the `gupshup_cards` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "CardHeaderType" AS ENUM ('IMAGE', 'VIDEO');

-- DropForeignKey
ALTER TABLE "gupshup_cards" DROP CONSTRAINT "gupshup_cards_gupshup_template_id_fkey";

-- DropForeignKey
ALTER TABLE "message_template_buttons" DROP CONSTRAINT "message_template_buttons_message_template_id_fkey";

-- AlterTable
ALTER TABLE "gupshup_templates" ADD COLUMN     "gupshup_cards" JSONB;

-- AlterTable
ALTER TABLE "message_template_buttons" ADD COLUMN     "message_template_card_id" TEXT,
ALTER COLUMN "message_template_id" DROP NOT NULL;

-- DropTable
DROP TABLE "gupshup_cards";

-- CreateTable
CREATE TABLE "message_template_cards" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "header_type" "CardHeaderType",
    "media_url" TEXT,
    "media_id" TEXT,
    "body" TEXT NOT NULL,
    "card_index" INTEGER NOT NULL,
    "message_template_id" TEXT NOT NULL,

    CONSTRAINT "message_template_cards_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "message_template_cards" ADD CONSTRAINT "message_template_cards_message_template_id_fkey" FOREIGN KEY ("message_template_id") REFERENCES "message_templates"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "message_template_buttons" ADD CONSTRAINT "message_template_buttons_message_template_card_id_fkey" FOREIGN KEY ("message_template_card_id") REFERENCES "message_template_cards"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "message_template_buttons" ADD CONSTRAINT "message_template_buttons_message_template_id_fkey" FOREIGN KEY ("message_template_id") REFERENCES "message_templates"("id") ON DELETE SET NULL ON UPDATE CASCADE;
