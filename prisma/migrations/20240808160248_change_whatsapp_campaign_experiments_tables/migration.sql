-- Create<PERSON><PERSON>
CREATE TYPE "WinningMetric" AS ENUM ('engagement_rate', 'read_rate');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "ExperimentStatus" AS ENUM ('scheduled', 'in_progress', 'completed', 'canceled');

-- CreateEnum
CREATE TYPE "CampaignType" AS ENUM ('standard', 'experiment_test_group', 'experiment_winning_group');

-- AlterTable
ALTER TABLE "campaign_recipients" ADD COLUMN     "campaign_experiment_id" TEXT,
ADD COLUMN     "type" "CampaignType" NOT NULL DEFAULT 'standard';

-- AlterTable
ALTER TABLE "whatsapp_campaigns" ADD COLUMN     "campaignType" "CampaignType" NOT NULL DEFAULT 'standard',
ADD COLUMN     "campaign_experiment_id" TEXT,
ADD COLUMN     "type" "CampaignType" NOT NULL DEFAULT 'standard';

-- CreateTable
CREATE TABLE "campaign_experiments" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "winning_metric" "WinningMetric" NOT NULL,
    "test_size_percentage" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "scheduled_start_time" TIMESTAMP(3) NOT NULL,
    "scheduled_end_time" TIMESTAMP(3) NOT NULL,
    "filter_criteria" TEXT,
    "scheduled_job_id" TEXT,
    "status" "ExperimentStatus" NOT NULL DEFAULT 'scheduled',

    CONSTRAINT "campaign_experiments_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "whatsapp_campaigns" ADD CONSTRAINT "whatsapp_campaigns_campaign_experiment_id_fkey" FOREIGN KEY ("campaign_experiment_id") REFERENCES "campaign_experiments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "campaign_recipients" ADD CONSTRAINT "campaign_recipients_campaign_experiment_id_fkey" FOREIGN KEY ("campaign_experiment_id") REFERENCES "campaign_experiments"("id") ON DELETE SET NULL ON UPDATE CASCADE;
