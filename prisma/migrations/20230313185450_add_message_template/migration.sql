-- CreateEnum
CREATE TYPE "MessageTemplateStatus" AS ENUM ('pending', 'rejected', 'approved', 'deleted', 'disabled');

-- CreateTable
CREATE TABLE "message_templates" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "is_approved" BOOLEAN NOT NULL DEFAULT false,
    "company_id" TEXT NOT NULL,
    "media_url" TEXT,
    "media_type" "MediaType",
    "gupshup_template_id" TEXT NOT NULL,
    "template_text" VARCHAR(1028) NOT NULL,
    "status" "MessageTemplateStatus" NOT NULL DEFAULT E'pending',

    CONSTRAINT "message_templates_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "message_templates" ADD CONSTRAINT "message_templates_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
