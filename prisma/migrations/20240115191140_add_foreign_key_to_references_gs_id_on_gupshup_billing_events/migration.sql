/*
  Warnings:

  - A unique constraint covering the columns `[whatsapp_message_id]` on the table `messages` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE UNIQUE INDEX "messages_whatsapp_message_id_key" ON "messages"("whatsapp_message_id");

-- AddForeignKey
ALTER TABLE "gupshup_billing_events" ADD CONSTRAINT "gupshup_billing_events_reference_gs_id_fkey" FOREIGN KEY ("reference_gs_id") REFERENCES "messages"("whatsapp_message_id") ON DELETE SET NULL ON UPDATE CASCADE;
