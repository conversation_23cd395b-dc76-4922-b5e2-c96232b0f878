/*
  Warnings:

  - The values [send_tracking_code,cart_recovery] on the enum `AutomationTypeSlug` will be removed. If these variants are still used in the database, this will fail.
  - The values [sendCartRecoveryMessages] on the enum `LogType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "AutomationTypeSlug_new" AS ENUM ('tracking_code', 'abandoned_cart');
ALTER TABLE "automation_types" ALTER COLUMN "slug" TYPE "AutomationTypeSlug_new" USING ("slug"::text::"AutomationTypeSlug_new");
ALTER TYPE "AutomationTypeSlug" RENAME TO "AutomationTypeSlug_old";
ALTER TYPE "AutomationTypeSlug_new" RENAME TO "AutomationTypeSlug";
DROP TYPE "AutomationTypeSlug_old";
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "LogType_new" AS ENUM ('syncOrders', 'sendAbandonedCartMessages');
ALTER TABLE "logs" ALTER COLUMN "type" TYPE "LogType_new" USING ("type"::text::"LogType_new");
ALTER TYPE "LogType" RENAME TO "LogType_old";
ALTER TYPE "LogType_new" RENAME TO "LogType";
DROP TYPE "LogType_old";
COMMIT;
