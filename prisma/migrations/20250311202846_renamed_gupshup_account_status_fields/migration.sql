/*
  Warnings:

  - You are about to drop the column `gupshup_company_status` on the `companies` table. All the data in the column will be lost.
  - You are about to drop the column `gupshup_company_status_reason` on the `companies` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "GupshupAccountStatus" AS ENUM ('ACCOUNT_VIOLATION', 'ACCOUNT_DISABLE', 'ACCOUNT_VERIFIED', 'ACCOUNT_RESTRICTED');

-- AlterTable
ALTER TABLE "companies" DROP COLUMN "gupshup_company_status",
DROP COLUMN "gupshup_company_status_reason",
ADD COLUMN     "gupshup_account_status" "GupshupAccountStatus",
ADD COLUMN     "gupshup_account_status_reason" JSONB;

-- DropEnum
DROP TYPE "GupshupCompanyStatus";
