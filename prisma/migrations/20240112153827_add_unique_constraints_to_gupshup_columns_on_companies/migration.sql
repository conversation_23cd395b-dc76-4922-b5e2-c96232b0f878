/*
  Warnings:

  - A unique constraint covering the columns `[gupshup_app_name]` on the table `companies` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[gupshup_app_id]` on the table `companies` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateTable
CREATE TABLE "gupshup_billing_events" (
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "gupshup_app_name" TEXT,
    "deduction_type" TEXT,
    "deduction_model" TEXT,
    "deduction_source" TEXT,
    "deduction_billable" TEXT,
    "reference_id" TEXT,
    "reference_gs_id" TEXT,
    "reference_conversation_id" TEXT,
    "reference_destination" TEXT,

    CONSTRAINT "gupshup_billing_events_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "companies_gupshup_app_name_key" ON "companies"("gupshup_app_name");

-- CreateIndex
CREATE UNIQUE INDEX "companies_gupshup_app_id_key" ON "companies"("gupshup_app_id");

-- AddForeignKey
ALTER TABLE "gupshup_billing_events" ADD CONSTRAINT "gupshup_billing_events_gupshup_app_name_fkey" FOREIGN KEY ("gupshup_app_name") REFERENCES "companies"("gupshup_app_name") ON DELETE SET NULL ON UPDATE CASCADE;
