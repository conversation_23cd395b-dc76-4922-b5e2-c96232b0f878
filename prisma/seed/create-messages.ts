import { faker } from '@faker-js/faker';
import { Company, Prisma, PrismaClient } from '@prisma/client';

const PROBABILITY_OF_READING = 0.8;

export async function createMessages(
  prisma: PrismaClient,
  companies: Company[],
) {
  const messages: Prisma.MessageCreateManyInput[] = [];
  for (const company of companies) {
    const whatsappCampaigns = await prisma.whatsappCampaign.findMany({
      where: {
        companyId: company.id,
      },
      include: {
        template: {
          select: {
            templateText: true,
            mediaUrl: true,
            mediaType: true,
            ctaLink: true,
          },
        },
      },
    });

    const conversations = await prisma.conversation.findMany({
      where: {
        companyId: company.id,
      },
      include: {
        messages: {
          take: 1,
        },
      },
      orderBy: {
        id: 'asc',
      },
    });
    for (const whatsappCampaign of whatsappCampaigns) {
      for (const conversation of conversations) {
        messages.push({
          createdAt: whatsappCampaign.createdAt,
          tempId: faker.string.uuid(),
          text: whatsappCampaign.template.templateText,
          senderPhoneNumberId: company.phoneNumberId,
          recipientPhoneNumberId: conversation.recipientPhoneNumberId,
          conversationId: conversation.id,
          fromSystem: true,
          firstReplyId:
            whatsappCampaign === whatsappCampaigns[0]
              ? conversation.messages[0]?.id
              : null,
          status: Math.random() > PROBABILITY_OF_READING ? 'sent' : 'read',
          whatsappMessageId: faker.string.uuid(),
          messageTemplateId: whatsappCampaign.templateId,
          whatsappCampaignId: whatsappCampaign.id,
        });
      }
    }
  }
  return prisma.message.createMany({
    data: messages,
    skipDuplicates: true,
  });
}
