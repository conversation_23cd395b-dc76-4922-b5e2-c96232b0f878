import { PrismaClient, Prisma, Company } from '@prisma/client';

export async function createBillingSettings(
  prisma: PrismaClient,
  companies: Company[],
) {
  const billingSettings: Prisma.BillingSettingsCreateManyInput[] = [];
  for (const company of companies) {
    billingSettings.push({
      companyId: company.id,
      customerServiceFee: 30000,
      platformFee: 30000,
      whatsappMarketingPackageLimit: 10000,
      whatsappUtilityPackageLimit: 5000,
      whatsappServicePackageLimit: 2000,
      whatsappMarketingMessageFee: 50,
      whatsappUtilityMessageFee: 30,
      whatsappServiceMessageFee: 20,
      whatsappMarketingExtraMessageFee: 60,
      whatsappUtilityExtraMessageFee: 40,
      whatsappServiceExtraMessageFee: 30,
      billingContactEmail: '<EMAIL>',
      paymentMethod: 'boleto',
      smsPackageLimit: 1000,
      smsExtraMessageFee: 20,
      smsMessageFee: 15,
    });
  }
  return prisma.billingSettings.createMany({
    data: billingSettings,
    skipDuplicates: true,
  });
}
