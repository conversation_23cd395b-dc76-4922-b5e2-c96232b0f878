import { PrismaClient } from '@prisma/client';

export async function createAudienceRecommendationClasses(
  prisma: PrismaClient,
) {
  return prisma.audienceRecommendationClass.createMany({
    data: [
      {
        id: 'champion',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        description: 'Melhores clientes',
        slug: 'champion',
      },
    ],
    skipDuplicates: true,
  });
}
