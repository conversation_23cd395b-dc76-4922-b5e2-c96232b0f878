import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma, Company } from '@prisma/client';

export async function createConversationCategories(
  prisma: PrismaClient,
  companies: Company[],
) {
  const conversationCategories: Prisma.ConversationCategoryCreateManyInput[] =
    [];
  for (const company of companies) {
    conversationCategories.push({
      name: 'Interessados',
      companyId: company.id,
    });
    conversationCategories.push({
      name: 'Não tem interesse',
      companyId: company.id,
    });
    conversationCategories.push({
      name: 'Dúvidas',
      companyId: company.id,
    });
  }
  return prisma.conversationCategory.createMany({
    data: conversationCategories,
    skipDuplicates: true,
  });
}
