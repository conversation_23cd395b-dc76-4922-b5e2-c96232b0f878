import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma, Company } from '@prisma/client';

const NUM_COMPANIES = 1;

const shopifyReviTest = {
  adminApiAccessToken: '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',
  apiKey: '2a2741c316b33f28a8d2f7db6feb9ec7',
  apiSecretKey: 'c58bb73451783f6272828eeed3345794',
  shop: 'loja-do-revi.myshopify.com',
};

export async function createCompanies(prisma: PrismaClient) {
  const companies: Prisma.CompanyCreateManyInput[] = [];
  for (let i = 0; i < NUM_COMPANIES; i++) {
    companies.push({
      id: 'test',
      name: `test`,
      phoneNumber: '************',
      phoneNumberId: '************',
      whatsappBusinessId: faker.string.uuid(),
      whatsappAccessToken: faker.string.uuid(),
      firstContactMessage: 'Ol<PERSON>, bem vindo a Revi. Em que posso ajudar?',
      isAutomaticSortingActive: false,
      gupshupAppName: 'ReviApp2',
      gupshupAppId: 'd797afae-d447-4f09-8993-f5e9408b7d76',
      dailyMessageLimitOnWhatsapp: 2000,
      monthlyMessageLimitOnWhatsapp: 30000,
      vtexAppKey: faker.string.uuid(),
      vtexAppToken: faker.string.uuid(),
      vtexAccountName: faker.string.uuid(),
      isVtexActive: false,
      shopifyShopName: shopifyReviTest.shop,
      shopifySession: faker.string.uuid(),
      shopifyApiKey: shopifyReviTest.apiKey,
      shopifyApiSecretKey: shopifyReviTest.apiSecretKey,
      shopifyAdminAccessToken: shopifyReviTest.adminApiAccessToken,
      isShopifyActive: false,
      costPerMessage: 50,
      productDescription: 'sapatos',
      valueProposition:
        'A marca Paula Ferber personifica a elegância e sofisticação na moda, destacando-se por seus sapatos de luxo exclusivos e atemporais. Cada par é uma obra-prima artesanal, refletindo a fusão de criatividade e refinamento. Utilizando materiais de alta qualidade, a marca assegura durabilidade e conforto incomparáveis. Além da estética, a Paula Ferber prioriza uma experiência de uso luxuosa e sustentabilidade em toda a cadeia de produção. Mais que uma marca, é um símbolo de sofisticação, artesanato impecável e valores éticos na moda.',
    });
    companies.push({
      id: 'demo',
      name: `demo`,
      phoneNumber: '+55 11 5196-5398',
      phoneNumberId: '+************',
      whatsappBusinessId: faker.string.uuid(),
      whatsappAccessToken: faker.string.uuid(),
      firstContactMessage: faker.lorem.sentence(),
      isAutomaticSortingActive: false,
      gupshupAppName: 'ReviApp',
      gupshupAppId: '54343fd8-cc65-43a8-bee3-a19eda45f989',
      dailyMessageLimitOnWhatsapp: 2000,
      monthlyMessageLimitOnWhatsapp: 30000,
      vtexAppKey: faker.string.uuid(),
      vtexAppToken: faker.string.uuid(),
      vtexAccountName: faker.string.uuid(),
      isVtexActive: false,
      shopifyShopName: shopifyReviTest.shop,
      shopifySession: faker.string.uuid(),
      shopifyApiKey: shopifyReviTest.apiKey,
      shopifyApiSecretKey: shopifyReviTest.apiSecretKey,
      shopifyAdminAccessToken: shopifyReviTest.adminApiAccessToken,
      isShopifyActive: false,
      costPerMessage: 50,
      afterHoursMessage: 'Estamos fora do horário de atendimento.',
      monthlyMessageLimitOnSms: 100,
      productDescription: 'sapatos',
      valueProposition:
        'A marca Paula Ferber personifica a elegância e sofisticação na moda, destacando-se por seus sapatos de luxo exclusivos e atemporais. Cada par é uma obra-prima artesanal, refletindo a fusão de criatividade e refinamento. Utilizando materiais de alta qualidade, a marca assegura durabilidade e conforto incomparáveis. Além da estética, a Paula Ferber prioriza uma experiência de uso luxuosa e sustentabilidade em toda a cadeia de produção. Mais que uma marca, é um símbolo de sofisticação, artesanato impecável e valores éticos na moda.',
      cnpj: '*********',
      razaoSocial: 'Paula Ferber',
    });
  }
  return prisma.company.createMany({
    data: companies,
    skipDuplicates: true,
  });
}
