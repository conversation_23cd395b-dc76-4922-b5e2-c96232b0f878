import { PrismaClient, Prisma, Company } from '@prisma/client';

export async function createAudienceRecommendations(
  prisma: PrismaClient,
  companies: Company[],
) {
  const audienceRecommendations: Prisma.AudienceRecommendationCreateManyInput[] =
    [];
  for (const company of companies) {
    audienceRecommendations.push({
      classId: 'champion',
      companyId: company.id,
      filterCriteria:
        'minTotalOrders=3&minTotalPurchases=1000&sortBy=totalPurchasesDesc',
    });
  }
  return prisma.audienceRecommendation.createMany({
    data: audienceRecommendations,
    skipDuplicates: true,
  });
}
