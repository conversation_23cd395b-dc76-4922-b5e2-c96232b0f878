import { Prisma, PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import {
  TEST_COMPANY_GUPSHUP_APP_ID,
  TEST_COMPANY_GUPSHUP_APP_NAME,
  TEST_COMPANY_ID,
  TEST_COMPANY_PHONE_NUMBER_ID,
  TEST_USER_EMAIL,
  TEST_USER_PASSWORD,
} from 'src/_e2e-utils/constants/test-company-data.constant';

const prisma = new PrismaClient();

function printSeparator() {
  console.log('--------------------------------');
}

async function clearE2eData(prisma: PrismaClient) {
  if (process.env.NODE_ENV !== 'development') {
    console.log('This script is only available in development mode');
    return;
  }

  if (process.env.ENABLE_E2E_UTILS !== 'true') {
    console.log('E2E utils are disabled');
    return;
  }

  if (!process.env.DATABASE_URL) {
    console.log('DATABASE_URL is not set');
    return;
  }

  if (process.env.DATABASE_URL.includes('production')) {
    console.log('This script is not available in production');
    return;
  }

  if (
    !process.env.DATABASE_URL.includes('localhost') ||
    !process.env.DATABASE_URL.includes('127.0.0.1')
  ) {
    console.log('This script is only available in localhost');
    return;
  }

  if (TEST_COMPANY_ID !== 'e2e-test') {
    console.log('TEST_COMPANY_ID is not set to e2e-test');
    return;
  }

  const sqlQueries: Prisma.Sql[] = [
    Prisma.sql`DELETE FROM flow_events fe WHERE fe.flow_id IN (SELECT id FROM flows WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM short_url_clicks WHERE short_url_id IN (SELECT id FROM short_urls WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM short_urls WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM sms_messages WHERE sms_campaign_id IN (SELECT id FROM sms_campaigns WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM campaign_recipients WHERE whatsapp_campaign_id IN (SELECT id FROM whatsapp_campaigns WHERE company_id = ${TEST_COMPANY_ID}) OR sms_campaign_id IN (SELECT id FROM sms_campaigns WHERE company_id = ${TEST_COMPANY_ID}) OR campaign_experiment_id IN (SELECT id FROM campaign_experiments WHERE company_id = ${TEST_COMPANY_ID}) OR email_campaign_id IN (SELECT id FROM email_campaigns WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM whatsapp_sessions WHERE conversation_id IN (SELECT id FROM conversations WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM conversation_tickets WHERE conversation_id IN (SELECT id FROM conversations WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM message_cards WHERE message_id IN (SELECT id FROM messages WHERE conversation_id IN (SELECT id FROM conversations WHERE company_id = ${TEST_COMPANY_ID}));`,
    Prisma.sql`DELETE FROM messages WHERE conversation_id IN (SELECT id FROM conversations WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM email_campaign_results WHERE email_campaign_id IN (SELECT id FROM email_campaigns WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM whatsapp_campaigns WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM campaign_experiments WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM sms_campaigns WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM email_campaigns ec WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM flow_node_scheduled_actions WHERE conversation_id IN (SELECT id FROM conversations WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM conversations WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM customer_tags WHERE customer_id IN (SELECT id FROM customers WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM tags WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM company_defined_fields WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM order_items WHERE order_id IN (SELECT id FROM orders WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM order_product_categories WHERE product_id IN (SELECT id FROM products WHERE company_id = ${TEST_COMPANY_ID});`,
    Prisma.sql`DELETE FROM order_products WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM categories WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM orders WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM customers WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM company_defined_fields WHERE company_id = ${TEST_COMPANY_ID};`,
    Prisma.sql`DELETE FROM emails WHERE company_id = ${TEST_COMPANY_ID};`,
  ];

  for (const query of sqlQueries) {
    if (query.sql.toLocaleLowerCase().includes('delete')) {
      if (!query.sql.toLocaleLowerCase().includes(`where company_id = ?`)) {
        throw new Error(`Query does not contain WHERE company_id = ?`);
      }
      if (!query.values.includes(TEST_COMPANY_ID)) {
        throw new Error(
          `Query does not contain company_id = ${TEST_COMPANY_ID}`,
        );
      }
    }
  }

  await prisma.$transaction(async (prisma) => {
    for (const query of sqlQueries) {
      await prisma.$queryRaw(query);
    }
  });
}

async function main() {
  if (process.env.NODE_ENV !== 'development') {
    console.log('This script is only available in development mode');
    return;
  }

  if (!process.env.DATABASE_URL) {
    console.log('DATABASE_URL is not set');
    return;
  }

  if (process.env.DATABASE_URL.includes('production')) {
    console.log('This script is not available in production');
    return;
  }

  if (
    !process.env.DATABASE_URL.includes('localhost') ||
    !process.env.DATABASE_URL.includes('127.0.0.1')
  ) {
    console.log('This script is only available in localhost');
    return;
  }

  console.log('Clearing E2E data...');
  await clearE2eData(prisma);
  console.log('E2E data cleared!');
  printSeparator();

  console.log('Creating E2E company...');

  const e2eCompany = await prisma.company.upsert({
    where: {
      id: TEST_COMPANY_ID,
    },
    create: {
      id: TEST_COMPANY_ID,
      name: 'E2E Company',
      phoneNumber: TEST_COMPANY_PHONE_NUMBER_ID,
      phoneNumberId: TEST_COMPANY_PHONE_NUMBER_ID,
      gupshupAppName: TEST_COMPANY_GUPSHUP_APP_NAME,
      gupshupAppId: TEST_COMPANY_GUPSHUP_APP_ID,
      isActive: true,
    },
    update: {},
  });

  console.log('E2E company created!');
  printSeparator();

  console.log('Creating E2E user...');
  const hashPassword = await bcrypt.hash(TEST_USER_PASSWORD, 10);
  // find or create e2e customer with specific id
  const e2eUser = await prisma.user.upsert({
    where: {
      id: 'e2e-test',
    },
    create: {
      id: 'e2e-test',
      name: 'E2E Customer',
      email: TEST_USER_EMAIL,
      companyId: e2eCompany.id,
      password: hashPassword,
    },
    update: {},
  });

  console.log('E2E user created!');
  printSeparator();

  console.log('E2E seed completed successfully');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
