import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma, Company } from '@prisma/client';

const NUM_CUSTOMERS = 1000;
const PROBABILITY_OF_OPTED_OUT = 0.05;

export async function createCustomers(
  prisma: PrismaClient,
  companies: Company[],
) {
  const customers: Prisma.CustomerCreateManyInput[] = [];
  for (const company of companies) {
    for (let i = 0; i < NUM_CUSTOMERS; i++) {
      customers.push({
        sourceId: faker.string.uuid(),
        sourceCustomerId: faker.string.uuid(),
        sourceUserId: faker.string.uuid(),
        sourceCreatedAt: faker.date.past(),
        sourceUpdatedAt: faker.date.past(),
        phoneNumberId: faker.string.uuid(),
        name: faker.person.fullName(),
        email: faker.internet.email(),
        isOptedIn: true,
        isOptedOut: Math.random() < PROBABILITY_OF_OPTED_OUT,
        isOptedInToNewsletter: Math.random() < PROBABILITY_OF_OPTED_OUT,
        companyId: company.id,
        birthDate: faker.date.past(),
        state: faker.location.state(),
        city: faker.location.city(),
      });
    }
  }
  return prisma.customer.createMany({
    data: customers,
    skipDuplicates: true,
  });
}
