import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma, Company } from '@prisma/client';
import { ArrayUtils } from '../../src/shared/utils/array.utils';
import { subDays } from 'date-fns';

const NUM_SMS_CAMPAIGNS = 50;
const PROBABILITY_OF_SCHEDULED = 0.1;

export async function createSmsCampaigns(
  prisma: PrismaClient,
  companies: Company[],
) {
  const smsCampaigns: Prisma.SmsCampaignCreateManyInput[] = [];
  for (const company of companies) {
    const messageTemplates = await prisma.messageTemplate.findMany({
      where: {
        companyId: company.id,
      },
    });
    const messageTemplateIds = messageTemplates.map((messageTemplate) => {
      return messageTemplate.id;
    });

    for (let i = 0; i < NUM_SMS_CAMPAIGNS; i++) {
      const status =
        Math.random() < PROBABILITY_OF_SCHEDULED ? 'scheduled' : 'completed';
      smsCampaigns.push({
        createdAt: subDays(new Date(), i),
        companyId: company.id,
        messageTemplateId: ArrayUtils.randomArrayElement(messageTemplateIds),
        totalRecipients: faker.number.int({ min: 1, max: 1000 }),
        totalProcessed: faker.number.int({ min: 0, max: 1000 }),
        status,
        filterCriteria: faker.lorem.sentence(),
        templateArgs: JSON.stringify({
          arg1: faker.lorem.word(),
          arg2: faker.lorem.word(),
        }),
        scheduledExecutionTime:
          status === 'scheduled' ? faker.date.soon() : null,
        scheduledJobId: faker.string.uuid(),
      });
    }
  }
  return await prisma.smsCampaign.createMany({
    data: smsCampaigns,
    skipDuplicates: true,
  });
}
