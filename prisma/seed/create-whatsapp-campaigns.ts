import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma, Company } from '@prisma/client';
import { ArrayUtils } from '../../src/shared/utils/array.utils';
import { subDays } from 'date-fns';

const NUM_WHATSAPP_CAMPAIGNS = 3;
const PROBABILITY_OF_SCHEDULED = 0.1;

export async function createWhatsappCampaigns(
  prisma: PrismaClient,
  companies: Company[],
) {
  const whatsappCampaigns: Prisma.WhatsappCampaignCreateManyInput[] = [];
  for (const company of companies) {
    const messageTemplates = await prisma.messageTemplate.findMany({
      where: {
        companyId: company.id,
      },
    });
    const messageTemplateIds = messageTemplates.map((messageTemplate) => {
      return messageTemplate.id;
    });
    for (let i = 0; i < NUM_WHATSAPP_CAMPAIGNS; i++) {
      const status =
        Math.random() < PROBABILITY_OF_SCHEDULED ? 'scheduled' : 'completed';
      whatsappCampaigns.push({
        createdAt: subDays(new Date(), i),
        companyId: company.id,
        templateId: ArrayUtils.randomArrayElement(messageTemplateIds),
        totalRecipients: faker.number.int({ min: 1, max: 1000 }),
        totalProcessed: faker.number.int({ min: 0, max: 1000 }),
        status,
        filterCriteria: faker.lorem.sentence(),
        templateArgs: JSON.stringify({
          arg1: faker.lorem.word(),
          arg2: faker.lorem.word(),
        }),
        scheduledExecutionTime:
          status === 'scheduled' ? faker.date.soon() : null,
        scheduledJobId: faker.string.uuid(),
      });
    }
  }
  return prisma.whatsappCampaign.createMany({
    data: whatsappCampaigns,
    skipDuplicates: true,
  });
}
