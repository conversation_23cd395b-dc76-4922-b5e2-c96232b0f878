import { Company, Prisma, PrismaClient } from '@prisma/client';

export async function createInvoices(
  prisma: PrismaClient,
  companies: Company[],
) {
  const invoices: Prisma.InvoiceCreateManyInput[] = [];
  for (const company of companies) {
    // Create invoice for company
    const invoice: Prisma.InvoiceCreateManyInput = {
      companyId: company.id,
      paymentMethod: 'boleto',
      value: 29900,
      dueDate: new Date('2024-01-15'),
      referenceMonth: new Date('2024-01-01'),
    };
    invoices.push(invoice);

    // Create invoice items
    const invoiceItems: Omit<Prisma.InvoiceItemCreateManyInput, 'invoiceId'>[] =
      [
        {
          name: 'Custo de plataforma',
          unitPrice: 400 * 100,
          quantity: 1,
          totalPrice: 400,
          discount: 0,
          description: 'Custo de plataforma',
        },
        {
          name: '<PERSON>usto de <PERSON>',
          unitPrice: 400 * 100,
          quantity: 1,
          totalPrice: 400,
          discount: 0,
          description: 'Custo de CS',
        },
        {
          name: 'Imp<PERSON>a<PERSON>',
          unitPrice: 500 * 100,
          quantity: 1,
          totalPrice: 500,
          discount: 0,
          description: 'Implantação',
        },
        {
          name: 'Custo de msgs de mkt no pacote',
          unitPrice: 0.6 * 100,
          quantity: 10000,
          totalPrice: 6000,
          discount: 0,
          description: 'Custo de msgs de mkt no pacote',
        },
        {
          name: 'Custo de msgs de mkt excedentes',
          unitPrice: 0.65 * 100,
          quantity: 4992,
          totalPrice: 3244.8,
          discount: 0,
          description: 'Custo de msgs de mkt excedentes',
        },
        {
          name: 'Custo de msgs de utility no pacote',
          unitPrice: 0.2 * 100,
          quantity: 0,
          totalPrice: 0,
          discount: 0,
          description: 'Custo de msgs de utility no pacote',
        },
        {
          name: 'Custo de msgs de utility excedentes',
          unitPrice: 0.2 * 100,
          quantity: 0,
          totalPrice: 0,
          discount: 0,
          description: 'Custo de msgs de utility excedentes',
        },
        {
          name: 'Custo de msgs de atendimento no pacote',
          unitPrice: 0.2 * 100,
          quantity: 3000,
          totalPrice: 600,
          discount: 0,
          description: 'Custo de msgs de atendimento no pacote',
        },
        {
          name: 'Custo de msgs de atendimento excedentes',
          unitPrice: 0.2 * 100,
          quantity: 0,
          totalPrice: 0,
          discount: 0,
          description: 'Custo de msgs de atendimento excedentes',
        },
        {
          name: 'Desconto',
          unitPrice: 400 * 100,
          quantity: 1,
          totalPrice: 400,
          discount: 0,
          description: 'Desconto',
        },
      ];

    await prisma.invoice.create({
      data: {
        ...invoice,
        invoiceItems: {
          createMany: {
            data: invoiceItems,
          },
        },
      },
    });
  }
}
