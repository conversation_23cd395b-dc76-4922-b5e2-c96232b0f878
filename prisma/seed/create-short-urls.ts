import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma, Company } from '@prisma/client';

async function createSmsShortUrls(prisma: PrismaClient, companies: Company[]) {
  const shortUrls: Prisma.ShortUrlCreateManyInput[] = [];
  for (const company of companies) {
    const smsCampaigns = await prisma.smsCampaign.findMany({
      where: {
        companyId: company.id,
      },
      select: {
        messages: {
          select: {
            id: true,
          },
        },
      },
    });

    for (const smsCampaign of smsCampaigns) {
      const shortUrl = {
        originalUrl: faker.internet.url(),
        companyId: company.id,
      };

      for (const message of smsCampaign.messages) {
        shortUrls.push({
          ...shortUrl,
          smsMessageId: message.id,
        });
      }
    }
  }

  return prisma.shortUrl.createMany({
    data: shortUrls,
    skipDuplicates: true,
  });
}

async function createWhatsappShortUrls(
  prisma: PrismaClient,
  companies: Company[],
) {
  const shortUrls: Prisma.ShortUrlCreateManyInput[] = [];
  for (const company of companies) {
    const messages = await prisma.message.findMany({
      where: {
        conversation: {
          companyId: company.id,
        },
      },
    });
    for (const message of messages) {
      shortUrls.push({
        originalUrl: faker.internet.url(),
        messageId: message.id,
        companyId: company.id,
      });
    }
  }
  return prisma.shortUrl.createMany({
    data: shortUrls,
    skipDuplicates: true,
  });
}

export async function createShortUrls(
  prisma: PrismaClient,
  companies: Company[],
) {
  await createSmsShortUrls(prisma, companies);
  await createWhatsappShortUrls(prisma, companies);
}
