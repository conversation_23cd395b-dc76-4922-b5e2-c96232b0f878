import { PrismaClient } from '@prisma/client';
import { createCompanies } from './create-companies';
import { createCustomers } from './create-customers';
import { createUsers } from './create-users';
import { createOrders } from './create-orders';
import { createMessageTemplates } from './create-message-templates';
import { createConversationCategories } from './create-conversation-categories';
import { createWhatsappCampaigns } from './create-whatsapp-campaigns';
import { createSmsCampaigns } from './create-sms-campaigns';
import { createConversations } from './create-conversations';
import { createMessages } from './create-messages';
import { createShortUrls } from './create-short-urls';
import { createShortUrlClicks } from './create-short-url-clicks';
import { createFirstReplies } from './create-first-replies';
import { createAudienceRecommendationClasses } from './create-audience-recommendation-classes';
import { createAudienceRecommendations } from './create-audience-recommendations';
import { createInitialPrompts } from './create-initial-prompts';
import { createBillingSettings } from './create-billing-settings';
import { createInvoices } from './create-invoices';
import { createAutomationTypes } from './create-automation-types';
import { createRoles } from './create-roles';
import { createSmsMessages } from './create-sms-messages';
import { createCampaignRecipients } from './create-campaigns-recipients';

const prisma = new PrismaClient();

function printSeparator() {
  console.log('--------------------------------------------------');
}

async function main() {
  if (process.env.NODE_ENV === 'production') {
    console.log('This script is not available in production');
    return;
  }

  if (!process.env.DATABASE_URL) {
    console.log('DATABASE_URL is not set');
    return;
  }

  if (process.env.DATABASE_URL.includes('production')) {
    console.log('This script is not available in production');
    return;
  }

  console.log('STARTING SEEDING PROCESS');

  console.log('Creating companies...');
  await createCompanies(prisma);
  const companies = await prisma.company.findMany();
  console.log('Companies created!');
  printSeparator();

  console.log('Creating roles...');
  await createRoles(prisma, companies);
  const roles = await prisma.role.findMany();
  console.log('Roles created!');
  printSeparator();

  console.log('Creating users...');
  await createUsers(prisma, companies, roles);
  console.log('Users created!');
  printSeparator();

  // company related data
  console.log('Creating messageTemplates...');
  await createMessageTemplates(prisma, companies);
  console.log('messageTemplates created!');
  printSeparator();

  console.log('Creating conversationCategories...');
  await createConversationCategories(prisma, companies);
  console.log('conversationCategories created!');
  printSeparator();

  console.log('Creating conversationCategories...');
  await createMessageTemplates(prisma, companies);
  console.log('conversationCategories created!');
  printSeparator();

  console.log('Creating customers...');
  await createCustomers(prisma, companies);
  console.log('Customers created!');
  printSeparator();

  console.log('Creating conversations...');
  await createConversations(prisma, companies);
  console.log('Conversations created!');
  printSeparator();

  console.log('Creating whatsappCampaigns...');
  await createWhatsappCampaigns(prisma, companies);
  console.log('WhatsappCampaigns created!');
  printSeparator();

  console.log('Creating smsCampaigns...');
  await createSmsCampaigns(prisma, companies);
  console.log('SmsCampaigns created!');
  printSeparator();

  console.log('Creating CampaignRecipients...');
  await createCampaignRecipients(prisma, companies);
  console.log('CampaignRecipients created!');
  printSeparator();

  console.log('Creating first reply messages...');
  await createFirstReplies(prisma, companies);
  console.log('First reply created!');
  printSeparator();

  console.log('Creating messages...');
  await createMessages(prisma, companies);
  console.log('Messages created!');
  printSeparator();

  console.log('Creating SmsMessages...');
  await createSmsMessages(prisma);
  console.log('SmsMessages created!');
  printSeparator();

  console.log('Creating shortUrls...');
  await createShortUrls(prisma, companies);
  console.log('ShortUrls created!');
  printSeparator();

  console.log('Creating shortUrlClicks...');
  await createShortUrlClicks(prisma, companies);
  console.log('ShortUrlClicks created!');
  printSeparator();

  console.log('Creating orders...');
  await createOrders(prisma, companies);
  console.log('Orders created!');
  printSeparator();

  console.log('Creating audience recommendation classes...');
  await createAudienceRecommendationClasses(prisma);
  console.log('Audience recommendation classes created!');

  console.log('Creating audience recommendations...');
  await createAudienceRecommendations(prisma, companies);
  console.log('Audience recommendations created!');

  console.log('Creating initial prompts...');
  await createInitialPrompts(prisma);
  console.log('Initial prompts created!');

  console.log('Fixing total recipients on whatsappCampaign');
  const messagesByCampaign = await prisma.message.groupBy({
    where: {
      conversation: {
        companyId: {
          in: companies.map((el) => el.id),
        },
      },
    },
    by: ['whatsappCampaignId'],
    _count: {
      whatsappCampaignId: true,
    },
  });
  for (const messageByCampaign of messagesByCampaign.filter(
    (el) => el.whatsappCampaignId,
  )) {
    await prisma.whatsappCampaign.update({
      where: {
        id: messageByCampaign.whatsappCampaignId!,
      },
      data: {
        totalRecipients: messageByCampaign._count.whatsappCampaignId,
      },
    });
  }
  console.log('Total recipients fixed!');
  printSeparator();

  console.log('Creating initialPrompts...');
  await prisma.initialPrompt.create({
    data: {
      type: 'generate_whatsapp_message_template',
      promptText: `You will receive parameters in the following structure:
      [parameter]: value
      Replace the each parameter from instructions received by the user.

      You are a company called [nome da empresa] and your customer name is [nome do consumidor]. Working in the Retail industry, selling [descricao do produto]. To celebrate [evento], you want to offer your customers a benefit [valor do cupom] using the discount code [codigo do cupom]. This benefit is valid until [data de validade]. Prepare message to send to our customers over WhatsApp in Portuguese as we are a Brazilian company. It should describe the campaign and make the message playful, personalized and easy to read. The entire message should include exactly 3 emojis. If more than 3 emojis show up, only show the first 3.

      Parameters should only be used once in the message.
      The message should be short, max of 100 words. If a value of a parameter is not given, ignore the parameter and do not include it in the message.
      Use the following steps as reference of how the message should be structured. Make sure it is broken down with double spaces between each sentence.
       1.⁠ ⁠start by saying hello (Olá) to the customer
       2.⁠ ⁠make them feel special and describe the offering
       3.⁠ ⁠let them know the benefits of your products
       4.⁠ ⁠Conclude by letting them know they should access the companies`,
    },
  });
  console.log('InitialPrompts created!');
  printSeparator();

  console.log('Creating audienceRecommendations...');
  await prisma.audienceRecommendation.createMany({
    data: [],
  });
  console.log('AudienceRecommendations created!');
  printSeparator();

  console.log('Creating invoices...');
  await createInvoices(prisma, companies);
  console.log('Invoices created!');
  printSeparator();

  console.log('Creating billingSettings...');
  await createBillingSettings(prisma, companies);

  console.log('Creating billingSettings...');
  await createAutomationTypes(prisma);

  console.log('SEED FINISHED WITH SUCCESS!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
