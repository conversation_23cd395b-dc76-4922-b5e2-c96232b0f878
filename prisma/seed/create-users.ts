import { PrismaClient, Prisma, Company, Role } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const NUM_USERS = 1;

export async function createUsers(
  prisma: PrismaClient,
  companies: Company[],
  roles: Role[],
) {
  const hashPassword = await bcrypt.hash('*********', 10);
  const users: Prisma.UserCreateManyInput[] = [];
  for (const company of companies) {
    const superAdminRole = roles.find(
      (role) => role.name === 'Super Admin' && role.companyId === company.id,
    );
    for (let i = 0; i < NUM_USERS; i++) {
      users.push({
        companyId: company.id,
        name: '<PERSON><PERSON>',
        email: `revi@${company.name}.com`,
        password: hashPassword,
        roleId: superAdminRole?.id,
      });
    }
  }
  return prisma.user.createMany({
    data: users,
    skipDuplicates: true,
  });
}
