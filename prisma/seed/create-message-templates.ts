import { Company, Prisma, PrismaClient } from '@prisma/client';

export async function createMessageTemplates(
  prisma: PrismaClient,
  companies: Company[],
) {
  for (const company of companies) {
    const messageTemplatesData = [
      {
        createdAt: '2024-02-21T13:50:26.593Z',
        updatedAt: '2024-02-21T13:51:01.855Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/paulaferber_extra-sale-e-outlet_revi-768843cb-5ba4-44a0-8b7e-8585390d5802.jpg',
        mediaType: 'image',
        gupshupTemplateId: '5e0b7f18-e8e7-441e-8062-08fb62eaf8a8',
        name: 'extra_sale_e_outlet',
        templateText:
          'Olá [nome do consumidor]!\r\n\r\nA *<PERSON>* está com uma promoção incrível para você aproveitar até 28/01!\r\n\r\nEstamos com até *40% OFF* na nossa Sale Verão & até *70% OFF* no nosso Outlet.🔥\r\n\r\n*+20% OFF* sobre os descontos já vigentes, para você que é nossa cliente especial.\r\n\r\nNão perca essa oportunidade de garantir suas peças preferidas com preços imperdíveis. 😍',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-21T14:28:33.529Z',
        updatedAt: '2024-02-26T14:27:22.696Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/WhatsApp%20Image%202024-02-22%20at%2017.13.52-c1d8df9b-1e49-41d5-ae25-76ee9ab3b0ed.jpeg',
        mediaType: 'image',
        gupshupTemplateId: '44c579ec-fa91-425b-ba69-bf19061f23b2',
        name: 'extra_sale_e_outlet_op2',
        templateText:
          'Olá [nome do consumidor]!\r\n\r\nA *Paula Ferber* está com uma promoção incrível para você aproveitar até 28/01!\r\n\r\nEstamos com até *40% OFF* na nossa Sale Verão & até *70% OFF* no nosso Outlet.🔥\r\n\r\n*+20% OFF* sobre os descontos já vigentes, para você que é nossa cliente especial.\r\n\r\nNão perca essa oportunidade de garantir suas peças preferidas com preços imperdíveis. 😍',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://paulaferber.com/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:29:05.006Z',
        updatedAt: '2024-02-20T21:29:05.006Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/semana Nutri Power-9c3e481c-fd4b-4f15-a320-59db01242785.mp4',
        mediaType: 'video',
        gupshupTemplateId: '30bff50b-4b58-430d-b02c-72ed23a701e4',
        name: 'template_teste_video',
        templateText:
          'Olá [nome do consumidor], tudo bom?\r\n\r\nQueremos que comemorar a semana Nutri com você! \r\n\r\nObrigada!\r\nTime Power Focus',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://www.powerfocus.com.br/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:29:03.070Z',
        updatedAt: '2024-02-20T21:29:03.070Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/Capa foto WhatsApp-64bafdf6-475f-4e81-96fb-c4df59174996.png',
        mediaType: 'image',
        gupshupTemplateId: '689024a5-03c3-4988-8c2c-03a2c396e04b',
        name: 'sale_fim_de_verao',
        templateText:
          'Olá [nome do consumidor], tudo bem?\r\n\r\nEstamos quase no *fim do verão*, mas você ainda pode arrasar! 🔥\r\n\r\nNa [nome da empresa] estamos realizando nosso Sale de Fim de Verão com uma promoção única. \r\n\r\nAproveite o cupom de desconto *[nome do cupom]*, válido até [validade do cupom], e ganhe [desconto]% off na sua compra. 🛍\r\n\r\nNão deixe esta *oportunidade* passar: visite-nos ou compre online agora mesmo!',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://paulaferber.com/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T22:05:19.668Z',
        updatedAt: '2024-02-20T22:05:19.668Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: null,
        name: 'paula_ferber_10',
        templateText:
          'Ola [nome do consumidor], te esperamos na Paula Ferber, *10% off*!',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://paulaferber.com/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'sms',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:55.085Z',
        updatedAt: '2024-02-20T21:28:55.085Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/Capa foto WhatsApp-751fba17-c541-4c11-8925-4bc388c470d2.png',
        mediaType: 'image',
        gupshupTemplateId: 'f2f17f37-269a-47ec-b301-8cd6086b8f55',
        name: 'teste_fluxos_apt_comercial_1',
        templateText:
          'Olá [nome do consumidor]!\r\n\r\nEstamos com uma promo de 50% off quer renovar seu look? 🎁',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:57.951Z',
        updatedAt: '2024-02-20T21:28:57.951Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: '052a986a-faca-432a-90a3-164ceaa64ae1',
        name: 'teste_emoji_botao',
        templateText:
          'Olá [nome do consumidor],\r\n\r\nTudo bem? Espero que tenha um ótimo final de semana!',
        status: 'rejected',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:54.003Z',
        updatedAt: '2024-02-20T21:28:54.003Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/ARTE REVI (1)-f8fd3ce4-7880-4234-93b7-c13d6123251a-49136016-0d90-435c-b375-0974d144bcea.png',
        mediaType: 'image',
        gupshupTemplateId: '206fad10-8f30-453a-98fd-55b47231750c',
        name: 'carrinho_abandonado',
        templateText:
          'Olá [nome do consumidor], tudo bem? \r\n\r\nFalta pouco para você finalizar seu pedido: [nome do produto] da *Power Focus*. Não deixe de garantir sua energia limpa, saudável e duradoura com os nossos produtos! ⚡️\r\n\r\nPara você concluir a sua compra vamos te presentear com *15% de desconto* \r\nUse o cupom: *MEUCARRINHO* 🎊\r\n\r\nQualquer dúvida estamos por aqui para te ajudar!',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://www.powerfocus.com.br/cart',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:29:05.738Z',
        updatedAt: '2024-02-20T21:29:05.738Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/breakup (1)-2c38c7ea-4c62-4b4c-99cb-513d4505459d.pdf',
        mediaType: 'file',
        gupshupTemplateId: 'cfb05f25-fff2-491a-a8da-1be381a4fc21',
        name: 'revi_marketing_resposta',
        templateText: 'Olá, você gostaria de receber um cupom? 🙃',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:44.779Z',
        updatedAt: '2024-02-20T21:28:44.779Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: '9c5fac39-2cc5-4e08-b30c-b1c5f0eb5601',
        name: 'revi_teste_cta',
        templateText:
          'Olá [nome do consumidor], essa é uma mensagem de teste com link no botão',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://www.google.com/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:29:00.760Z',
        updatedAt: '2024-02-20T21:29:00.760Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/Forest Machine Introdução (2)-8a1553e8-0e80-4486-926a-5d27d5eebd58.png',
        mediaType: 'image',
        gupshupTemplateId: '3fae6426-cec2-4e1c-8e1e-669b35209529',
        name: 'revi_marketing_url',
        templateText:
          'Olá [nome do consumidor], visite nosso site e ganhe 20% nos nossos produtos!',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://www.tropicalina.com.br/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:56.972Z',
        updatedAt: '2024-02-20T21:28:56.972Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/breakup (1)-e97a0cda-e219-4b74-908b-ecc19a21aa43.pdf',
        mediaType: 'file',
        gupshupTemplateId: '46b51c45-3443-489d-944e-9d381db3992c',
        name: 'revi_marketing_pdf',
        templateText:
          'Olá, estou enviando nosso catalago para nossa nova coleção! Aproveite 😇',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:29:06.501Z',
        updatedAt: '2024-02-20T21:29:06.501Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/Forest Machine Introdução (2)-41a394b9-7b38-4b42-9d59-45b4ce10d730.png',
        mediaType: 'image',
        gupshupTemplateId: '8b5490a3-19ef-4154-80a4-5d6b260eb6ef',
        name: 'revi_marketing_foto',
        templateText:
          'Olá, aqui é da [nome da empresa]. [nome do consumidor] quero te presentear com 20%.\r\n\r\nObrigada! 😘',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:43.997Z',
        updatedAt: '2024-02-20T21:28:43.997Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/semana Nutri Power-bc0e50fd-9718-41ea-a469-c58bdb087d1d.mp4',
        mediaType: 'video',
        gupshupTemplateId: 'eae9b715-f267-4ca8-b234-e20d40462a49',
        name: 'revi_teste_video',
        templateText: 'Olá [nome do consumidor], este é um template com vídeo.',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:48.579Z',
        updatedAt: '2024-02-20T21:28:48.579Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: '250a3e12-54e6-449a-92f1-6682d36e9b39',
        name: 'envio_codigo_de_rastreio',
        templateText:
          'Olá [nome do consumidor], seu pedido está a caminho.\n\nSeu código de rastreio é: *[codigo de rastreio]*\n\nBasta acessar o link abaixo e inserir o código acima para acompanhar a sua entrega',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://www.bluntbrasil.com.br/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:43.265Z',
        updatedAt: '2024-02-20T21:28:43.265Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: '5dd0d457-52f9-44fc-97f8-275e4ea5fe07',
        name: 'test_hi',
        templateText:
          'Olá meu querido [nome do consumidor], tudo bom?\n\nEsta é uma mensagem de teste\n  \nO que você acha de [texto personalizado].\n  \nObrigado',
        status: 'approved',
        type: 'INITIAL_MESSAGE',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:59.801Z',
        updatedAt: '2024-02-20T21:28:59.801Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/lockscreen-e1541500-cd9d-4c02-b929-9798335b5547.png',
        mediaType: 'image',
        gupshupTemplateId: 'f509f75e-f0d3-4073-b49c-e2b162ae43e7',
        name: 'test_49_staging',
        templateText:
          'Olá [nome do consumidor].\r\n\r\nEsse é um teste da [nome da empresa]. Feito em staging 😀',
        status: 'pending',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:49.310Z',
        updatedAt: '2024-02-20T21:28:49.310Z',
        companyId: company.id,
        mediaUrl: 'https://demo.twilio.com/owl.png',
        mediaType: 'image',
        gupshupTemplateId: '7b6527cc-75e0-474a-b270-60f327c0a3a4',
        name: 'revi_teste_imagem',
        templateText:
          'Olá [nome do consumidor], essa é uma mensagem de teste utilizando imagem',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:47.076Z',
        updatedAt: '2024-02-20T21:28:47.076Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: '8759a6ba-9c1c-4e80-946a-926e58c71c03',
        name: 'revi_template_teste2',
        templateText:
          'Olá [nome do consumidor]! Aqui é da [nome da empresa].\nEssa é uma mensagem de teste',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:40.033Z',
        updatedAt: '2024-02-20T21:28:40.033Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-template-images.s3.amazonaws.com/R.+JOAQUIM+ANTUNES+196+FREE+VALET+(2).jpg',
        mediaType: 'image',
        gupshupTemplateId: 'd0b40244-e904-4505-94d2-0c9ea49ff25a',
        name: 'antlerFeedbackTeste',
        templateText:
          'Olá [nome do consumidor], tudo bom?\n\nEspero que você esteja aproveitando o programa da *Antler* tanto quanto a gente! Em *30 segundinhos*, poderia avaliar sua experiência hoje? 😁',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:52.875Z',
        updatedAt: '2024-02-20T21:28:52.875Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: '72cbc092-3c50-45c9-a11c-dfff0f33c0e5',
        name: 'revi_teste_parametros',
        templateText:
          'Olá [nome do consumidor],\r\n\r\nTudo bom? Você gosta do [nome do produto]? Está em promoção, não perca essa oportunidade.',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://www.tropicalina.com.br/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:51.982Z',
        updatedAt: '2024-02-20T21:28:51.982Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: '8e5520c0-1d9d-4c12-99fd-6802fe950972',
        name: 'teste_fluxos_apt_comercial_3',
        templateText:
          'Maravilha [nome do consumidor]! Acesse o link abaixo para garantir acesso exclusivo. Boas compras! 🎊',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://www.userevi.com',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:29:04.094Z',
        updatedAt: '2024-02-20T21:29:04.094Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/Forest Machine Introdução (2)-882e5810-64a1-43b6-a3d4-aec436133ce5.png',
        mediaType: 'image',
        gupshupTemplateId: '5c3c5d23-48f7-4b40-895c-8e7d4f7644a0',
        name: 'revi_teste_analytics',
        templateText:
          'Olá [nome do consumidor], tudo bem?\r\n\r\nPara comemorar a parceria especial que temos com você, a [nome da empresa] lhe presenteia com um cupom VIP! 🎉\r\n\r\nAproveite agora e faça compras incríveis! 🥳',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://www.tropicalina.com.br/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:29:01.500Z',
        updatedAt: '2024-02-20T21:29:01.500Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/Info Pack Curso Faap-e54845c7-f807-487d-9189-6044f566cec2.pdf',
        mediaType: 'file',
        gupshupTemplateId: 'f7bcf1b8-5cb6-4670-9594-84e8d3d71b52',
        name: 'fluxo_faap_2',
        templateText:
          '🎉 Parabéns pelo seu interesse no curso de negócios da Faculdade FAAP, [nome do consumidor]! 🎓\r\n\r\nPara fornecer a você todas as informações necessárias e ajudá-lo em sua decisão, preparamos um pacote exclusivo de materiais informativos que você acessa no PDF acima.\r\n\r\nPara formalizar a sua inscrição, *acesse o site no botão abaixo e preencha o formulário*.',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://www.faap.br/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:29:02.230Z',
        updatedAt: '2024-02-20T21:29:02.230Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: '5c38e8dd-8657-427b-895f-0670e1ff0544',
        name: 'teste_fluxos_apt_comercial_2',
        templateText: 'Maravilha [nome do consumidor], aproveite!',
        status: 'rejected',
        type: 'MARKETING',
        ctaLink: 'https://ahlex.com.br/credito/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:51.093Z',
        updatedAt: '2024-02-20T21:28:51.093Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: '3bc099fa-5982-4a71-baa1-ded90d796837',
        name: 'revi_marketing_texto',
        templateText:
          'Olá [nome do consumidor], aqui é da [nome da empresa]. Obrigada por todo seu apoio. Segue um cupom de 20% 🥰',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:45.514Z',
        updatedAt: '2024-02-20T21:28:45.514Z',
        companyId: company.id,
        mediaUrl: 'https://demo.twilio.com/owl.png',
        mediaType: 'image',
        gupshupTemplateId: 'f03d9273-eb44-4457-8fe3-1ee31b09fcab',
        name: 'revi_template_botaoteste',
        templateText:
          'Olá [nome do consumidor], tudo bom?\n\nVocê é proprietário do terreno? Você teria interesse em venda ou arrendamento? 🌳',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:47.840Z',
        updatedAt: '2024-02-20T21:28:47.840Z',
        companyId: company.id,
        mediaUrl: 'https://www.africau.edu/images/default/sample.pdf',
        mediaType: 'file',
        gupshupTemplateId: '15902d62-c8a7-4507-bdf5-717eeabe37a9',
        name: 'revi_template_test_pdf',
        templateText:
          'Olá [nome do consumidor], esta é uma mensagem de teste contendo PDF.',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:56.049Z',
        updatedAt: '2024-02-20T21:28:56.049Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/gabarito faap-2dab3b2b-d7a6-4db9-aa3c-a9fb6ae3b0ee.pdf',
        mediaType: 'file',
        gupshupTemplateId: '35e68cd9-f638-418d-a673-79e5bdbbde82',
        name: 'gabarito_faap',
        templateText:
          '📢 Atualização Importante: Gabarito da Prova Presencial - 18/06 📚\r\n\r\n👋 Olá,[nome do consumidor]! Temos uma ótima notícia para vocês! O gabarito da prova presencial realizada em 18/06 já está disponível. 🎉\r\n\r\n📖 Acesse o gabarito no arquivo acima!\r\n\r\n✅ Certifique-se de conferir suas respostas e compará-las com o gabarito oficial. Isso permitirá que você avalie seu desempenho e faça uma análise precisa do seu progresso acadêmico.\r\n\r\n🔔 Fique atento aos prazos de recursos e demais orientações fornecidas pelo corpo docente em relação à revisão de provas. Caso tenha alguma dúvida ou precise de assistência adicional, entre em contato com o departamento acadêmico da faculdade.\r\n\r\n📚 Continuem o excelente trabalho e lembrem-se de que estamos aqui para apoiá-los em sua jornada educacional.\r\n\r\n🌐 Para mais informações sobre a Faculdade FAAP e nossos cursos, acesse o link abaixo. \r\n📞 Em caso de dúvidas, entre em contato conosco.\r\n\r\nAgradecemos pela sua dedicação e parabéns por mais essa etapa concluída! 👏✨',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: 'https://www.faap.br/',
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:29:07.231Z',
        updatedAt: '2024-02-20T21:29:07.231Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: 'b9f6a0bb-a867-449a-9a56-e39c356d9685',
        name: 'teste_2110',
        templateText: 'Mais um teste do [nome do consumidor]',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:58.859Z',
        updatedAt: '2024-02-20T21:28:58.859Z',
        companyId: company.id,
        mediaUrl:
          'https://revi-backend-staging.s3.amazonaws.com/whatsapp/faap-og-8a3e936f-15a0-4f7c-b2cd-5aba981594d5.jpeg',
        mediaType: 'image',
        gupshupTemplateId: '43bad023-d29c-44ae-bff0-27bfb3c2b05f',
        name: 'fluxo_faap',
        templateText:
          '👋 Olá [nome do consumidor]! Você está pronto para dar um salto em sua carreira? A Faculdade FAAP está entusiasmada em anunciar seu novo curso de negócios, projetado para impulsionar suas habilidades empresariais e abrir portas para um futuro de sucesso. 💼📚 Este programa exclusivo da FAAP oferece uma combinação perfeita de teoria e prática, com corpo docente renomado e conteúdo atualizado para preparar você para o mundo dos negócios de hoje. 🌍🎯 Quer saber mais? Responda com os botões abaixo!',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
      {
        createdAt: '2024-02-20T21:28:46.293Z',
        updatedAt: '2024-02-20T21:28:46.293Z',
        companyId: company.id,
        mediaUrl: null,
        mediaType: null,
        gupshupTemplateId: '23bb4ea4-de3d-4df4-a3b7-7b442f5d6644',
        name: 'revi_marketing_intro',
        templateText:
          'Olá [nome do consumidor], tudo bom?\n\nVocê teria interesse de receber nossas novidades e lançamentos?',
        status: 'approved',
        type: 'MARKETING',
        ctaLink: null,
        messageTemplateSuggestionId: null,
        communicationChannel: 'whatsapp',
        whatsappTemplateCategory: 'MARKETING',
      },
    ] as any;

    await prisma.messageTemplate.createMany({
      data: messageTemplatesData,
      skipDuplicates: true,
    });
  }
}
