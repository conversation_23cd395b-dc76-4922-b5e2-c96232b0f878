import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma, Company } from '@prisma/client';

export async function createCampaignRecipients(
  prisma: PrismaClient,
  companies: Company[],
) {
  return await createSmsCampaignRecipients(prisma, companies);
}

async function createSmsCampaignRecipients(
  prisma: PrismaClient,
  companies: Company[],
) {
  const campaignRecipients: Prisma.CampaignRecipientCreateManyInput[] = [];

  for (const company of companies) {
    const campaigns = await prisma.smsCampaign.findMany({
      where: {
        companyId: company.id,
      },
      select: {
        id: true,
      },
    });

    const customers = await prisma.customer.findMany({
      where: {
        companyId: company.id,
      },
      select: {
        id: true,
      },
    });

    for (const campaign of campaigns) {
      const recipientCount = faker.number.int({ min: 5, max: 25 });

      const selectedCustomers = faker.helpers.arrayElements(
        customers,
        Math.min(recipientCount, customers.length),
      );

      for (const customer of selectedCustomers) {
        campaignRecipients.push({
          smsCampaignId: campaign.id,
          customerId: customer.id,
          type: 'standard',
          createdAt: faker.date.recent({ days: 30 }),
          updatedAt: faker.date.recent({ days: 30 }),
        });
      }

      await prisma.smsCampaign.update({
        where: { id: campaign.id },
        data: {
          totalRecipients: recipientCount,
        },
      });
    }
  }

  return await prisma.campaignRecipient.createMany({
    data: campaignRecipients,
    skipDuplicates: true,
  });
}
