import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma, Company } from '@prisma/client';
import { addDays, subDays } from 'date-fns';

const NUM_ORDERS = 5;
const ORDER_STATUSES = [
  '<PERSON>dente',
  'Pago',
  '<PERSON><PERSON><PERSON>',
  'Em separação',
  'Envia<PERSON>',
  'Entregue',
  'Cancelado',
  'Dev<PERSON>vido',
  '<PERSON><PERSON><PERSON>sad<PERSON>',
  'Aguardando pagamento',
  'Atrasado',
  'Em análise',
  'Autorizado',
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>and<PERSON>',
  '<PERSON>cusado',
  'Erro no pagamento',
  '<PERSON><PERSON>ando retirada',
  '<PERSON>tirado',
  'Em transporte',
  'Extraviado',
  'Em disputa',
  'Reembolsado parcialmente',
];

async function createSmsCampaignOrders(prisma: PrismaClient) {
  const orders: Prisma.OrderCreateManyInput[] = [];

  const smsCampaigns = await prisma.smsCampaign.findMany({
    where: {
      status: 'completed',
    },
    include: {
      campaignRecipients: {
        include: {
          customer: true,
        },
      },
      messages: {
        orderBy: {
          createdAt: 'asc',
        },
        take: 1,
      },
    },
  });

  for (const campaign of smsCampaigns) {
    // Skip if no messages were sent
    if (!campaign.messages.length) continue;

    const firstMessageDate = campaign.messages[0].createdAt;

    // For each recipient, create an order with 30% probability
    for (const recipient of campaign.campaignRecipients) {
      // 30% chance to create an order
      if (Math.random() > 0.3) continue;

      // Random order date between 1-7 days after message
      const orderDate = addDays(
        firstMessageDate,
        Math.floor(Math.random() * 4) + 1,
      );

      // Create order
      orders.push({
        source: 'direct_message',
        sourceId: `sms-campaign-${campaign.id}-${recipient.customerId}`,
        sourceCreatedAt: orderDate,
        sourceUpdatedAt: orderDate,
        value: Math.floor(Math.random() * 100000) + 1000, // Random value between 10-1000
        status: 'completed',
        companyId: campaign.companyId,
        customerId: recipient.customerId,
        totalItemsValue: Math.floor(Math.random() * 100000) + 1000,
        totalItemsQuantity: Math.floor(Math.random() * 5) + 1,
        totalDiscountsValue: 0,
        totalShippingValue: 1000,
        totalTaxValue: 0,
        currency: 'BRL',
      });
    }
  }

  await prisma.order.createMany({
    data: orders,
    skipDuplicates: true,
  });
}

async function createWhatsappCampaignOrders(
  prisma: PrismaClient,
  companies: Company[],
) {
  const orders: Prisma.OrderCreateManyInput[] = [];
  const products: Prisma.OrderProductCreateManyInput[] = [];
  const orderItems: Prisma.OrderItemCreateManyInput[] = [];

  for (const company of companies) {
    const customers = await prisma.customer.findMany({
      where: {
        companyId: company.id,
        conversation: {
          messages: {
            some: {
              whatsappCampaignId: {
                not: null,
              },
            },
          },
        },
      },
    });

    for (const customer of customers) {
      for (let i = 0; i < faker.number.int({ min: 0, max: 10 }); i++) {
        const orderId = faker.string.uuid();
        orders.push({
          id: orderId,
          companyId: company.id,
          customerId: customer.id,
          sourceId: faker.string.uuid(),
          sourceCreatedAt: subDays(
            new Date(),
            faker.number.int({ min: 0, max: 2 }),
          ),
          sourceUpdatedAt: subDays(
            new Date(),
            faker.number.int({ min: 0, max: 2 }),
          ),
          status: faker.helpers.arrayElement(ORDER_STATUSES),
          value: faker.number.int({ min: 1000, max: 50000 }),
          totalItemsValue: faker.number.int({ min: 1000, max: 50000 }),
          totalDiscountsValue: faker.number.int({ min: 0, max: 1000 }),
          totalShippingValue: faker.number.int({ min: 0, max: 500 }),
          totalTaxValue: faker.number.int({ min: 0, max: 500 }),
          totalItemsQuantity: faker.number.int({ min: 1, max: 10 }),
        });

        const numProducts = faker.number.int({ min: 1, max: 5 });
        for (let j = 0; j < numProducts; j++) {
          const productId = faker.string.uuid();
          products.push({
            id: productId,
            companyId: company.id,
            name: faker.commerce.productName(),
          });

          orderItems.push({
            orderId,
            productId,
            quantity: faker.number.int({ min: 1, max: 10 }),
          });
        }
      }
    }
  }

  await prisma.order.createMany({
    data: orders,
    skipDuplicates: true,
  });

  await prisma.orderProduct.createMany({
    data: products,
    skipDuplicates: true,
  });

  await prisma.orderItem.createMany({
    data: orderItems,
    skipDuplicates: true,
  });
}

export async function createOrders(prisma: PrismaClient, companies: Company[]) {
  await createSmsCampaignOrders(prisma);
  await createWhatsappCampaignOrders(prisma, companies);
}
