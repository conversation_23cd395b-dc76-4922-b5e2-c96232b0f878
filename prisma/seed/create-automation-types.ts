import { PrismaClient } from '@prisma/client';

export async function createAutomationTypes(prisma: PrismaClient) {
  return prisma.automationType.createMany({
    data: [
      {
        id: '4utom4t3-9882-431e-9b8c-32fc893561c7',
        name: 'Carrin<PERSON>band<PERSON>do',
        slug: 'abandoned_cart',
        sourceIntegration: 'vtex_ecommerce',
      },
      {
        id: '4utom4t3-5b0d-4b9b-9560-2858d7e100f6',
        name: '<PERSON><PERSON><PERSON> de Rastream<PERSON>o',
        slug: 'tracking_code',
        sourceIntegration: 'vtex_ecommerce',
      },
      {
        id: '4utom4t3-8a3c-4a3d-85c2-1386ffc4d651',
        name: 'Carrinho Abandonado',
        slug: 'abandoned_cart',
        sourceIntegration: 'shopify_ecommerce',
      },
      {
        id: '4utom4t3-3994-4cbe-9ce3-9c6714f337a0',
        name: '<PERSON><PERSON><PERSON>stream<PERSON>o',
        slug: 'tracking_code',
        sourceIntegration: 'shopify_ecommerce',
      },
      {
        id: '4utom4t3-eacc-4cc8-8d58-e7965441d287',
        name: 'Carrinho Abandonado',
        slug: 'abandoned_cart',
        sourceIntegration: 'visual_ecommerce',
      },
      {
        id: '4utom4t3-f06a-48fa-9e9e-88e576ccac98',
        name: 'Carrinho Abandonado',
        slug: 'abandoned_cart',
        sourceIntegration: 'woo_commerce',
      },
      {
        id: '4utom4t3-52f9-493c-b7c5-4b8f75072f5c',
        name: 'Código de Rastreamento',
        slug: 'tracking_code',
        sourceIntegration: 'woo_commerce',
      },
      {
        id: '4utom4t3-618b-4df8-91ce-925013bff7a2',
        name: 'Boas Vindas',
        slug: 'welcome_registration',
        sourceIntegration: 'woo_commerce',
      },
      {
        id: '4utom4t3-d8da-445a-b3fc-c427228665ee',
        name: 'Novo Pedido',
        slug: 'new_order',
        sourceIntegration: 'woo_commerce',
      },
      {
        id: '4utom4t3-352f-4bf3-9946-16f45b76e6aa',
        name: 'Confirmação de Pagamento',
        slug: 'order_payment_confirmation',
        sourceIntegration: 'woo_commerce',
      },
      {
        id: '4utom4t3-32e2-4873-89f6-2b8ae90caf38',
        name: 'Carrinho Abandonado',
        slug: 'abandoned_cart',
        sourceIntegration: 'unbox',
      },
      {
        id: '4utom4t3-b607-45f1-b386-a0beffa6bf79',
        name: 'Carrinho Abandonado',
        slug: 'abandoned_cart',
        sourceIntegration: 'yampi',
      },
      {
        id: '4utom4t3-74e8-4826-8f25-9320f7e9d05c',
        name: 'Código de Rastreamento',
        slug: 'tracking_code',
        sourceIntegration: 'omie',
      },
      {
        id: '4utom4t3-a171-4b71-b61a-01ae4b70e22a',
        name: 'Carrinho Abandonado',
        slug: 'abandoned_cart',
        sourceIntegration: 'cartPanda',
      },
      {
        id: '4utom4t3-a1b2-c3d4-e5f6-1234567890ab',
        name: 'Status de Pedido - Cancelado',
        slug: 'order_status_update',
        sourceIntegration: 'shopify_ecommerce',
      },
      {
        id: '4utom4t3-b2c3-d4e5-f6a1-2345678901bc',
        name: 'Status de Pedido - Pago',
        slug: 'order_status_update',
        sourceIntegration: 'shopify_ecommerce',
      },
      {
        id: '4utom4t3-c3d4-e5f6-a1b2-3456789012cd',
        name: 'Status de Pedido - Criado',
        slug: 'order_status_update',
        sourceIntegration: 'shopify_ecommerce',
      },
      {
        id: '4utom4t3-d4e5-f6a1-b2c3-4567890123de',
        name: 'Status de Pedido - Entregue',
        slug: 'order_status_update',
        sourceIntegration: 'shopify_ecommerce',
      },
      {
        id: '4utom4t3-a1b2-c3d4-e5f6-7890abcdef12',
        name: 'Status de Pedido - Cancelado',
        slug: 'order_status_update',
        sourceIntegration: 'vtex_ecommerce',
      },
      {
        id: '4utom4t3-1234-5678-90ab-cdef34567890',
        name: 'Status de Pedido - Pago',
        slug: 'order_status_update',
        sourceIntegration: 'vtex_ecommerce',
      },
      {
        id: '4utom4t3-fedc-ba98-7654-3210abcdef99',
        name: 'Status de Pedido - Criado',
        slug: 'order_status_update',
        sourceIntegration: 'vtex_ecommerce',
      },
      {
        id: '4utom4t3-1a2b-3c4d-e5f6-7g8h9i0j1k2l',
        name: 'Status de Pedido - Entregue',
        slug: 'order_status_update',
        sourceIntegration: 'vtex_ecommerce',
      },
      {
        id: '4utom4t3-1a2b-3c4d-e5f6-7g8h9i0j1k2l',
        name: 'Status de Pedido - Cancelado',
        slug: 'order_status_update',
        sourceIntegration: 'tiny',
      },
      {
        id: '4utom4t3-2a3b-4c5d-e6f7-8g9h0i1j2k3l',
        name: 'Status de Pedido - Pago',
        slug: 'order_status_update',
        sourceIntegration: 'tiny',
      },
      {
        id: '4utom4t3-3a4b-5c6d-e7f8-9g0h1i2j3k4l',
        name: 'Status de Pedido - Criado',
        slug: 'order_status_update',
        sourceIntegration: 'tiny',
      },
      {
        id: '4utom4t3-1a2b-3c4d-e5f6-7g8h9i0j1k2l',
        name: 'Status de Pedido - Entregue',
        slug: 'order_status_update',
        sourceIntegration: 'tiny',
      },
      {
        id: '4utom4t3-1b2c-3d4e-f5g6-7h8i9j0k1l2m',
        name: 'Status de Pedido - Cancelado',
        slug: 'order_status_update',
        sourceIntegration: 'tray',
      },
      {
        id: '4utom4t3-2b3c-4d5e-f6g7-8h9i0j1k2l3m',
        name: 'Status de Pedido - Pago',
        slug: 'order_status_update',
        sourceIntegration: 'tray',
      },
      {
        id: '4utom4t3-3b4c-5d6e-f7g8-9h0i1j2k3l4m',
        name: 'Status de Pedido - Criado',
        slug: 'order_status_update',
        sourceIntegration: 'tray',
      },
      {
        id: '4utom4t3-4b5c-6d7e-f8g9-0h1i2j3k4l5m',
        name: 'Status de Pedido - Entregue',
        slug: 'order_status_update',
        sourceIntegration: 'tray',
      },
      {
        id: '4utom4t3-1c2d-3e4f-g5h6-7i8j9k0l1m2n',
        name: 'Status de Pedido - Cancelado',
        slug: 'order_status_update',
        sourceIntegration: 'shoppub',
      },
      {
        id: '4utom4t3-2c3d-4e5f-g6h7-8i9j0k1l2m3n',
        name: 'Status de Pedido - Pago',
        slug: 'order_status_update',
        sourceIntegration: 'shoppub',
      },
      {
        id: '4utom4t3-3c4d-5e6f-g7h8-9i0j1k2l3m4n',
        name: 'Status de Pedido - Criado',
        slug: 'order_status_update',
        sourceIntegration: 'shoppub',
      },
      {
        id: '4utom4t3-4c5d-6e7f-g8h9-i0j1k2l3m4n',
        name: 'Status de Pedido - Entregue',
        slug: 'order_status_update',
        sourceIntegration: 'shoppub',
      },
      {
        id: '4utom4t3-1d2e-3f4g-h5i6-7j8k9l0m1n2o',
        name: 'Status de Pedido - Cancelado',
        slug: 'order_status_update',
        sourceIntegration: 'omny',
      },
      {
        id: '4utom4t3-2d3e-4f5g-h6i7-8j9k0l1m2n3o',
        name: 'Status de Pedido - Pago',
        slug: 'order_status_update',
        sourceIntegration: 'omny',
      },
      {
        id: '4utom4t3-3d4e-5f6g-h7i8-9j0k1l2m3n4o',
        name: 'Status de Pedido - Criado',
        slug: 'order_status_update',
        sourceIntegration: 'omny',
      },
      {
        id: '4utom4t3-4d5e-6f7g-h8i9-j0k1l2m3n4o',
        name: 'Status de Pedido - Entregue',
        slug: 'order_status_update',
        sourceIntegration: 'omny',
      },
      {
        id: '4utom4t3-1e2f-3g4h-i5j6-7k8l9m0n1o2p',
        name: 'Status de Pedido - Cancelado',
        slug: 'order_status_update',
        sourceIntegration: 'omie',
      },
      {
        id: '4utom4t3-2e3f-4g5h-i6j7-8k9l0m1n2o3p',
        name: 'Status de Pedido - Pago',
        slug: 'order_status_update',
        sourceIntegration: 'omie',
      },
      {
        id: '4utom4t3-3e4f-5g6h-i7j8-9k0l1m2n3o4p',
        name: 'Status de Pedido - Criado',
        slug: 'order_status_update',
        sourceIntegration: 'omie',
      },
      {
        id: '4utom4t3-4e5f-6g7h-i8j9-k0l1m2n3o4p',
        name: 'Status de Pedido - Entregue',
        slug: 'order_status_update',
        sourceIntegration: 'omie',
      },
      {
        id: '4utom4t3-1f2g-3h4i-j5k6-7l8m9n0o1p2q',
        name: 'Status de Pedido - Cancelado',
        slug: 'order_status_update',
        sourceIntegration: 'nuvem_shop',
      },
      {
        id: '4utom4t3-2f3g-4h5i-j6k7-8l9m0n1o2p3q',
        name: 'Status de Pedido - Pago',
        slug: 'order_status_update',
        sourceIntegration: 'nuvem_shop',
      },
      {
        id: '4utom4t3-3f4g-5h6i-j7k8-9l0m1n2o3p4q',
        name: 'Status de Pedido - Criado',
        slug: 'order_status_update',
        sourceIntegration: 'nuvem_shop',
      },
      {
        id: '4utom4t3-4f5g-6h7i-j8k9-l0m1n2o3p4q',
        name: 'Status de Pedido - Entregue',
        slug: 'order_status_update',
        sourceIntegration: 'nuvem_shop',
      },
      {
        id: '4utom4t3-1g2h-3i4j-k5l6-7m8n9o0p1q2r',
        name: 'Status de Pedido - Cancelado',
        slug: 'order_status_update',
        sourceIntegration: 'linx_commerce',
      },
      {
        id: '4utom4t3-2g3h-4i5j-k6l7-8m9n0o1p2q3r',
        name: 'Status de Pedido - Pago',
        slug: 'order_status_update',
        sourceIntegration: 'linx_commerce',
      },
      {
        id: '4utom4t3-3g4h-5i6j-k7l8-9m0n1o2p3q4r',
        name: 'Status de Pedido - Criado',
        slug: 'order_status_update',
        sourceIntegration: 'linx_commerce',
      },
      {
        id: '4utom4t3-4g5h-6i7j-k8l9-m0n1o2p3q4r',
        name: 'Status de Pedido - Entregue',
        slug: 'order_status_update',
        sourceIntegration: 'linx_commerce',
      },
      {
        id: '4utom4t3-1h2i-3j4k-l5m6-7n8o9p0q1r2s',
        name: 'Status de Pedido - Cancelado',
        slug: 'order_status_update',
        sourceIntegration: 'cartPanda',
      },
      {
        id: '4utom4t3-2h3i-4j5k-l6m7-8n9o0p1q2r3s',
        name: 'Status de Pedido - Pago',
        slug: 'order_status_update',
        sourceIntegration: 'cartPanda',
      },
      {
        id: '4utom4t3-3h4i-5j6k-l7m8-9n0o1p2q3r4s',
        name: 'Status de Pedido - Criado',
        slug: 'order_status_update',
        sourceIntegration: 'cartPanda',
      },
      {
        id: '4utom4t3-4h5i-6j7k-l8m9-n0o1p2q3r4s',
        name: 'Status de Pedido - Entregue',
        slug: 'order_status_update',
        sourceIntegration: 'cartPanda',
      },
    ],
    skipDuplicates: true,
  });
}
