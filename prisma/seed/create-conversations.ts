import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma, Company } from '@prisma/client';

export async function createConversations(
  prisma: PrismaClient,
  companies: Company[],
) {
  const conversations: Prisma.ConversationCreateManyInput[] = [];
  for (const company of companies) {
    const customers = await prisma.customer.findMany({
      where: {
        companyId: company.id,
      },
    });
    for (const customer of customers) {
      if (customer.phoneNumberId) {
        conversations.push({
          createdAt: faker.date.recent(),
          updatedAt: faker.date.recent(),
          id: faker.string.uuid(),
          recipientPhoneNumberId: customer.phoneNumberId,
          recipientName: faker.person.fullName(),
          customerId: customer.id,
          companyId: company.id,
        });
      }
    }
  }
  return prisma.conversation.createMany({
    data: conversations,
    skipDuplicates: true,
  });
}
