import { PrismaClient, Company, Prisma, AppModule } from '@prisma/client';
import { SUPER_ADMIN_ROLE_NAME } from './constants/roles';

const nonAdminAppModulePermissionsArr: AppModule[] = [
  AppModule.HOME,
  AppModule.AUTOMATIONS,
  AppModule.CAMPAIGNS,
  AppModule.CHAT,
  AppModule.CUSTOMERS,
  AppModule.REPORTS,
  AppModule.TEMPLATES,
  AppModule.PRODUCTS,
];

const roles: Prisma.RoleCreateManyInput[] = [
  {
    name: SUPER_ADMIN_ROLE_NAME,
    appModulePermissions: [
      AppModule.DEBUG_TOOLS,
      AppModule.SETTINGS,
      ...nonAdminAppModulePermissionsArr,
    ],
    companyId: '',
  },
  {
    name: 'Admin',
    appModulePermissions: [
      AppModule.SETTINGS,
      ...nonAdminAppModulePermissionsArr,
    ],
    companyId: '',
  },
  {
    name: 'Agent',
    appModulePermissions: nonAdminAppModulePermissionsArr,
    companyId: '',
  },
];

export async function createRoles(prisma: PrismaClient, companies: Company[]) {
  const data = companies
    .map((company) =>
      roles.map(
        (role) =>
          ({
            ...role,
            companyId: company.id,
          } as Prisma.RoleCreateManyInput),
      ),
    )
    .flat();

  return prisma.role.createMany({
    data,
    skipDuplicates: true,
  });
}
