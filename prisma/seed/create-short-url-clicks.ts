import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma, Company } from '@prisma/client';

const PROBABILITY_OF_CLICK = 0.3;

async function createSmsShortUrlClicks(
  prisma: PrismaClient,
  companies: Company[],
) {
  const shortUrlClicks: Prisma.ShortUrlClickCreateManyInput[] = [];
  for (const company of companies) {
    let smsShortUrls = await prisma.shortUrl.findMany({
      where: {
        companyId: company.id,
        smsMessageId: {
          not: null,
        },
      },
    });

    smsShortUrls = smsShortUrls.slice(0, smsShortUrls.length / 2);

    for (const smsShortUrl of smsShortUrls) {
      shortUrlClicks.push({
        shortUrlId: smsShortUrl.id,
      });
    }
  }

  return prisma.shortUrlClick.createMany({
    data: shortUrlClicks,
    skipDuplicates: true,
  });
}

async function createWhatsappShortUrlClicks(
  prisma: PrismaClient,
  companies: Company[],
) {
  const shortUrlClicks: Prisma.ShortUrlClickCreateManyInput[] = [];
  for (const company of companies) {
    const shortUrls = await prisma.shortUrl.findMany({
      where: {
        companyId: company.id,
      },
    });
    for (const shortUrl of shortUrls) {
      if (Math.random() > PROBABILITY_OF_CLICK) continue;
      shortUrlClicks.push({
        shortUrlId: shortUrl.id,
        createdAt: faker.date.recent({
          days: faker.number.int({ min: 1, max: 3 }),
        }),
      });
    }
  }
  return prisma.shortUrlClick.createMany({
    data: shortUrlClicks,
    skipDuplicates: true,
  });
}

export async function createShortUrlClicks(
  prisma: PrismaClient,
  companies: Company[],
) {
  await createSmsShortUrlClicks(prisma, companies);
  await createWhatsappShortUrlClicks(prisma, companies);
}
