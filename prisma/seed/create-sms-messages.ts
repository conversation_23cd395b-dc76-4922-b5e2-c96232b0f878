import { faker } from '@faker-js/faker';
import { Prisma, PrismaClient, MessageStatus } from '@prisma/client';

export async function createSmsMessages(prisma: PrismaClient) {
  const messages: Prisma.SmsMessageCreateManyInput[] = [];

  const smsCampaigns = await prisma.smsCampaign.findMany({
    select: {
      id: true,
      messageTemplateId: true,
      campaignRecipients: {
        select: {
          customer: {
            select: {
              phoneNumberId: true,
            },
          },
        },
      },
    },
  });

  for (const campaign of smsCampaigns) {
    for (const recipient of campaign.campaignRecipients) {
      const status = faker.helpers.arrayElement([
        MessageStatus.sent,
        MessageStatus.delivered,
        MessageStatus.failed,
      ]);

      if (!recipient.customer.phoneNumberId) {
        continue;
      }

      const message: Prisma.SmsMessageCreateManyInput = {
        text: faker.lorem.sentence(),
        recipientPhoneNumberId: recipient.customer.phoneNumberId,
        messageTemplateId: campaign.messageTemplateId,
        status,
        smsCampaignId: campaign.id,
        smsId: faker.string.uuid(),
        errorMessage:
          status === MessageStatus.failed ? faker.lorem.sentence() : null,
        createdAt: faker.date.recent({ days: 30 }),
        updatedAt: faker.date.recent({ days: 30 }),
      };

      messages.push(message);
    }
  }

  return await prisma.smsMessage.createMany({
    data: messages,
    skipDuplicates: true,
  });
}
