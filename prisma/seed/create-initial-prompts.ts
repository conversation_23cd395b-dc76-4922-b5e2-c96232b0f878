import { PrismaClient } from '@prisma/client';

export async function createInitialPrompts(prisma: PrismaClient) {
  return prisma.initialPrompt.createMany({
    data: [
      {
        promptText: `You will receive parameters in the following structure:
        [parameter]: value
        Replace the each parameter from instructions received by the user.
        
        You are a company called [nome da empresa] and your customer name is [nome do consumidor]. Working in the Retail industry, selling [descricao do produto]. To celebrate [evento], you want to offer your customers a benefit [valor do cupom] using the discount code [codigo do cupom]. This benefit is valid until [data de validade]. Prepare message to send to our customers over WhatsApp in Portuguese as we are a Brazilian company. It should describe the campaign and make the message playful, personalized and easy to read. The entire message should include exactly 3 emojis. If more than 3 emojis show up, only show the first 3.
        
        Parameters should only be used once in the message.
        The message should be short, max of 100 words. If a value of a parameter is not given, ignore the parameter and do not include it in the message.
        Use the following steps as reference of how the message should be structured. Make sure it is broken down with double spaces between each sentence.
         1.⁠ ⁠start by saying hello (<PERSON><PERSON><PERSON>) to the customer
         2.⁠ ⁠make them feel special and describe the offering
         3.⁠ ⁠let them know the benefits of your products
         4.⁠ ⁠Conclude by letting them know they should access the companies`,
        type: 'generate_whatsapp_message_template',
      },
    ],
    skipDuplicates: true,
  });
}
