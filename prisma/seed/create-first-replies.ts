import { faker } from '@faker-js/faker';
import { PrismaClient, Prisma, Company } from '@prisma/client';

const PROBABILITY_OF_REPLY = 0.4;

export async function createFirstReplies(
  prisma: PrismaClient,
  companies: Company[],
) {
  const messages: Prisma.MessageCreateManyInput[] = [];

  for (const company of companies) {
    const conversations = await prisma.conversation.findMany({
      where: {
        companyId: company.id,
      },
    });
    for (const conversation of conversations) {
      if (Math.random() > PROBABILITY_OF_REPLY) continue;
      messages.push({
        wamId: faker.string.uuid(),
        createdAt: faker.date.recent({
          days: faker.number.int({ min: 1, max: 3 }),
        }),
        tempId: faker.string.uuid(),
        text: faker.lorem.sentence(),
        senderPhoneNumberId: conversation.recipientPhoneNumberId,
        recipientPhoneNumberId: company.phoneNumberId,
        conversationId: conversation.id,
        fromSystem: false,
      });
    }
  }
  return prisma.message.createMany({
    data: messages,
    skipDuplicates: true,
  });
}
