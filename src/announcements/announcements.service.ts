import { Injectable, NotFoundException } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { isBefore, subDays } from 'date-fns';
import { JwtPayload } from 'jsonwebtoken';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class AnnouncementsService {
  constructor(private readonly prismaService: PrismaService) {}

  async listAnnouncements(
    userTokenData: JwtPayload,
    options: {
      lastXDays?: number;
      viewed?: boolean;
    },
  ) {
    const { lastXDays = 15, viewed } = options;
    const user = await this.prismaService.user.findUnique({
      where: { id: userTokenData.sub },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const fifteenDaysAgo = subDays(new Date(), lastXDays);
    const lastViewedAt = user.lastAnnouncementViewedAt
      ? new Date(user.lastAnnouncementViewedAt)
      : null;

    const lastViewedIsRecent = lastViewedAt
      ? !isBefore(lastViewedAt, fifteenDaysAgo)
      : false;

    const filterBaseDate =
      lastViewedIsRecent && lastViewedAt ? lastViewedAt : fifteenDaysAgo;

    const createdAtFilter: Prisma.DateTimeFilter =
      viewed === false
        ? { gt: filterBaseDate }
        : viewed === true
        ? { lte: filterBaseDate }
        : { gt: fifteenDaysAgo };

    const where = {
      OR: [{ companyId: user.companyId }, { companyId: null }],
      createdAt: createdAtFilter,
    };

    const announcements = await this.prismaService.announcement.findMany({
      where,
    });

    if (announcements.length === 0) {
      return [];
    }

    await this.prismaService.user.update({
      where: {
        id: user.id,
      },
      data: { lastAnnouncementViewedAt: new Date() },
    });

    return announcements;
  }

  async createAnnouncement(title: string, html: string, companyId?: string) {
    return this.prismaService.announcement.create({
      data: { html, companyId, title },
    });
  }
}
