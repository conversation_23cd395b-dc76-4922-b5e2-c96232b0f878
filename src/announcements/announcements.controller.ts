import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AnnouncementsService } from './announcements.service';
import { RequestWithJwt } from 'src/shared/types/RequestWithJwt';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { RetoolAuthGuard } from 'src/auth/retool-auth.guard';
import { createAnnouncementDto } from './dto/create-announcement.dto';

@Controller('announcements')
export class AnnouncementsController {
  constructor(private readonly announcementsService: AnnouncementsService) {}

  @UseGuards(JwtAuthGuard)
  @Get()
  async listAnnouncements(
    @Req() req: RequestWithJwt,
    @Query('viewed') viewed?: boolean,
  ) {
    return this.announcementsService.listAnnouncements(req.user, { viewed });
  }

  @UseGuards(RetoolAuthGuard)
  @Post()
  async createAnnouncement(
    @Body() createAnnouncementDto: createAnnouncementDto,
  ) {
    return await this.announcementsService.createAnnouncement(
      createAnnouncementDto.title,
      createAnnouncementDto.html,
    );
  }
}
