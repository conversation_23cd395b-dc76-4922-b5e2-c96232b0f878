import { Module } from '@nestjs/common';
import { AnnouncementsController } from './announcements.controller';
import { AnnouncementsService } from './announcements.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  controllers: [AnnouncementsController],
  providers: [AnnouncementsService],
  imports: [PrismaModule, ConfigModule],
})
export class AnnouncementsModule {}
