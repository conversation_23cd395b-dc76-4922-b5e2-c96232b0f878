import { Module, forwardRef } from '@nestjs/common';
import { VtexService } from './vtex.service';
import { CompaniesModule } from 'src/companies/companies.module';
import { LogsModule } from 'src/logs/logs.module';
import { ProcessVtexOrderHandler } from './handlers/process-vtex-order.handler';
import { CqrsModule } from '@nestjs/cqrs';
import { VtexController } from './vtex.controller';
import { CustomersModule } from 'src/customers/customers.module';
import { OrdersModule } from 'src/orders/orders.module';
import { OrderProductsModule } from 'src/order-products/order-products.module';
import { CategoriesModule } from 'src/categories/categories.module';
import { UsersModule } from 'src/users/users.module';
import { ApiKeysModule } from 'src/api-keys/api-keys.module';
import { OrderProductCategoriesModule } from 'src/order-product-categories/order-product-categories.module';
import { OrderItemsModule } from 'src/order-items/order-items.module';
import { AutomationsModule } from 'src/automations/automations.module';
import { AbandonedCartsModule } from 'src/abandoned-carts/abandoned-carts.module';
import { PrismaModule } from 'src/prisma/prisma.module';
import { OrderStatusHistoryModule } from 'src/order-status-history/order-status-history.module';
import { VtexTempService } from './vtex.temp.service';

export const CommandHandlers = [ProcessVtexOrderHandler];

@Module({
  providers: [VtexService, VtexTempService, ...CommandHandlers],
  exports: [VtexService, VtexTempService],
  imports: [
    forwardRef(() => CustomersModule),
    forwardRef(() => CompaniesModule),
    LogsModule,
    CqrsModule,
    forwardRef(() => OrdersModule),
    OrderProductsModule,
    CategoriesModule,
    UsersModule,
    ApiKeysModule,
    OrderProductCategoriesModule,
    OrderItemsModule,
    forwardRef(() => AutomationsModule),
    AbandonedCartsModule,
    PrismaModule,
    forwardRef(() => OrderStatusHistoryModule),
  ],
  controllers: [VtexController],
})
export class VtexModule {}
