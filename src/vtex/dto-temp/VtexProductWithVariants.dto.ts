export interface VtexProductWithVariantsDto {
  productId: number;
  name: string;
  salesChannel: string;
  available: boolean;
  displayMode: string;
  dimensions: string[];
  dimensionsInputType: VtexProductDimensions;
  dimensionsMap: VtexProductDimensionsMap;
  skus: VtexProductSkus[];
}

export interface VtexProductDimensions {
  Color: string;
  Size: string;
  'Origin country': string;
  Gender: string;
}

export interface VtexProductDimensionsMap {
  Color: string[];
  Size: string[];
  'Origin country': string[];
  Gender: string[];
}

export interface VtexProductSkus {
  sku: number;
  skuname: string;
  dimensions: VtexProductDimensions;
  available: boolean;
  availablequantity: number;
  cacheVersionUsedToCallCheckout: null | string;
  listPriceFormated: string;
  listPrice: number;
  taxFormated: string;
  taxAsInt: number;
  bestPriceFormated: string;
  bestPrice: number;
  spotPrice: number;
  installments: number;
  installmentsValue: number;
  installmentsInsterestRate: number | null;
  image: string;
  sellerId: string;
  seller: string;
  measures: VtexProductMeasures;
  unitMultiplier: number;
  rewardValue: number;
}

export interface VtexProductMeasures {
  cubicweight: number;
  height: number;
  length: number;
  weight: number;
  width: number;
}
