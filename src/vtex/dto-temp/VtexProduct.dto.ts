export interface VtexProductDto {
  Id: number;
  Name: string;
  DepartmentId: number;
  CategoryId: number;
  BrandId: number;
  LinkId: string;
  RefId: string;
  IsVisible: boolean;
  Description: string;
  DescriptionShort: string;
  ReleaseDate: string;
  KeyWords: string;
  Title: string;
  IsActive: boolean;
  TaxCode: string;
  MetaTagDescription: string;
  SupplierId: number;
  ShowWithoutStock: boolean;
  AdWordsRemarketingCode: string | null;
  LomadeeCampaignCode: string | null;
  Score: number;
}

export interface VtexSkuDto {
  Id: number;
  ProductId: number;
  IsActive: boolean;
  Name: string;
  RefId: string;
  PackagedHeight: number;
  PackagedLength: number;
  PackagedWidth: number;
  PackagedWeightKg: number;
  Height: number | null;
  Length: number | null;
  Width: number | null;
  WeightKg: number | null;
  CubicWeight: number;
  IsKit: boolean;
  CreationDate: string;
  RewardValue: number | null;
  EstimatedDateArrival: string | null;
  ManufacturerCode: string | null;
  CommercialConditionId: number;
  MeasurementUnit: string;
  UnitMultiplier: number;
  ModalType: string | null;
  KitItensSellApart: boolean;
  Videos: any[];
  Images: VtexImageDto[];
  SkuSpecifications: VtexSkuSpecificationDto[];
  ProductSpecifications: VtexProductSpecificationDto[];
  ProductClustersIds: string;
  ProductCategoryIds: string;
  ProductGlobalCategoryId: string | null;
  ProductCategories: Record<string, string>;
  IsInventoryTracked: boolean;
  StockKeepingUnitId: number;
  NameComplete: string;
  ProductName: string;
  ProductDescription: string;
  SkuName: string;
  IsTransported: boolean;
  IsPerishable: boolean;
  IsGiftCardRecharge: boolean;
  ImageUrl: string;
  DetailUrl: string;
  CSCIdentification: string | null;
  BrandId: number;
  BrandName: string;
  Dimension: VtexDimensionDto;
  RealDimension: VtexDimensionDto;
  KitItems: VtexSkuKitItem[];
  KeyWords?: string;
  Services: any[];
  Categories: string[];
  CategoriesIds: string[];
  IsDirectCategoryActive: boolean;
  ActivateIfPossible: boolean;
  ShowIfNotAvailable: boolean;
  GiftCardRechargeOptions: any[];
  AvailableQuantity: number;
  InventoryLevels: VtexInventoryLevelDto[];
  // Campos de preço
  ListPrice?: number;
  Price?: number;
  bestPrice?: number;
  // Campos adicionais
  metadata?: any;
}

export interface VtexSkuKitItem {
  Id: number;
  Name: string;
  UnitPrice: number;
  Amount: number;
  EstimatedDateArrival: string | null;
  Dimension: VtexSkuKitItemDimension;
  RefId: string;
  EAN: string;
  IsKitOptimized: boolean;
}

export interface VtexSkuKitItemDimension {
  cubicweight: number;
  height: number;
  length: number;
  weight: number;
  width: number;
}

export interface VtexImageDto {
  ImageUrl: string;
  ImageName: string;
  FileId: number;
}

export interface VtexSkuSpecificationDto {
  FieldId: number;
  FieldName: string;
  FieldValueIds: number[];
  FieldValues: string[];
}

export interface VtexProductSpecificationDto {
  FieldId: number;
  FieldName: string;
  FieldValueIds: number[];
  FieldValues: string[];
}

export interface VtexDimensionDto {
  cubicweight: number;
  height: number;
  length: number;
  weight: number;
  width: number;
}

export interface VtexInventoryLevelDto {
  warehouseId: string;
  warehouseName: string;
  threshold: number;
  totalQuantity: number;
  reservedQuantity: number;
  hasUnlimitedQuantity: boolean;
}

export interface VtexProductAndSkuIdsResponse {
  data: Record<string, number[]>;
  range: {
    total: number;
    from: number;
    to: number;
  };
}
