export interface VtexOrderListByCustomerEmailResponseDto {
  list: VtexOrderByCustomerEmail[];
  paging: VtexOrderListResponsePaging;
  facets: any[];
  stats: VtexOrderListResponseDtoStats;
  reportRecordsLimit: number;
}

export interface VtexOrderByCustomerEmail {
  orderId: string;
  creationDate: Date;
  clientName: string;
  items: null;
  totalValue: number;
  paymentNames: string;
  status: string;
  statusDescription: string;
  marketPlaceOrderId: null;
  sequence: string;
  salesChannel: string;
  affiliateId: string;
  origin: string;
  workflowInErrorState: boolean;
  workflowInRetry: boolean;
  lastMessageUnread: null;
  ShippingEstimatedDate: null;
  ShippingEstimatedDateMax: null;
  ShippingEstimatedDateMin: null;
  orderIsComplete: boolean;
  listId: null;
  listType: null;
  authorizedDate: null;
  callCenterOperatorName: null;
  totalItems: number;
  currencyCode: string;
  hostname: string;
  invoiceOutput: null;
  invoiceInput: null;
  lastChange: Date;
  isAllDelivered: boolean;
  isAnyDelivered: boolean;
  giftCardProviders: null;
  orderFormId: string;
  paymentApprovedDate: null;
  readyForHandlingDate: null;
  deliveryDates: null;
  customFieldsValues: null;
  customFields: any[];
}

export interface VtexOrderListResponsePaging {
  total: number;
  pages: number;
  currentPage: number;
  perPage: number;
}

export interface VtexOrderListResponseDtoStats {
  stats: VtexOrderListResponseStatsStats;
}

export interface VtexOrderListResponseStatsStats {
  totalValue: VtexOrderListResponseTotal;
  totalItems: VtexOrderListResponseTotal;
}

export interface VtexOrderListResponseTotal {
  Count: number;
  Max: number;
  Mean: number;
  Min: number;
  Missing: number;
  StdDev: number;
  Sum: number;
  SumOfSquares: number;
  Facets: VtexOrderListResponseFacets;
}

export interface VtexOrderListResponseFacets {}
