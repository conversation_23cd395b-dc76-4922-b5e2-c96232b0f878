export interface VtexAbandonedCartDto {
  acompanharWhatsapp?: string;
  grupocliente?: string;
  profilePicture?: string;
  promocoesWhatsapp?: string;
  isCorporate?: boolean;
  tradeName?: string;
  rclastcart?: string;
  rclastcartvalue?: number;
  rclastsession?: string;
  rclastsessiondate?: Date;
  homePhone?: string;
  phone?: string;
  brandPurchasedTag?: Tag;
  brandVisitedTag?: Tag;
  categoryPurchasedTag?: Tag;
  categoryVisitedTag?: Tag;
  departmentVisitedTag?: Tag;
  productPurchasedTag?: Tag;
  productVisitedTag?: Tag;
  stateRegistration?: string;
  email: string;
  userId: string;
  firstName?: string;
  lastName?: string;
  document?: string;
  isNewsletterOptIn: boolean;
  localeDefault?: string;
  attach?: string;
  approved?: string;
  birthDate?: Date;
  businessPhone?: string;
  carttag?: Tag;
  checkouttag?: Tag;
  corporateDocument?: string;
  corporateName?: string;
  documentType?: string;
  gender?: string;
  visitedProductWithStockOutSkusTag?: Tag;
  customerClass?: string;
  priceTables?: string;
  birthDateMonth?: number;
  id: string;
  accountId: string;
  accountName: string;
  dataEntityId: string;
  createdBy: string;
  createdIn: Date;
  updatedBy?: string;
  updatedIn?: Date;
  lastInteractionBy: string;
  lastInteractionIn: Date;
  followers: any[];
  tags: any[];
  auto_filter?: string;
}

export interface Tag {
  DisplayValue?: string;
  Scores: Record<string, any>;
}
