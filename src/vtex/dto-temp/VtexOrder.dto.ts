export interface VtexOrderDto {
  orderId: string;
  sequence: string;
  marketplaceOrderId: string;
  marketplaceServicesEndpoint: string;
  sellerOrderId: string;
  origin: string;
  affiliateId: string;
  salesChannel: string;
  merchantName: string | null;
  status: string;
  workflowIsInError: boolean;
  statusDescription: string;
  value: number;
  creationDate: Date;
  lastChange: Date;
  orderGroup: string;
  followUpEmail: string;
  lastMessage: string | null;
  hostname: string;
  isCompleted: boolean;
  roundingError: number;
  orderFormId: string;
  allowCancellation: boolean;
  allowEdition: boolean;
  isCheckedIn: boolean;
  authorizedDate: string | null;
  invoicedDate: string | null;
  cancelReason: string | null;
  checkedInPickupPointId: string | null;
  totals: VtexOrderTotal[];
  sellers: VtexOrderSeller[];
  clientPreferencesData: VtexOrderClientPreferencesData;
  cancellationData: string | null;
  taxData: string | null;
  subscriptionData: string | null;
  itemMetadata: VtexOrderItemMetadata;
  marketplace: VtexOrderMarketplace;
  storePreferencesData: VtexOrderStorePreferencesData;
  customData: string | null;
  commercialConditionData: string | null;
  openTextField: string | null;
  invoiceData: string | null;
  changesAttachment: string | null;
  callCenterOperatorData: string | null;
  packageAttachment: VtexOrderPackageAttachment;
  paymentData: VtexOrderPaymentData;
  shippingData: VtexOrderShippingData;
  ratesAndBenefitsData: VtexOrderRatesAndBenefitsData;
  marketingData: VtexOrderMarketingData;
  giftRegistryData: string | null;
  clientProfileData: VtexOrderClientProfileData;
  items: VtexOrderItemElement[];
  marketplaceItems: any[];
  cancellationRequests: string | null;
  approvedBy: string | null;
  cancelledBy: string | null;
  purchaseAgentData: string | null;
  pendingData: string | null;
  creationEnvironment: string;
}

export interface VtexOrderClientPreferencesData {
  locale: string;
  optinNewsLetter: boolean;
}

export interface VtexOrderClientProfileData {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  documentType: string;
  document: string;
  phone: string;
  corporateName: string | null;
  tradeName: string | null;
  corporateDocument: string | null;
  stateInscription: string | null;
  corporatePhone: string | null;
  isCorporate: boolean;
  userProfileId: string;
  userProfileVersion: string | null;
  customerClass: string | null;
  customerCode: string | null;
}

export interface VtexOrderItemMetadata {
  Items: VtexOrderItem[];
}

export interface VtexOrderItem {
  Id: string;
  Seller: string;
  Name: string;
  SkuName: string;
  ProductId: string;
  RefId: string;
  Ean: string;
  ImageUrl: string;
  DetailUrl: string;
  AssemblyOptions: any[];
}

export interface VtexOrderItemElement {
  uniqueId: string;
  id: string;
  productId: null | string;
  ean: string;
  lockId: null | string;
  itemAttachment: VtexOrderItemAttachment;
  attachments: any[];
  quantity: number;
  seller: null | string;
  name: string;
  refId: string;
  price: number;
  listPrice: number | null;
  manualPrice: string | null;
  manualPriceAppliedBy: string | null;
  priceTags: VtexOrderPriceTag[];
  imageUrl: null | string;
  detailUrl: null | string;
  components: VtexOrderItemElement[];
  bundleItems: any[];
  params: any[];
  offerings: any[];
  attachmentOfferings: any[];
  sellerSku: string;
  priceValidUntil: Date | null;
  commission: number;
  tax: number;
  preSaleDate: string | null;
  additionalInfo: VtexOrderAdditionalInfo;
  measurementUnit: string;
  unitMultiplier: number;
  sellingPrice: number;
  isGift: boolean;
  shippingPrice: string | null;
  rewardValue: number;
  freightCommission: number;
  priceDefinition: VtexOrderPriceDefinition;
  taxCode: null | string;
  parentItemIndex: string | null;
  parentAssemblyBinding: string | null;
  callCenterOperator: string | null;
  serialNumbers: string | null;
  assemblies: any[];
  costPrice: number | null;
}

export interface VtexOrderAdditionalInfo {
  brandName: null | string;
  brandId: null | string;
  categoriesIds: null | string;
  categories: VtexOrderCategory[] | null;
  productClusterId: null | string;
  commercialConditionId: null | string;
  dimension: VtexOrderDimension;
  offeringInfo: string | null;
  offeringType: string | null;
  offeringTypeId: string | null;
}

export interface VtexOrderCategory {
  id: number;
  name: string;
}

export interface VtexOrderDimension {
  cubicweight: number;
  height: number;
  length: number;
  weight: number;
  width: number;
}

export interface VtexOrderItemAttachment {
  content: any;
  name: string | null;
}

export interface VtexOrderPriceDefinition {
  sellingPrices: VtexOrderSellingPrice[];
  calculatedSellingPrice: number;
  total: number;
  reason: string | null;
}

export interface VtexOrderSellingPrice {
  value: number;
  quantity: number;
}

export interface VtexOrderPriceTag {
  name: string;
  value: number;
  isPercentual: boolean;
  identifier: string;
  rawValue: number;
  rate: string | null;
  jurisCode: string | null;
  jurisType: string | null;
  jurisName: string | null;
}

export interface VtexOrderMarketingData {
  id: string;
  utmSource: string | null;
  utmPartner: string | null;
  utmMedium: string | null;
  utmCampaign: string | null;
  coupon: string | null;
  utmiCampaign: string | null;
  utmipage: string | null;
  utmiPart: string | null;
  marketingTags: string[];
}

export interface VtexOrderMarketplace {
  baseURL: string;
  isCertified: string | null;
  name: string;
}

export interface VtexOrderPackageAttachment {
  packages: any[];
}

export interface VtexOrderPaymentData {
  transactions: VtexOrderTransaction[];
  giftCards: any[];
}

export interface VtexOrderTransaction {
  isActive: boolean;
  transactionId: string;
  merchantName: string;
  payments: VtexOrderPayment[];
}

export interface VtexOrderPayment {
  id: string;
  paymentSystem: string;
  paymentSystemName: string;
  value: number;
  installments: number;
  referenceValue: number;
  cardHolder: string | null;
  cardNumber: string | null;
  firstDigits: string | null;
  lastDigits: string | null;
  cvv2: string | null;
  expireMonth: string | null;
  expireYear: string | null;
  url: string;
  giftCardId: string | null;
  giftCardName: string | null;
  giftCardCaption: string | null;
  redemptionCode: string | null;
  group: string;
  tid: string;
  dueDate: string | null;
  connectorResponses: VtexOrderConnectorResponses;
  giftCardProvider: string | null;
  giftCardAsDiscount: string | null;
  koinUrl: string | null;
  accountId: string | null;
  parentAccountId: string | null;
  bankIssuedInvoiceIdentificationNumber: string;
  bankIssuedInvoiceIdentificationNumberFormatted: string;
  bankIssuedInvoiceBarCodeNumber: string | null;
  bankIssuedInvoiceBarCodeType: string;
  billingAddress: string | null;
  paymentOrigin: string | null;
}

export interface VtexOrderConnectorResponses {
  ReturnCode: string;
  Message: string | null;
  acquirer: string;
  Tid: string;
}

export interface VtexOrderRatesAndBenefitsData {
  id: string;
  rateAndBenefitsIdentifiers: VtexOrderRateAndBenefitsIdentifier[];
}

export interface VtexOrderRateAndBenefitsIdentifier {
  description: string;
  featured: boolean;
  id: string;
  name: string;
  matchedParameters: VtexOrderMatchedParameters;
  additionalInfo: string | null;
}

export interface VtexOrderMatchedParameters {
  'productCluster@CatalogSystem': string;
}

export interface VtexOrderSeller {
  id: string;
  name: string;
  logo: string;
  fulfillmentEndpoint: string;
}

export interface VtexOrderShippingData {
  id: string;
  address: VtexOrderAddress;
  logisticsInfo: VtexOrderLogisticsInfo[];
  trackingHints: string | null;
  selectedAddresses: VtexOrderAddress[];
  availableAddresses: VtexOrderAddress[];
  contactInformation: any[];
}

export interface VtexOrderAddress {
  addressType: string;
  receiverName: string;
  addressId: string;
  versionId: string | null;
  entityId: string | null;
  postalCode: string;
  city: string;
  state: string;
  country: string;
  street: string;
  number: string;
  neighborhood: string;
  complement: string | null;
  reference: string | null;
  geoCoordinates: number[];
}

export interface VtexOrderLogisticsInfo {
  itemIndex: number;
  itemId: string;
  selectedSla: string;
  selectedDeliveryChannel: string;
  lockTTL: string;
  price: number;
  listPrice: number;
  sellingPrice: number;
  deliveryWindow: string | null;
  deliveryCompany: string;
  shippingEstimate: string;
  shippingEstimateDate: string | null;
  slas: VtexOrderSla[];
  shipsTo: string[];
  deliveryIds: VtexOrderDeliveryID[];
  deliveryChannels: VtexOrderDeliveryChannel[];
  deliveryChannel: string;
  pickupStoreInfo: VtexOrderPickupStoreInfo;
  addressId: string;
  versionId: string | null;
  entityId: string | null;
  polygonName: string;
  pickupPointId: string | null;
  transitTime: string;
}

export interface VtexOrderDeliveryChannel {
  id: string;
  stockBalance: number;
}

export interface VtexOrderDeliveryID {
  courierId: string;
  courierName: string;
  dockId: string;
  quantity: number;
  warehouseId: string;
  accountCarrierName: string;
  kitItemDetails: VtexOrderKitItemDetail[];
}

export interface VtexOrderKitItemDetail {
  ItemId: string;
  WarehouseId: string;
}

export interface VtexOrderPickupStoreInfo {
  additionalInfo: string | null;
  address: string | null;
  dockId: string | null;
  friendlyName: string | null;
  isPickupStore: boolean;
}

export interface VtexOrderSla {
  id: string;
  name: string;
  shippingEstimate: string;
  deliveryWindow: string | null;
  availableDeliveryWindows: any[];
  price: number;
  listPrice: number;
  deliveryChannel: string;
  pickupStoreInfo: VtexOrderPickupStoreInfo;
  polygonName: string;
  lockTTL: string;
  pickupPointId: string | null;
  transitTime: string;
  pickupDistance: number;
  deliveryIds: VtexOrderDeliveryID[];
  shippingEstimateDate: string | null;
}

export interface VtexOrderStorePreferencesData {
  countryCode: string;
  currencyCode: string;
  currencyFormatInfo: VtexOrderCurrencyFormatInfo;
  currencyLocale: number;
  currencySymbol: string;
  timeZone: string;
}

export interface VtexOrderCurrencyFormatInfo {
  CurrencyDecimalDigits: number;
  CurrencyDecimalSeparator: string;
  CurrencyGroupSeparator: string;
  CurrencyGroupSize: number;
  StartsWithCurrencySymbol: boolean;
}

export interface VtexOrderTotal {
  id: string;
  name: string;
  value: number;
}
