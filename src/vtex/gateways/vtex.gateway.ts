import VtexCartCreateItemsDto from '../dto-temp/VtexCartCreateItems.dto';
import {
  VtexOrderByCustomerEmail,
  VtexOrderListByCustomerEmailResponseDto,
} from '../dto-temp/VtexOrderListByCustomerEmailResponse.dto';
import { VtexOrderDto } from '../dto-temp/VtexOrder.dto';
import VtexCartCustomer from '../types-temp/VtexCartCustomer';
import DefaultFilterParams from '../types-temp/DefaultFilterParams';
import HttpRequestParams, {
  HttpRequestMethod,
} from '../types-temp/http-request-params.interface';
import { HttpRequestStep } from '../utils-temp/http-request.step';
export class VtexGateway {
  private vtexStoreDomain: string;
  private vtexAccountName: string;
  private vtexAppKey: string;
  private vtexAppToken: string;

  constructor(
    vtexStoreDomain: string,
    vtexAccountName: string,
    vtexAppKey: string,
    vtexAppToken: string,
  ) {
    this.vtexStoreDomain = vtexStoreDomain;
    this.vtexAccountName = vtexAccountName;
    this.vtexAppKey = vtexAppKey;
    this.vtexAppToken = vtexAppToken;
  }

  private createHttpRequestParams({
    url,
    method,
    headers,
    data,
  }: Omit<HttpRequestParams, 'method'> & {
    method?: HttpRequestMethod;
  }): HttpRequestParams {
    return {
      method: method ?? 'GET',
      headers: {
        'X-VTEX-API-AppKey': this.vtexAppKey,
        'X-VTEX-API-AppToken': this.vtexAppToken,
        Accept: 'application/json',
        ...(headers ?? {}),
      },
      url,
      data,
    } as HttpRequestParams;
  }

  async createEmptyCart(retry: boolean = false): Promise<any | null> {
    if (retry) {
      await new Promise((resolve) => setTimeout(resolve, 30000));
    }

    const httpRequestParams = this.createHttpRequestParams({
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      url: `https://${this.vtexAccountName}.vtexcommercestable.com.br/api/checkout/pub/orderForm?forceNewCart=true`,
    });

    const requisition = new HttpRequestStep(httpRequestParams);
    const response = await requisition.execute();
    if (![200, 201, 204, 403].includes(response.code) && !retry) {
      return this.createEmptyCart(true);
    }

    if ([200, 201, 204].includes(response.code) && response.data) {
      return response.data;
    }

    return null;
  }
  async addItemToCart(
    orderFormId: string,
    orderItems: VtexCartCreateItemsDto,
    retry: boolean = false,
  ): Promise<any | null> {
    if (retry) {
      await new Promise((resolve) => setTimeout(resolve, 30000));
    }

    const httpRequestParams = this.createHttpRequestParams({
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      url: `https://${this.vtexAccountName}.vtexcommercestable.com.br/api/checkout/pub/orderForm/${orderFormId}/items`,
      data: orderItems,
    });

    const requisition = new HttpRequestStep(httpRequestParams);
    const response = await requisition.execute();
    if (![200, 201, 204, 403].includes(response.code) && !retry) {
      return this.addItemToCart(orderFormId, orderItems, true);
    }

    if ([200, 201, 204].includes(response.code) && response.data) {
      return response.data;
    }

    return null;
  }

  async addCustomerToCart(
    orderFormId: string,
    customer: VtexCartCustomer,
    retry: boolean = false,
  ): Promise<any | null> {
    if (retry) {
      await new Promise((resolve) => setTimeout(resolve, 30000));
    }

    const httpRequestParams = this.createHttpRequestParams({
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      url: `https://${this.vtexAccountName}.vtexcommercestable.com.br/api/checkout/pub/orderForm/${orderFormId}/attachments/clientProfileData`,
      data: customer,
    });

    const requisition = new HttpRequestStep(httpRequestParams);
    const response = await requisition.execute();
    if (![200, 201, 204, 403].includes(response.code) && !retry) {
      return this.addCustomerToCart(orderFormId, customer, true);
    }

    if ([200, 201, 204].includes(response.code) && response.data) {
      return response.data;
    }

    return null;
  }

  async fetchOrder({
    orderId,
    retry,
  }: {
    orderId: string;
    retry?: boolean;
  }): Promise<VtexOrderDto> {
    if (retry) {
      await new Promise((resolve) => setTimeout(resolve, 30000));
    }

    const httpRequestParams = this.createHttpRequestParams({
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      url: `https://${this.vtexAccountName}.vtexcommercestable.com.br/api/oms/pvt/orders/${orderId}`,
    });

    const requisition = new HttpRequestStep(httpRequestParams);
    const response = await requisition.execute();
    if (![200, 201, 204, 403].includes(response.code) && !retry) {
      return this.fetchOrder({ orderId: orderId, retry: true });
    }

    return response.data as VtexOrderDto;
  }
  private async fetchOrdersByCustomerEmailPage(
    params: DefaultFilterParams & { customerEmail: string; retry?: boolean },
  ): Promise<VtexOrderListByCustomerEmailResponseDto> {
    if (params.retry) {
      await new Promise((resolve) => setTimeout(resolve, 30000));
    }

    const httpRequestParams = this.createHttpRequestParams({
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      url: `https://${this.vtexAccountName}.vtexcommercestable.com.br/api/oms/pvt/orders?clientEmail=${params.customerEmail}&page=${params.page}&per_page=${params.limit}`,
    });

    const requisition = new HttpRequestStep(httpRequestParams);
    const response = await requisition.execute();
    if (![200, 201, 204, 403].includes(response.code) && !params.retry) {
      return this.fetchOrdersByCustomerEmailPage({ ...params, retry: true });
    }
    return response.data as VtexOrderListByCustomerEmailResponseDto;
  }

  async listOrdersByCustomerEmail(
    customerEmail: string,
  ): Promise<VtexOrderByCustomerEmail[]> {
    const params: DefaultFilterParams & { customerEmail: string } = {
      page: 1,
      limit: 25,
      customerEmail,
    };
    const response: VtexOrderByCustomerEmail[] = [];
    while (true) {
      const currentPage = await this.fetchOrdersByCustomerEmailPage(params);
      if (currentPage.list.length == 0) {
        break;
      }
      response.push(...currentPage.list);
      params.page = params.page! + 1;
    }
    return response;
  }
}
