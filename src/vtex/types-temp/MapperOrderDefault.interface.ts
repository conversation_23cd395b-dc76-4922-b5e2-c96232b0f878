import { MappingFunction } from '../utils-temp/mapper.utils';

type MapperOrderDefaultValue =
  | string
  | string[]
  | { path: string | string[]; transform?: MappingFunction };

export interface MapperCustomer {
  integrationConfigId?: MapperOrderDefaultValue;
  phoneNumberId: MapperOrderDefaultValue;
  name: MapperOrderDefaultValue;
  email?: MapperOrderDefaultValue;
  isOptedIn?: MapperOrderDefaultValue;
  companyId: MapperOrderDefaultValue;
  tags?: MapperOrderDefaultValue;
  isOptedOut?: MapperOrderDefaultValue;
  customFields?: MapperOrderDefaultValue;
  sourceCustomerId?: MapperOrderDefaultValue;
  source: MapperOrderDefaultValue;
  sourceCreatedAt?: MapperOrderDefaultValue;
  sourceUpdatedAt?: MapperOrderDefaultValue;
  birthDate?: MapperOrderDefaultValue;
  sourceId: MapperOrderDefaultValue;
  sourceUserId?: MapperOrderDefaultValue;
  isOptedInToNewsletter?: MapperOrderDefaultValue;
  city?: MapperOrderDefaultValue;
  state?: MapperOrderDefaultValue;
  country?: MapperOrderDefaultValue;
  cpf?: MapperOrderDefaultValue;
  isDeleted?: MapperOrderDefaultValue;
  notes?: MapperOrderDefaultValue;
}
export interface MapperOrder {
  sourceType?: MapperOrderDefaultValue;
  integrationConfigId?: MapperOrderDefaultValue;
  sourceId: MapperOrderDefaultValue;
  source: MapperOrderDefaultValue;
  sourceCreatedAt: MapperOrderDefaultValue;
  sourceUpdatedAt?: MapperOrderDefaultValue;
  platformOrderId?: MapperOrderDefaultValue;
  platformOrderSource?: MapperOrderDefaultValue;
  value: MapperOrderDefaultValue;
  totalItemsValue?: MapperOrderDefaultValue;
  totalItemsQuantity?: MapperOrderDefaultValue;
  status?: MapperOrderDefaultValue;
  companyId: MapperOrderDefaultValue;
  customerId: MapperOrderDefaultValue;
  coupon?: MapperOrderDefaultValue;
  totalDiscountsValue?: MapperOrderDefaultValue;
  totalShippingValue?: MapperOrderDefaultValue;
  totalTaxValue?: MapperOrderDefaultValue;
  shippingCarrier?: MapperOrderDefaultValue;
  trackingCode?: MapperOrderDefaultValue;
  trackingUrl?: MapperOrderDefaultValue;
  isTrackingCodeSent?: MapperOrderDefaultValue;
  salesChannel?: MapperOrderDefaultValue;
  storeName?: MapperOrderDefaultValue;
  shipmentStatus?: MapperOrderDefaultValue;
  userAgent?: MapperOrderDefaultValue;
  note?: MapperOrderDefaultValue;
  noteAtributes?: MapperOrderDefaultValue;
  currency?: MapperOrderDefaultValue;
  statusUrl?: MapperOrderDefaultValue;
  marketingData?: MapperOrderDefaultValue;
  cancelReason?: MapperOrderDefaultValue;
  cancelledAt?: MapperOrderDefaultValue;
  isSubscription?: MapperOrderDefaultValue;
  paymentMethods?: MapperOrderDefaultValue;
}

export interface MapperOrderCustomer {
  'customer.phoneNumberId': MapperOrderDefaultValue;
  'customer.name': MapperOrderDefaultValue;
  'customer.email'?: MapperOrderDefaultValue;
  'customer.isOptedIn'?: MapperOrderDefaultValue;
  'customer.companyId': MapperOrderDefaultValue;
  'customer.tags'?: MapperOrderDefaultValue;
  'customer.isOptedOut'?: MapperOrderDefaultValue;
  'customer.customFields'?: MapperOrderDefaultValue;
  'customer.sourceCustomerId'?: MapperOrderDefaultValue;
  'customer.source': MapperOrderDefaultValue;
  'customer.sourceCreatedAt': MapperOrderDefaultValue;
  'customer.sourceUpdatedAt'?: MapperOrderDefaultValue;
  'customer.birthDate'?: MapperOrderDefaultValue;
  'customer.sourceId': MapperOrderDefaultValue;
  'customer.sourceUserId'?: MapperOrderDefaultValue;
  'customer.isOptedInToNewsletter'?: MapperOrderDefaultValue;
  'customer.city'?: MapperOrderDefaultValue;
  'customer.state'?: MapperOrderDefaultValue;
  'customer.country'?: MapperOrderDefaultValue;
  'customer.cpf'?: MapperOrderDefaultValue;
  'customer.isDeleted'?: MapperOrderDefaultValue;
  'customer.notes'?: MapperOrderDefaultValue;
}

export interface MapperOrderDefaultMapperOrder {
  'order.sourceType'?: MapperOrderDefaultValue;
  'order.sourceId': MapperOrderDefaultValue;
  'order.source': MapperOrderDefaultValue;
  'order.sourceCreatedAt': MapperOrderDefaultValue;
  'order.sourceUpdatedAt'?: MapperOrderDefaultValue;
  'order.platformOrderId'?: MapperOrderDefaultValue;
  'order.platformOrderSource'?: MapperOrderDefaultValue;
  'order.value': MapperOrderDefaultValue;
  'order.totalItemsValue'?: MapperOrderDefaultValue;
  'order.totalItemsQuantity'?: MapperOrderDefaultValue;
  'order.status'?: MapperOrderDefaultValue;
  'order.companyId': MapperOrderDefaultValue;
  'order.customerId': MapperOrderDefaultValue;
  'order.coupon'?: MapperOrderDefaultValue;
  'order.totalDiscountsValue'?: MapperOrderDefaultValue;
  'order.totalShippingValue'?: MapperOrderDefaultValue;
  'order.totalTaxValue'?: MapperOrderDefaultValue;
  'order.shippingCarrier'?: MapperOrderDefaultValue;
  'order.trackingCode'?: MapperOrderDefaultValue;
  'order.trackingUrl'?: MapperOrderDefaultValue;
  'order.isTrackingCodeSent'?: MapperOrderDefaultValue;
  'order.salesChannel'?: MapperOrderDefaultValue;
  'order.storeName'?: MapperOrderDefaultValue;
  'order.userAgent'?: MapperOrderDefaultValue;
  'order.note'?: MapperOrderDefaultValue;
  'order.noteAtributes'?: MapperOrderDefaultValue;
  'order.currency'?: MapperOrderDefaultValue;
  'order.statusUrl'?: MapperOrderDefaultValue;
  'order.marketingData'?: MapperOrderDefaultValue;
}
export interface MapperOrderItem {
  quantity?: MapperOrderDefaultValue;
  productId: MapperOrderDefaultValue;
  orderId: MapperOrderDefaultValue;
}

export interface MapperOrderProduct {
  sourceId?: MapperOrderDefaultValue;
  name: MapperOrderDefaultValue;
  brand?: MapperOrderDefaultValue;
  companyId: MapperOrderDefaultValue;
  sku?: MapperOrderDefaultValue;
}

type MapperOrderDefault = MapperOrderCustomer &
  MapperOrderDefaultMapperOrder & {
    orderItems: MapperOrderDefaultValue;
    product: MapperOrderDefaultValue;
  };

export default MapperOrderDefault;
