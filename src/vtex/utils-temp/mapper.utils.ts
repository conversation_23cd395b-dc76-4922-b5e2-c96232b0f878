export type MappingFunction = (value: any) => any;

export interface MapperPattern {
  [key: string]:
    | string
    | string[]
    | { path: string | string[]; transform?: MappingFunction };
}

export class Mapper {
  private pattern: MapperPattern;

  constructor(pattern: MapperPattern) {
    this.pattern = pattern;
  }

  map<T = any>(source: any): T {
    if (Array.isArray(source)) {
      return source.map((item) => this.mapItem(item)) as any;
    }
    return this.mapItem(source);
  }

  private mapItem<T = any>(source: any): T {
    const result: any = {};

    for (const [destinationPath, config] of Object.entries(this.pattern)) {
      const { path, transform } = this.resolveConfig(config);

      const value = this.getValueFromPath(source, path);

      const transformedValue = transform ? transform(value) : value;

      this.setValueInResult(result, destinationPath, transformedValue);
    }

    return result;
  }

  private resolveConfig(config: any): {
    path: string | string[];
    transform?: MappingFunction;
  } {
    if (typeof config === 'string' || Array.isArray(config)) {
      return { path: config };
    }
    return { path: config.path, transform: config.transform };
  }

  private getValueFromPath(source: any, path: string | string[]): any {
    const paths = Array.isArray(path) ? path : [path];

    const values = paths.map((p) =>
      p.split('.').reduce((acc, part) => acc && acc[part], source),
    );
    return values.length === 1 ? values[0] : values;
  }

  private setValueInResult(
    result: any,
    destinationPath: string,
    value: any,
  ): void {
    const keys = destinationPath.split('.');
    let current = result;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!current[key]) {
        current[key] = {};
      }
      current = current[key];
    }
    current[keys[keys.length - 1]] = value;
  }
}
