import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { IntegrationStep } from '../types-temp/integration-step.interface';
import HttpRequestParams from '../types-temp/http-request-params.interface';
import HttpStepResponse from '../types-temp/http-step-response.interface';

export class HttpRequestStep implements IntegrationStep {
  private params: HttpRequestParams;
  private client: any;

  constructor(params: HttpRequestParams) {
    this.params = params;
  }

  private buildConfig(): AxiosRequestConfig {
    const config: AxiosRequestConfig = {
      headers: this.params.headers,
      params: this.params.query,
    };

    if (this.params.auth && this.params.auth.type !== 'ntlm') {
      config.auth = {
        username: this.params.auth.username,
        password: this.params.auth.password,
      };
    }

    return config;
  }

  async execute(): Promise<HttpStepResponse> {
    const config = this.buildConfig();
    let response: AxiosResponse;

    try {
      const httpClient = this.client || axios;

      switch (this.params.method) {
        case 'GET':
          response = await httpClient.get(this.params.url, config);
          break;
        case 'POST':
          response = await httpClient.post(
            this.params.url,
            this.params.data,
            config,
          );
          break;
        case 'PUT':
          response = await httpClient.put(
            this.params.url,
            this.params.data,
            config,
          );
          break;
        case 'DELETE':
          response = await httpClient.delete(this.params.url, config);
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${this.params.method}`);
      }

      return {
        code: response.status,
        data: response.data,
        headers: response.headers,
        errorMessage: undefined,
      };
    } catch (error: unknown) {
      if (axios.isAxiosError(error)) {
        return {
          code: error.response?.status || 500,
          headers: {},
          data: error.response?.data || null,
          errorMessage: error.response?.data?.message || error.message,
        };
      } else if (error instanceof Error) {
        return {
          code: 500,
          data: null,
          headers: {},
          errorMessage: error.message,
        };
      } else {
        return {
          code: 500,
          headers: {},
          data: null,
          errorMessage: 'An unknown error occurred',
        };
      }
    }
  }
}
