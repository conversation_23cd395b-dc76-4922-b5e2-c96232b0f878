import { LogsService } from 'src/logs/logs.service';
import {
  Body,
  Controller,
  Post,
  UseGuards,
  Request,
  Get,
  Query,
} from '@nestjs/common';
import { VtexService } from './vtex.service';
import { ProcessOrderDto } from './dto/ProcessOrder.dto';
import { ApiKeyGuard } from 'src/auth/api-key-auth.guard';
import { VtexAbandonedCartDto } from './dto/AbandonedCart.dto';
import { JwtPayload } from 'jsonwebtoken';
import { Headers } from 'openai/_shims';
import { VtexTempService } from './vtex.temp.service';
import { ReviIntegratorAuthGuard } from 'src/auth/revi-integrator-auth.guard';

@Controller('vtex')
export class VtexController {
  constructor(
    private readonly vtexService: VtexService,
    private readonly logsService: LogsService,
    private readonly vtexTempService: VtexTempService,
  ) {}

  @UseGuards(ApiKeyGuard)
  @Post('process-order')
  async proccessVtexOrder(@Body() body: ProcessOrderDto): Promise<any> {
    try {
      const {
        OrderId,
        Origin: { Key, Account },
        State,
      } = body;
      return await this.vtexService.proccessVtexOrder({
        orderId: OrderId,
        accountName: Account,
        appKey: Key,
        status: State,
      });
    } catch (err: any) {
      await this.logsService.createSentryLog(
        {
          message: err.message,
          severity: 'error',
          source: 'vtex',
          meta: {
            origin: 'VtexController.proccessVtexOrder',
            payload: JSON.stringify(body),
          } as any,
        },
        err,
      );
    }
  }

  @UseGuards(ApiKeyGuard)
  @Post('process-abandoned-cart')
  async proccessAbandonedVtexCartOrder(
    @Body() body: VtexAbandonedCartDto,
    @Request() request: { user: JwtPayload },
  ): Promise<any> {
    try {
      return await this.vtexService.proccessAbandonedVtexCartOrder({
        companyId: request.user.companyId,
        data: body,
      });
    } catch (err: any) {
      await this.logsService.createSentryLog(
        {
          message: err.message,
          severity: 'error',
          source: 'vtex',
          meta: {
            origin: 'VtexController.proccessAbandonedVtexCartOrder',
            payload: JSON.stringify(body),
          } as any,
        },
        err,
      );
    }
  }

  @UseGuards(ReviIntegratorAuthGuard)
  @Post('create-cart')
  async createCart(@Body() data: { companyId: string; customerId: string }) {
    return await this.vtexTempService.createCart(data);
  }

  @UseGuards(ReviIntegratorAuthGuard)
  @Get('last-order-by-customer-email')
  async fetchLastOrderByCustomerEmail(
    @Headers() headers: any,
    @Query('customerEmail') customerEmail?: string,
  ) {
    const companyId = headers['company-id'] ?? '';
    return await this.vtexTempService.fetchLastOrderByCustomerEmail({
      customerEmail: customerEmail ?? '',
      companyId,
    });
  }
}
