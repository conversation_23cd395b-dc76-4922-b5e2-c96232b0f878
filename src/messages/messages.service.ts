import {
  BadRequestException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  MediaType,
  Message,
  MessageStatus,
  Prisma,
  WebPushType,
} from '@prisma/client';
import { SqsConsumerEventHandler, SqsMessageHandler } from '@ssut/nestjs-sqs';
import async from 'async';
import { CompaniesService } from 'src/companies/companies.service';
import { CompanyWithIncludes } from 'src/companies/types/CompanyWithIncludes';
import { ConversationTicketsService } from 'src/conversation-tickets/conversation-tickets.service';
import { ConversationsService } from 'src/conversations/conversations.service';
import { CustomersService } from 'src/customers/customers.service';
import { LogsService } from 'src/logs/logs.service';
import { MessageTemplatesService } from 'src/message-templates/message-templates.service';
import { PrismaService } from 'src/prisma/prisma.service';
import { SqsQueueEnum } from 'src/shared/types/SqsQueueEnum';
import { TemplateParametersEnum } from 'src/shared/types/TemplateParametersEnum';
import { WhatsappSessionsService } from 'src/whatsapp-sessions/whatsapp-sessions.service';
import { NameUtils } from '../shared/utils/name.utils';
import { WhatsappCampaignsService } from './../whatsapp-campaigns/whatsapp-campaigns.service';
import { ReceiveMessageCommand } from './commands/receive-message.command';
import { SendMessageTemplateCommand } from './commands/send-message-template.command';
import { SendMessageCommand } from './commands/send-message.command';
import { SendWhatsappCampaignMessagesCommand } from './commands/send-whatsapp-campaign-messages.command';
import { ReceiveMessageDto } from './dto/receive-message.dto';
import { SendMessageTemplateByPhoneDto } from './dto/send-message-template-by-phone.dto';
import { SendMessageTemplateDto } from './dto/send-message-template.dto';
import { SendMessageDto } from './dto/send-message.dto';
import { MessageEventsEnum } from './types/MessageEventsEnum';
import { NewMessageEventPayload } from './types/NewMessageEventPayload';
import { QueueBodyPayload } from './types/QueueBodyPayload';
import { QueueMessagePayload } from './types/QueueMessagePayload';
import { UpdateMessageStatusEventPayload } from './types/UpdateMessageStatusEventPayload';
import { MessageTemplateUtils } from 'src/shared/utils/message-template.utils';
import { ConversationWithIncludes } from 'src/conversations/types/ConversationWithIncludes';
import { MessageWithCardIncluded } from './types/Message';
import { FilesService } from 'src/files/files.service';
import _ from 'lodash';
import { AutomationsService } from 'src/automations/automations.service';
import { FlowNodesService } from 'src/flow-nodes/flow-nodes.service';
import { SessionMessageButton } from './strategies/send-message/SendSessionMessage.strategy';
import { JwtPayload } from 'src/auth/types/JwtPayload';
import { Server } from 'socket.io';
import { SocketsService } from 'src/sockets/sockets.service';
import { WebPushService } from 'src/web-push/web-push.service';
import { WebPushThrottleService } from 'src/web-push/web-push-throttle.service';
import { WebPushNotificationPayloadFactory } from 'src/web-push/factories/WebPushNotificationPayload.factory';
import { SessionCatalogData } from './types/SessionCatalogData';
import { ProductsService } from 'src/products/products.service';
import {
  CatalogType,
  ProductSectionDto,
  SendProductCatalogDto,
} from './dto/send-product-catalog.dto';

const includes: Prisma.MessageInclude = {
  messageCards: {
    include: {
      messageTemplateCard: {
        select: {
          id: true,
          mediaUrl: true,
          buttons: true,
          headerType: true,
          messageTemplateId: true,
        },
      },
    },
  },
  context: true,
};

@Injectable()
export class MessagesService {
  constructor(
    private readonly prismaService: PrismaService,
    @Inject(forwardRef(() => ConversationsService))
    private readonly conversationsService: ConversationsService,
    @Inject(forwardRef(() => CompaniesService))
    private readonly companiesService: CompaniesService,
    private readonly conversationTicketService: ConversationTicketsService,
    private readonly whatsappSessionsService: WhatsappSessionsService,
    @Inject(forwardRef(() => CustomersService))
    private readonly customersService: CustomersService,
    private eventEmitter: EventEmitter2,
    private readonly whatsappCampaignsService: WhatsappCampaignsService,
    private readonly messageTemplatesService: MessageTemplatesService,
    private readonly commandBus: CommandBus,
    private readonly logsService: LogsService,
    private readonly filesService: FilesService,
    @Inject(forwardRef(() => AutomationsService))
    private readonly automationsService: AutomationsService,
    private readonly flowNodesService: FlowNodesService,
    private readonly socketsService: SocketsService,
    private readonly webPushService: WebPushService,
    private readonly webPushThrottleService: WebPushThrottleService,
    private readonly productsService: ProductsService,
  ) {}

  async changeMesssageStatus(params: {
    whatsappMessageId: string;
    status?: MessageStatus;
    errorMessage?: string;
    errorCode?: string;
  }): Promise<boolean> {
    const { whatsappMessageId, status, errorCode, errorMessage } = params;
    const message = await this.prismaService.message.findFirst({
      where: { whatsappMessageId },
    });

    if (!message) return false;
    if (!status) return false;

    function shouldUpdateMessageStatus(
      currentStatus: MessageStatus,
      newStatus: MessageStatus,
    ) {
      const messageStatusesSorted = [
        'enqueued',
        'sent',
        'delivered',
        'read',
        'mismatch',
        'failed',
      ];

      const currentStatusIndex = messageStatusesSorted.indexOf(currentStatus);
      const newStatusIndex = messageStatusesSorted.indexOf(newStatus);

      return newStatusIndex > currentStatusIndex;
    }

    if (!shouldUpdateMessageStatus(message.status, status)) return true;

    const updatedMessage = await this.prismaService.message.update({
      where: {
        id: message.id,
      },
      data: {
        status,
        errorMessage: errorMessage ? String(errorMessage) : undefined,
        errorCode: errorCode ? String(errorCode) : undefined,
      },
      include: {
        conversation: {
          select: {
            customerId: true,
          },
        },
      },
    });

    await this.eventEmitter.emit(MessageEventsEnum.UPDATE_MESSAGE_STATUS, {
      id: updatedMessage.id,
      status: updatedMessage.status,
      tempId: updatedMessage.tempId,
      companyPhoneNumberId: updatedMessage.senderPhoneNumberId,
      conversationId: updatedMessage.conversationId,
    } as UpdateMessageStatusEventPayload);

    if (['1013', '1026', '131026'].includes(String(errorCode))) {
      await this.customersService.updateCustomer({
        where: {
          id: updatedMessage.conversation.customerId,
        },
        data: {
          isOptedOut: true,
        },
      });
    }
    return true;
  }

  async updateMessage(params: {
    where: Prisma.MessageWhereUniqueInput;
    data: Prisma.MessageUncheckedUpdateInput;
    withContextMessage?: boolean;
  }): Promise<Message> {
    const { where, data, withContextMessage = false } = params;
    return await this.prismaService.message.update({
      data,
      where,
      include: {
        ...(withContextMessage && { context: true }),
      },
    });
  }

  async listMessages(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.MessageWhereUniqueInput;
    where?: Prisma.MessageWhereInput;
    orderBy?: Prisma.MessageOrderByWithRelationInput;
    withIncludes?: true;
  }): Promise<MessageWithCardIncluded[]>;
  async listMessages(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.MessageWhereUniqueInput;
    where?: Prisma.MessageWhereInput;
    orderBy?: Prisma.MessageOrderByWithRelationInput;
    withIncludes?: false;
  }): Promise<Message[]>;
  async listMessages(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.MessageWhereUniqueInput;
    where?: Prisma.MessageWhereInput;
    orderBy?: Prisma.MessageOrderByWithRelationInput;
    withIncludes?: boolean;
  }): Promise<MessageWithCardIncluded[] | Message[]> {
    const { skip, take, cursor, where, orderBy } = params;
    return await this.prismaService.message.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
      include: {
        ...(params.withIncludes && includes),
      },
    });
  }

  public async findFirstMessage(
    args: Prisma.MessageFindFirstArgs,
  ): Promise<Message | null> {
    return await this.prismaService.message.findFirst(args);
  }

  public async handleMessageFromRabbitmqQueue(payload: QueueBodyPayload) {
    const {
      campaignId,
      company,
      messages,
      templateArgs,
      templateId,
      automationId,
    } = payload;
    const messageTemplate =
      await this.messageTemplatesService.findMessageTemplate({
        id: templateId,
      });
    if (messageTemplate?.status === 'approved') {
      for (const data of messages) {
        try {
          await this.processQueueMessage({
            campaignId,
            company,
            templateArgs,
            templateId,
            customer: data.customer,
            automationId,
          });
        } catch (err: any) {
          await this.logsService.createSentryLog(
            {
              message: err.message,
              severity: 'error',
              meta: {
                origin: 'messagesService.handleMessageFromRabbitmqQueue',
                message: JSON.stringify(data),
              },
            },
            err,
          );
        }
      }
    } else {
      await this.whatsappCampaignsService.updateWhatsappCampaign({
        where: { id: campaignId },
        data: {
          status: 'interrupted',
        },
      });
      await this.logsService.createSentryLog(
        {
          message: 'Campanha interrompida por template não aprovado',
          severity: 'error',
          meta: {
            origin: 'messagesService.handleMessageFromRabbitmqQueue',
          },
        },
        new Error('Campanha interrompida por template não aprovado'),
      );
    }

    await this.whatsappCampaignsService.incrementTotalProcessed(
      campaignId,
      messages.length,
    );
  }

  @SqsMessageHandler(SqsQueueEnum.WHATSAPP_MESSAGES, false)
  public async handleMessageFromSqsQueue(message: any) {
    const body: QueueBodyPayload = JSON.parse(message.Body || '{}');
    const { campaignId, company, messages, templateArgs, templateId } = body;

    const messageTemplate =
      await this.messageTemplatesService.findMessageTemplate({
        id: templateId,
      });

    if (messageTemplate?.status === 'approved') {
      const concurrencyLimit = 3;
      await async.eachLimit(
        messages,
        concurrencyLimit,
        async (data: QueueMessagePayload) => {
          try {
            await this.processQueueMessage({
              campaignId,
              company,
              templateArgs,
              templateId,
              customer: data.customer,
            });
          } catch (err: any) {
            await this.logsService.createSentryLog(
              {
                message: err.message,
                severity: 'error',
                meta: {
                  origin: 'messagesService.handleMessageFromSqsQueue',
                  message: JSON.stringify(data),
                },
              },
              err,
            );
          }
        },
      );
    } else {
      await this.whatsappCampaignsService.updateWhatsappCampaign({
        where: { id: campaignId },
        data: {
          status: 'interrupted',
        },
      });
      await this.logsService.createSentryLog(
        {
          message: 'Campanha interrompida por template não aprovado',
          severity: 'error',
          meta: {
            origin: 'messagesService.handleMessageFromSqsQueue',
          },
        },
        new Error('Campanha interrompida por template não aprovado'),
      );
    }
    await this.whatsappCampaignsService.incrementTotalProcessed(
      campaignId,
      messages.length,
    );
  }

  @SqsConsumerEventHandler(SqsQueueEnum.WHATSAPP_MESSAGES, 'processing_error')
  public async onProcessingError(error: Error, message: Message) {
    await this.logsService.createSentryLog(
      {
        message: error.message,
        severity: 'error',
        meta: {
          origin: 'messagesService.onProcessingError',
          message: JSON.stringify(message),
        },
      },
      error,
    );
  }

  private async processQueueMessage({
    campaignId,
    company,
    templateArgs,
    templateId,
    customer,
    automationId,
  }: Omit<QueueBodyPayload, 'messages'> & {
    customer: QueueMessagePayload['customer'];
  }) {
    let conversationId = customer.conversationId;
    let conversation: ConversationWithIncludes | null = null;

    conversation = await this.conversationsService.findOrCreateConversation(
      {
        companyId: company.id,
        recipientPhoneNumberId: customer.phoneNumberId,
        recipientName: customer.name,
      },
      {
        shouldCreateConversationTicket: false,
      },
    );
    if (!conversationId && conversation) {
      conversationId = conversation.id;
    }

    if (!conversationId) {
      throw new BadRequestException('Conversa não encontrada');
    }

    const lastSentMessage = conversation?.messages.at(0);
    if (
      lastSentMessage &&
      campaignId &&
      lastSentMessage?.whatsappCampaignId === campaignId
    ) {
      return;
    }

    const mappedTemplateArgs = MessageTemplateUtils.mapTemplateArguments(
      templateArgs,
      customer,
    );
    await this.sendMessageTemplate(
      {
        templateId: templateId,
        conversationId: conversationId,
        companyId: company.id,
        senderPhoneNumberId: company.phoneNumberId,
        templateArgs: {
          [TemplateParametersEnum.CUSTOMER_NAME]: NameUtils.getFirstName(
            customer.name,
          ),
          [TemplateParametersEnum.COMPANY_NAME]: company.name,
          ...mappedTemplateArgs,
        },
        whatsappCampaignId: campaignId,
        automationId,
      },
      false,
    );
  }

  async sendWhatsappCampaignMessages(
    whatsappCampaignId: string,
  ): Promise<boolean> {
    return await this.commandBus.execute(
      new SendWhatsappCampaignMessagesCommand(whatsappCampaignId),
    );
  }

  async sendMessageTemplateByPhone(
    sendMessageTemplateByPhoneDto: SendMessageTemplateByPhoneDto & {
      companyId: string;
      senderPhoneNumberId: string;
      automationId?: string;
      customerCouponId?: string;
      agentId?: string;
    },
  ) {
    const shouldCreateConversationTicket =
      sendMessageTemplateByPhoneDto.shouldCreateConversationTicket === true;
    const conversation =
      await this.conversationsService.findOrCreateConversation(
        {
          companyId: sendMessageTemplateByPhoneDto.companyId,
          recipientName: sendMessageTemplateByPhoneDto.recipientName,
          recipientPhoneNumberId:
            sendMessageTemplateByPhoneDto.recipientPhoneNumberId,
        },
        {
          shouldCreateConversationTicket,
        },
      );

    if (!conversation) {
      throw new BadRequestException('Conversa não encontrada');
    }

    let messageTemplateId: string | undefined | null;
    let automationId: string | undefined | null =
      sendMessageTemplateByPhoneDto.automationId;

    if (sendMessageTemplateByPhoneDto.templateName) {
      const msgTemplate =
        await this.messageTemplatesService.findMessageTemplate({
          name: sendMessageTemplateByPhoneDto.templateName,
          companyId: sendMessageTemplateByPhoneDto.companyId,
        });
      messageTemplateId = msgTemplate?.id;
    } else if (
      sendMessageTemplateByPhoneDto.templateType === 'ABANDONED_CART'
    ) {
      const abandonedCartAutomation =
        await this.automationsService.findAutomation({
          companyId: sendMessageTemplateByPhoneDto.companyId,
          isActive: true,
          automationType: {
            slug: 'abandoned_cart',
          },
        });
      automationId = abandonedCartAutomation?.id;
      messageTemplateId = abandonedCartAutomation?.messageTemplateId;

      if (!abandonedCartAutomation) {
        throw new BadRequestException(
          'Automação de carrinho abandonado não encontrada',
        );
      }
    }

    if (!messageTemplateId) {
      throw new BadRequestException('Template não encontrado');
    }

    return await this.commandBus.execute(
      new SendMessageTemplateCommand(
        conversation,
        messageTemplateId,
        {
          ...sendMessageTemplateByPhoneDto.templateArgs,
          [TemplateParametersEnum.CUSTOMER_NAME]: NameUtils.getFirstName(
            sendMessageTemplateByPhoneDto.recipientName,
          ),
        },
        sendMessageTemplateByPhoneDto.companyId,
        sendMessageTemplateByPhoneDto.senderPhoneNumberId,
        null,
        shouldCreateConversationTicket,
        automationId || null,
        sendMessageTemplateByPhoneDto.customerCouponId || null,
        undefined,
        null,
        sendMessageTemplateByPhoneDto.agentId || null,
      ),
    );
  }

  async sendMessageTemplate(
    sendMessageTemplateDto: SendMessageTemplateDto & {
      companyId: string;
      senderPhoneNumberId: string;
      whatsappCampaignId?: string;
      automationId?: string | null;
      customerCouponId?: string | null;
      customMediaUrl?: string;
      flowNodeId?: string;
      agentId?: string;
    },
    shouldCreateConversationTicket = true,
  ) {
    const conversation = await this.conversationsService.findConversation({
      id: sendMessageTemplateDto.conversationId,
    });

    if (!conversation) {
      throw new BadRequestException('Conversa não encontrada');
    }

    return await this.commandBus.execute(
      new SendMessageTemplateCommand(
        conversation,
        sendMessageTemplateDto.templateId,
        sendMessageTemplateDto.templateArgs,
        sendMessageTemplateDto.companyId,
        sendMessageTemplateDto.senderPhoneNumberId,
        sendMessageTemplateDto.whatsappCampaignId || null,
        shouldCreateConversationTicket,
        sendMessageTemplateDto.automationId || null,
        sendMessageTemplateDto.customerCouponId || null,
        sendMessageTemplateDto.customMediaUrl,
        sendMessageTemplateDto.flowNodeId || null,
        sendMessageTemplateDto.agentId || null,
      ),
    );
  }

  async sendProductCatalogMessage(
    sendMessageDto: SendMessageDto & {
      companyId: string;
      senderPhoneNumberId: string;
      catalogData: SessionCatalogData;
      isAutomaticResponse?: boolean;
      agentId?: string;
      flowNodeId?: string;
      agentContext?: JwtPayload;
    },
  ) {
    return await this.commandBus.execute(
      new SendMessageCommand(
        sendMessageDto.companyId,
        sendMessageDto.senderPhoneNumberId,
        sendMessageDto.text,
        sendMessageDto.conversationId,
        undefined, // fileKey - não usado para catálogo
        undefined, // mediaType - não usado para catálogo
        sendMessageDto.tempId,
        undefined, // buttons - não usado para catálogo
        sendMessageDto.isAutomaticResponse,
        sendMessageDto.agentId,
        sendMessageDto.flowNodeId,
        sendMessageDto.contextMessageId,
        sendMessageDto.agentContext,
        sendMessageDto.catalogData,
      ),
    );
  }

  async sendMessage(
    sendMessageDto: SendMessageDto & {
      companyId: string;
      senderPhoneNumberId: string;
      buttons?: SessionMessageButton[];
      isAutomaticResponse?: boolean;
      agentId?: string;
      flowNodeId?: string;
      fileKey?: string;
      mediaType?: MediaType;
      agentContext?: JwtPayload;
    },
  ) {
    return await this.commandBus.execute(
      new SendMessageCommand(
        sendMessageDto.companyId,
        sendMessageDto.senderPhoneNumberId,
        sendMessageDto.text,
        sendMessageDto.conversationId,
        sendMessageDto.fileKey,
        sendMessageDto.mediaType,
        sendMessageDto.tempId,
        sendMessageDto.buttons,
        sendMessageDto.isAutomaticResponse,
        sendMessageDto.agentId,
        sendMessageDto.flowNodeId,
        sendMessageDto.contextMessageId,
        sendMessageDto.agentContext,
      ),
    );
  }

  async receiveMessage(receiveMessageDto: ReceiveMessageDto) {
    let company: CompanyWithIncludes | null = null;

    if (receiveMessageDto.gupshupAppName) {
      company = await this.companiesService.findCompanyByGupshupAppName(
        receiveMessageDto.gupshupAppName,
      );
    }

    if (!company && receiveMessageDto.whatsappPhoneNumberId) {
      company = await this.companiesService.findCompanyByWhatsappPhoneNumberId(
        receiveMessageDto.whatsappPhoneNumberId,
      );
    }

    if (!company) {
      company = await this.companiesService.findCompanyByPhoneNumberIds([
        receiveMessageDto.recipientPhoneNumberId,
        receiveMessageDto.senderPhoneNumberId,
      ]);
    }

    if (!company) {
      throw new BadRequestException('Empresa não encontrada');
    }

    return await this.commandBus.execute(
      new ReceiveMessageCommand(
        receiveMessageDto.wamId,
        company,
        receiveMessageDto.senderPhoneNumberId,
        receiveMessageDto.senderName,
        receiveMessageDto.text,
        receiveMessageDto.mediaType,
        receiveMessageDto.mediaUrl,
        receiveMessageDto.postbackText,
        receiveMessageDto.externalContextMessageId,
        receiveMessageDto.payload,
      ),
    );
  }

  async listMessagesByConversationId(
    conversationId: string,
    options: {
      page?: number;
    },
  ) {
    const itemsPerPage = 30;
    const skip = options.page ? (options.page - 1) * itemsPerPage : 0;
    const messages = await this.listMessages({
      where: {
        conversationId,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: itemsPerPage,
      skip,
      withIncludes: true,
    });

    return messages.reverse();
  }

  async updateManyMessages(params: {
    where: Prisma.MessageWhereInput;
    data: Prisma.MessageUncheckedUpdateInput;
  }): Promise<Prisma.BatchPayload> {
    return await this.prismaService.message.updateMany(params);
  }

  async createMessage(
    data: Prisma.MessageUncheckedCreateInput,
    options: {
      externalContextMessageId?: string;
      shouldCreateConversationTicket?: boolean;
      shouldEmitEvent?: boolean;
    },
  ): Promise<Message> {
    const {
      externalContextMessageId,
      shouldCreateConversationTicket = true,
      shouldEmitEvent = true,
    } = options;

    let createdMessage: Message = await this.prismaService.message.create({
      data,
      include: {
        conversation: {
          select: {
            id: true,
            currentFlowNodeId: true,
            company: {
              select: {
                isAutomaticSortingActive: true,
              },
            },
          },
        },
      },
    });

    const isTemplateMessage = !!createdMessage.messageTemplateId;
    const isCustomerMessage = !createdMessage.fromSystem;

    if (isTemplateMessage || isCustomerMessage) {
      await this.whatsappSessionsService.findOrCreateWhatsappSessionByConversation(
        { conversationId: createdMessage.conversationId },
      );

      if (shouldCreateConversationTicket) {
        await this.conversationTicketService.findOrCreateConversationTicketByConversation(
          createdMessage.conversationId,
        );
      }
    }

    if (!!externalContextMessageId) {
      const searchField = this.isWamId(externalContextMessageId)
        ? 'wamId'
        : 'whatsappMessageId';

      const contextMessage = await this.findFirstMessage({
        where: {
          [searchField]: externalContextMessageId,
        },
      });

      if (contextMessage) {
        createdMessage = await this.updateMessage({
          where: {
            id: createdMessage.id,
          },
          data: {
            contextMessageId: contextMessage.id,
          },
          withContextMessage: true,
        });
      }
    }

    if (shouldEmitEvent) {
      const conversation = await this.conversationsService.findConversation({
        id: createdMessage.conversationId,
      });
      if (!conversation) {
        throw new BadRequestException('Conversa não encontrada');
      }

      this.eventEmitter.emit(MessageEventsEnum.NEW_MESSAGE, {
        message: createdMessage,
        companyId: conversation.companyId,
        companyPhoneNumberId: createdMessage.fromSystem
          ? createdMessage.senderPhoneNumberId
          : createdMessage.recipientPhoneNumberId,
      } as NewMessageEventPayload);
    }

    return createdMessage;
  }

  async uploadMedia(file: Express.Multer.File, companyId: string) {
    return await this.filesService.uploadFile({
      file,
      companyId,
      keyPrefix: 'message-templates/media',
      uploadOptions: {
        isPublic: true,
      },
    });
  }

  private isWamId(contextId: string): boolean {
    return contextId.startsWith('wamid');
  }

  async sendPushNotification(payload: NewMessageEventPayload, server: Server) {
    if (
      payload.message.fromSystem ||
      !this.webPushThrottleService.canSendPushForCompany(payload.companyId) ||
      !(await this.companiesService.isWorkingHours(
        payload.companyId,
        new Date(),
      ))
    ) {
      return;
    }

    const usersAllowedToSeeConversation =
      await this.conversationsService.getUsersAllowedToSeeConversation(
        payload.message.conversationId,
        {
          skipReviUsers: false,
        },
      );

    if (!usersAllowedToSeeConversation.length) return;

    const connectedUsersIds = await this.socketsService.getRoomUsersIds(
      server,
      payload.companyPhoneNumberId,
    );

    const usersAllowedToSeeConversationNotConnected =
      usersAllowedToSeeConversation.filter(
        (user) => !connectedUsersIds.includes(user.id),
      );

    for (const user of usersAllowedToSeeConversationNotConnected) {
      try {
        await this.webPushService.sendPushToUserAllSubscriptions(
          user.id,
          user.companyId,
          WebPushNotificationPayloadFactory.create({
            title: 'Revi',
            body: `Você tem novas mensagens!`,
            tag: `new-message-${payload.message.id}-${user.id}`,
            type: WebPushType.notification,
          }),
          WebPushType.notification,
        );
      } catch (err) {
        console.log(err);
      }
    }
  }

  async sendProductCatalog(data: SendProductCatalogDto) {
    const {
      conversationId,
      catalogId,
      type,
      bodyText,
      footerText,
      headerText,
    } = data;

    const conversation = await this.validateConversation(conversationId);
    if (!conversation.company.gupshupAppName) {
      throw new BadRequestException('Errpr no gupshup Name');
    }

    const company = await this.validateCompany(
      conversation.company.gupshupAppName,
    );
    const companyId = conversation.companyId;

    const catalogData = await this.buildCatalogData({
      type,
      catalogId,
      companyId,
      bodyText,
      footerText,
      headerText,
      thumbnailProductId: data.thumbnailProductId,
      singleProductId: data.singleProductId,
      sections: data.sections,
    });

    return await this.sendProductCatalogMessage({
      text: bodyText,
      conversationId: conversation.id,
      companyId,
      senderPhoneNumberId: company.phoneNumberId,
      tempId: undefined,
      catalogData,
      agentId: undefined,
      isAutomaticResponse: false,
      flowNodeId: undefined,
      agentContext: undefined,
      contextMessageId: undefined,
    });
  }

  private async validateConversation(conversationId: string) {
    const conversation = await this.conversationsService.findConversation({
      id: conversationId,
    });

    if (!conversation) {
      throw new BadRequestException('Conversa não encontrada');
    }

    if (!conversation.company.gupshupAppName) {
      throw new BadRequestException('Empresa não encontrada');
    }

    return conversation;
  }

  private async validateCompany(gupshupAppName: string) {
    const company = await this.companiesService.findCompanyByGupshupAppName(
      gupshupAppName,
    );

    if (!company) {
      throw new BadRequestException('Empresa não encontrada');
    }

    return company;
  }

  private async buildCatalogData(params: {
    type: CatalogType;
    catalogId: string;
    companyId: string;
    bodyText: string;
    footerText?: string;
    headerText?: string;
    thumbnailProductId?: string;
    singleProductId?: string;
    sections?: ProductSectionDto[];
  }): Promise<SessionCatalogData> {
    const { type } = params;

    switch (type) {
      case CatalogType.CATALOG:
        if (!params.thumbnailProductId) {
          throw new BadRequestException(
            'thumbnailProductId é obrigatório para tipo catalog',
          );
        }
        return await this.buildCatalogType({
          catalogId: params.catalogId,
          companyId: params.companyId,
          bodyText: params.bodyText,
          footerText: params.footerText,
          thumbnailProductId: params.thumbnailProductId,
        });

      case CatalogType.SINGLE_PRODUCT:
        if (!params.singleProductId) {
          throw new BadRequestException(
            'singleProductId é obrigatório para tipo smp',
          );
        }
        return await this.buildSingleProductType({
          catalogId: params.catalogId,
          companyId: params.companyId,
          bodyText: params.bodyText,
          footerText: params.footerText,
          singleProductId: params.singleProductId,
        });

      case CatalogType.MULTI_PRODUCT:
        if (!params.sections || params.sections.length === 0) {
          throw new BadRequestException('sections é obrigatório para tipo mpm');
        }
        return await this.buildMultiProductType({
          catalogId: params.catalogId,
          companyId: params.companyId,
          bodyText: params.bodyText,
          footerText: params.footerText,
          headerText: params.headerText,
          sections: params.sections,
        });

      default:
        throw new BadRequestException('Tipo de catálogo inválido');
    }
  }

  private async buildCatalogType(params: {
    catalogId: string;
    companyId: string;
    bodyText: string;
    footerText?: string;
    thumbnailProductId: string;
  }): Promise<SessionCatalogData> {
    const { catalogId, companyId, bodyText, footerText, thumbnailProductId } =
      params;

    const result = await this.productsService.getExternalMappingsFromVariants({
      companyId,
      catalogId,
      variantIds: [thumbnailProductId],
    });

    const mapping = result.mappings[thumbnailProductId];
    if (!mapping) {
      throw new BadRequestException(
        `Produto thumbnail ${thumbnailProductId} não mapeado no catálogo`,
      );
    }

    return {
      type: 'catalog',
      bodyText,
      footerText: footerText ?? 'Explore nosso catálogo!',
      thumbnailProductId: mapping.externalProductId,
    };
  }

  private async buildSingleProductType(params: {
    catalogId: string;
    companyId: string;
    bodyText: string;
    footerText?: string;
    singleProductId: string;
  }): Promise<SessionCatalogData> {
    const { catalogId, companyId, bodyText, footerText, singleProductId } =
      params;

    const { mappings, catalog } =
      await this.productsService.getExternalMappingsFromVariants({
        companyId,
        catalogId,
        variantIds: [singleProductId],
      });

    const mapping = mappings[singleProductId];
    if (!mapping) {
      throw new BadRequestException(
        `Produto ${singleProductId} não mapeado no catálogo`,
      );
    }

    return {
      type: 'single_product',
      bodyText,
      footerText: footerText ?? 'Clique para ver detalhes!',
      productRetailerId: mapping.externalProductId,
      catalogId: catalog.metaCatalogId,
    };
  }

  private async buildMultiProductType(params: {
    catalogId: string;
    companyId: string;
    bodyText: string;
    footerText?: string;
    headerText?: string;
    sections: ProductSectionDto[];
  }): Promise<SessionCatalogData> {
    const { catalogId, companyId, bodyText, footerText, headerText, sections } =
      params;

    const allProductIds = sections.flatMap((section) => section.productIds);

    const { mappings, catalog } =
      await this.productsService.getExternalMappingsFromVariants({
        companyId,
        catalogId,
        variantIds: allProductIds,
      });

    const productSections = sections.map((section) => {
      const productItems = section.productIds.map((variantId) => {
        const mapping = mappings[variantId];
        if (!mapping) {
          throw new BadRequestException(
            `Produto ${variantId} não mapeado no catálogo`,
          );
        }
        return { productRetailerId: mapping.externalProductId };
      });

      return { title: section.title, productItems };
    });

    return {
      type: 'multi_product',
      catalogId: catalog.metaCatalogId,
      headerText: headerText ?? 'Produtos Selecionados',
      bodyText,
      footerText: footerText ?? 'Escolha o produto ideal!',
      productSections,
    };
  }
}
