import { SendMessageTemplateHandler } from './handlers/send-message-template.handler';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { AutomaticSortingOptionsModule } from 'src/automatic-sorting-options/automatic-sorting-options.module';
import { CompaniesModule } from 'src/companies/companies.module';
import { ConversationTicketsModule } from 'src/conversation-tickets/conversation-tickets.module';
import { ConversationsModule } from 'src/conversations/conversations.module';
import { CustomersModule } from 'src/customers/customers.module';
import { GupshupModule } from 'src/gupshup/gupshup.module';
import { MessageTemplatesModule } from 'src/message-templates/message-templates.module';
import { SocketsModule } from 'src/sockets/sockets.module';
import { UsersModule } from 'src/users/users.module';
import { LogsModule } from './../logs/logs.module';
import { WhatsappCampaignsModule } from './../whatsapp-campaigns/whatsapp-campaigns.module';
import { WhatsappSessionsModule } from './../whatsapp-sessions/whatsapp-sessions.module';
import { WhatsappModule } from './../whatsapp/whatsapp.module';
import { MessagesController } from './messages.controller';
import { MessagesGateway } from './messages.gateway';
import { MessagesService } from './messages.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { AutomaticRepliesModule } from 'src/automatic-replies/automatic-replies.module';
import { ApiKeysModule } from 'src/api-keys/api-keys.module';
import { CqrsModule } from '@nestjs/cqrs';
import { SqsModule } from '@ssut/nestjs-sqs';
import { SqsQueueEnum } from 'src/shared/types/SqsQueueEnum';
import { ReceiveMessageHandler } from './handlers/receive-message.handler';
import { RabbitmqModule } from 'src/rabbitmq/rabbitmq.module';
import { SendMessageHandler } from './handlers/send-message.handler';
import { SendWhatsappCampaignMessagesHandler } from './handlers/send-whatsapp-campaign-messages.handler';
import { FlowTriggersModule } from 'src/flow-triggers/flow-triggers.module';
import { FlowNodeButtonsModule } from 'src/flow-node-buttons/flow-node-buttons.module';
import { ConversationCategoriesModule } from 'src/conversation-categories/conversation-categories.module';
import { FlowNodesModule } from 'src/flow-nodes/flow-nodes.module';
import { TagsModule } from 'src/tags/tags.module';
import { FlowEventsModule } from 'src/flow-events/flow-events.module';
import { CompanyDefinedFieldsModule } from 'src/company-defined-fields/company-defined-fields.module';
import { FlowNodeConditionsQueryBuilder } from './builder/flow-condition.query-builder';
import { FlowsModule } from 'src/flows/flows.module';
import { FilesModule } from 'src/files/files.module';
import { AutomationsModule } from 'src/automations/automations.module';
import { WhatsappCloudApiModule } from 'src/whatsapp-cloud-api/whatsapp-cloud-api.module';
import { ShortUrlsModule } from 'src/short-urls/short-urls.module';
import { CouponsModule } from 'src/coupons/coupons.module';
import { EmailsModule } from 'src/emails/emails.module';
import { WebPushModule } from 'src/web-push/web-push.module';
import { ProductsModule } from 'src/products/products.module';
import { AiAgentsModule } from 'src/ai-agents/ai-agents.module';
import { AbandonedCartsModule } from 'src/abandoned-carts/abandoned-carts.module';

export const CommandHandlers = [
  SendMessageTemplateHandler,
  ReceiveMessageHandler,
  SendMessageHandler,
  SendWhatsappCampaignMessagesHandler,
  FlowNodeConditionsQueryBuilder,
];

@Module({
  controllers: [MessagesController],
  providers: [MessagesService, MessagesGateway, ...CommandHandlers],
  imports: [
    SqsModule.register({
      consumers: [
        {
          name: SqsQueueEnum.WHATSAPP_MESSAGES,
          queueUrl: process.env.AWS_SQS_WHATSAPP_MESSAGE_QUEUE_URL!,
          region: process.env.AWS_REGION,
          // pollingWaitTimeMs: 0,
        },
      ],
      producers: [
        {
          name: SqsQueueEnum.WHATSAPP_MESSAGES,
          queueUrl: process.env.AWS_SQS_WHATSAPP_MESSAGE_QUEUE_URL!,
          region: process.env.AWS_REGION,
        },
      ],
    }),
    RabbitmqModule,
    forwardRef(() => GupshupModule),
    WhatsappCloudApiModule,
    forwardRef(() => WhatsappModule),
    forwardRef(() => ConversationsModule),
    forwardRef(() => WhatsappCampaignsModule),
    forwardRef(() => MessageTemplatesModule),
    forwardRef(() => FlowsModule),
    forwardRef(() => CustomersModule),
    UsersModule,
    ConfigModule,
    JwtModule,
    SocketsModule,
    forwardRef(() => CompaniesModule),
    forwardRef(() => AutomationsModule),
    forwardRef(() => ConversationTicketsModule),
    AutomaticSortingOptionsModule,
    WhatsappSessionsModule,
    PrismaModule,
    AutomaticRepliesModule,
    ApiKeysModule,
    CqrsModule,
    LogsModule,
    FlowTriggersModule,
    FlowNodeButtonsModule,
    ConversationCategoriesModule,
    FlowNodesModule,
    TagsModule,
    FlowEventsModule,
    CompanyDefinedFieldsModule,
    FilesModule,
    ShortUrlsModule,
    forwardRef(() => CouponsModule),
    forwardRef(() => CouponsModule),
    forwardRef(() => EmailsModule),
    SocketsModule,
    WebPushModule,
    ProductsModule,
    forwardRef(() => AiAgentsModule),
    AbandonedCartsModule,
  ],
  exports: [MessagesService, ReceiveMessageHandler],
})
export class MessagesModule {}
