import { BadRequestException, forwardRef, Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { Company } from '@prisma/client';
import { AccountBalanceService } from 'src/companies/account-balance.service';
import { CompaniesService } from 'src/companies/companies.service';
import { GupshupService } from 'src/gupshup/gupshup.service';
import { MessageTemplatesService } from 'src/message-templates/message-templates.service';
import MessageTemplateWithIncludes, {
  SimplifiedTemplateMessageCard,
} from 'src/message-templates/types/MessageTemplateWithIncludes';
import { ConversationWithIncludes } from 'src/conversations/types/ConversationWithIncludes';
import { MessageTemplateUtils } from 'src/shared/utils/message-template.utils';
import { ShortUrlsService } from 'src/short-urls/short-urls.service';
import { WhatsappCloudApiService } from 'src/whatsapp-cloud-api/whatsapp-cloud-api.service';
import { SendMessageTemplateCommand } from '../commands/send-message-template.command';
import { MessagesService } from '../messages.service';
import { GupshupSendMessageTemplateStrategy } from '../strategies/send-message-template/gupshup-send-message-template.strategy';
import { WhatsappCloudApiSendMessageTemplateStrategy } from '../strategies/send-message-template/whatsapp-cloud-api-send-message-template.strategy';
import { ConversationsService } from 'src/conversations/conversations.service';
import { AutomationsService } from 'src/automations/automations.service';
import {
  AUTOMATION_WHATSAPP_MESSAGE_LIMIT_ERROR,
  WHATSAPP_MESSAGE_LIMIT_ERROR,
} from '../constants/whatsapp-message-limit-errors';

@CommandHandler(SendMessageTemplateCommand)
export class SendMessageTemplateHandler
  implements ICommandHandler<SendMessageTemplateCommand>
{
  constructor(
    private readonly accountBalanceService: AccountBalanceService,
    private readonly automationsService: AutomationsService,
    @Inject(forwardRef(() => CompaniesService))
    private readonly companiesService: CompaniesService,
    private readonly messageTemplatesService: MessageTemplatesService,
    private readonly gupshupService: GupshupService,
    private readonly messagesService: MessagesService,
    @Inject(forwardRef(() => ConversationsService))
    private readonly conversationsService: ConversationsService,
    private readonly whatsappCloudApiService: WhatsappCloudApiService,
    private readonly shortUrlsService: ShortUrlsService,
  ) {}

  private async getMessageCardsObject(
    cards: SimplifiedTemplateMessageCard[] = [],
    templateArgs: Record<string, string | undefined>,
  ) {
    if (cards.length === 0) return [];
    const res = [] as any;

    for (const card of cards) {
      const { replacedTemplate } =
        MessageTemplateUtils.extractParametersArrayFromTemplateText(
          card.body,
          templateArgs,
        );
      res.push({
        text: replacedTemplate,
        cardIndex: card.cardIndex,
        messageTemplateCardId: card.id,
      });
    }
    return res;
  }

  async execute(command: SendMessageTemplateCommand) {
    const {
      conversation,
      templateId,
      templateArgs,
      companyId,
      senderPhoneNumberId,
      whatsappCampaignId,
      shouldCreateConversationTicket,
      automationId,
      customerCouponId,
      customMediaUrl,
      flowNodeId,
      agentId,
    } = command;

    const isDirectMessage = !whatsappCampaignId;
    if (isDirectMessage) {
      await this.validateMessageLimit(companyId, automationId);
    }

    const messageTemplate = await this.findMessageTemplate(templateId);
    const { replacedTemplate } =
      MessageTemplateUtils.extractParametersArrayFromTemplateText(
        messageTemplate.templateText,
        templateArgs,
      );
    const messageCardsText = await this.getMessageCardsObject(
      messageTemplate.messageTemplateCards,
      templateArgs,
    );

    const resolvedMediaUrl = customMediaUrl || messageTemplate.mediaUrl;

    await this.removeConversationFromFlow(
      conversation.id,
      !!conversation.currentFlowNodeId,
      !agentId,
    );

    const createdMessage = await this.messagesService.createMessage(
      {
        text: replacedTemplate,
        conversationId: conversation.id,
        senderPhoneNumberId,
        recipientPhoneNumberId: conversation.recipientPhoneNumberId,
        fromSystem: true,
        createdAt: new Date(),
        messageTemplateId: templateId,
        whatsappCampaignId,
        automationId,
        customerCouponId,
        flowNodeId,
        mediaUrl: resolvedMediaUrl,
        messageCards: {
          createMany: {
            data: messageCardsText,
          },
        },
        createdByUserId: agentId,
      },
      {
        shouldCreateConversationTicket,
      },
    );

    const company = await this.findCompany(companyId);
    if (!company.gupshupAppName) {
      throw new BadRequestException(`Empresa não possui [App Name]`);
    }

    const shouldUseGupshup = true;
    if (shouldUseGupshup) {
      const strategy = new GupshupSendMessageTemplateStrategy(
        this.gupshupService,
      );
      await strategy.sendMessageTemplate(
        company,
        messageTemplate,
        templateArgs,
        conversation.recipientPhoneNumberId,
        createdMessage.id,
        resolvedMediaUrl,
      );
    } else {
      const strategy = new WhatsappCloudApiSendMessageTemplateStrategy(
        this.whatsappCloudApiService,
        this.shortUrlsService,
        this.messagesService,
      );
      await strategy.sendMessageTemplate(
        company,
        messageTemplate,
        templateArgs,
        conversation.recipientPhoneNumberId,
        createdMessage.id,
        resolvedMediaUrl,
      );
    }

    return createdMessage;
  }

  private async validateMessageLimit(
    companyId: string,
    automationId?: string | null,
  ): Promise<void> {
    const hasExceededWhatsappMessagesLimit =
      await this.accountBalanceService.hasExceededWhatsappMessagesLimit(
        companyId,
        1,
      );
    if (hasExceededWhatsappMessagesLimit) {
      throw new BadRequestException(WHATSAPP_MESSAGE_LIMIT_ERROR);
    }

    if (automationId) {
      const automation = await this.automationsService.findAutomation({
        id: automationId,
      });
      if (!automation) {
        throw new BadRequestException('Automação não encontrada');
      }
      const remainingLimit =
        await this.accountBalanceService.getRemainingWhatsappMessageLimitForAutomation(
          companyId,
          automation.id,
          automation.dailyMessageLimitOnWhatsapp,
        );
      if (remainingLimit <= 0) {
        throw new BadRequestException(AUTOMATION_WHATSAPP_MESSAGE_LIMIT_ERROR);
      }
    }
  }

  private async findCompany(companyId: string): Promise<Company> {
    const company = await this.companiesService.findCompany({
      id: companyId,
    });

    if (!company) {
      throw new BadRequestException(`Empresa não encontrada`);
    }

    if (!company.gupshupAppName) {
      throw new BadRequestException(`Empresa não possui [App Name]`);
    }

    return company as any;
  }

  private async findMessageTemplate(
    templateId: string,
  ): Promise<MessageTemplateWithIncludes> {
    const messageTemplate =
      await this.messageTemplatesService.findMessageTemplate(
        {
          id: templateId,
        },
        true,
      );

    if (!messageTemplate) {
      throw new BadRequestException(`Template de mensagem não encontrado`);
    }

    if (messageTemplate.status !== 'approved') {
      throw new BadRequestException(
        `Template de mensagem está com status [${messageTemplate.status}]`,
      );
    }
    return messageTemplate;
  }

  private async removeConversationFromFlow(
    conversationId: string,
    hasFlowNode: boolean,
    isAutomaticResponse: boolean,
  ) {
    if (!hasFlowNode || isAutomaticResponse) {
      return;
    }

    await this.conversationsService.removeConversationFromFlow(conversationId);
  }
}
