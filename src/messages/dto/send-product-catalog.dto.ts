// DTOs
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
  ArrayMinSize,
  ValidateIf,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum CatalogType {
  CATALOG = 'catalog',
  SINGLE_PRODUCT = 'smp',
  MULTI_PRODUCT = 'mpm',
}

export class ProductSectionDto {
  @IsString()
  @IsNotEmpty()
  title: string;

  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  productIds: string[];
}

export class SendProductCatalogDto {
  @IsString()
  @IsNotEmpty()
  conversationId: string;

  @IsString()
  @IsNotEmpty()
  catalogId: string;

  @IsEnum(CatalogType)
  type: CatalogType;

  @IsString()
  @IsNotEmpty()
  bodyText: string;

  @IsOptional()
  @IsString()
  footerText?: string;

  @IsOptional()
  @IsString()
  headerText?: string;

  // Para tipo 'catalog' - obrigatório
  @ValidateIf((o) => o.type === CatalogType.CATALOG)
  @IsString()
  @IsNotEmpty()
  thumbnailProductId?: string;

  // Para tipo 'smp' - obrigatório
  @ValidateIf((o) => o.type === CatalogType.SINGLE_PRODUCT)
  @IsString()
  @IsNotEmpty()
  singleProductId?: string;

  // Para tipo 'mpm' - obrigatório
  @ValidateIf((o) => o.type === CatalogType.MULTI_PRODUCT)
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => ProductSectionDto)
  sections?: ProductSectionDto[];
}
