export type CatalogMessageType = 'catalog' | 'single_product' | 'multi_product';

export type SessionCatalogData = {
  type: CatalogMessageType;
  bodyText: string;
  footerText?: string;
} & (
  | {
      type: 'catalog';
      thumbnailProductId?: string;
    }
  | {
      type: 'single_product';
      productRetailerId: string;
      catalogId?: string;
    }
  | {
      type: 'multi_product';
      headerText: string;
      catalogId: string;
      productSections: {
        title: string;
        productItems: {
          productRetailerId: string;
        }[];
      }[];
    }
);
