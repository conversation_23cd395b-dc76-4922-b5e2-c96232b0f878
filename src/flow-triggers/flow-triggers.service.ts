import { Injectable } from '@nestjs/common';
import {
  FlowTrigger,
  Prisma,
  ConversationTicket,
  FlowTriggerType,
} from '@prisma/client';
import { FlowEventsService } from 'src/flow-events/flow-events.service';
import { PayloadMessage } from 'src/messages/dto/receive-message.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { TargetFlowNode } from 'src/shared/types/TargetFlowNode';

const CONVERSATION_FLOW_TRIGGER_TYPES: FlowTriggerType[] = ['csat'];

export interface FlowTriggerWithIncludes extends FlowTrigger {
  targetFlowNode: TargetFlowNode;
}

@Injectable()
export class FlowTriggersService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly flowEventsService: FlowEventsService,
  ) {}

  async findFlowTrigger(
    where: Prisma.FlowTriggerWhereInput,
    include?: Prisma.FlowTriggerInclude,
  ): Promise<FlowTrigger | null> {
    return await this.prismaService.flowTrigger.findFirst({
      where,
      include,
    });
  }

  async findActiveFlowTriggers(
    where: Prisma.FlowTriggerWhereInput,
    include?: Prisma.FlowTriggerInclude,
  ): Promise<FlowTrigger[]> {
    return await this.prismaService.flowTrigger.findMany({
      where: {
        ...where,
        flow: {
          ...where.flow,
          isActive: true,
          isDeleted: false,
        } as Prisma.FlowWhereInput,
        isDeleted: false,
      },
      include,
    });
  }

  async updateFlowTrigger(
    flowTriggerId: string,
    data: Partial<Prisma.FlowTriggerUpdateInput>,
  ) {
    return this.prismaService.flowTrigger.update({
      where: { id: flowTriggerId },
      data,
    });
  }

  async matchMessageWithFlowTrigger(
    companyId: string,
    message: string,
    payloadButton?: PayloadMessage,
  ) {
    const flowTriggers = await this.findActiveFlowTriggers({
      companyId,
    });
    const flowTrigger = flowTriggers.find((trigger) => {
      if (trigger.type === 'quick_reply_message_template') {
        return trigger.messageTemplateButtonId === payloadButton?.postbackText;
      }
      if (trigger.type === 'exact_match') {
        return trigger.text === message;
      }
      if (trigger.type === 'keyword_match') {
        return message
          .toLocaleLowerCase()
          .split(' ')
          .includes(trigger.text.toLocaleLowerCase());
      }
      return false;
    });

    if (!flowTrigger) return null;

    await this.incrementTriggerInvocationCount(flowTrigger.id);

    return flowTrigger;
  }

  async matchConversationTicketWithFlowTrigger(
    companyId: string,
    conversationTicket: ConversationTicket,
  ) {
    const flowTriggers = await this.findActiveFlowTriggers({
      companyId,
      type: { in: CONVERSATION_FLOW_TRIGGER_TYPES },
    });
    const flowTrigger = flowTriggers.find((trigger) => {
      if (trigger.type === 'csat') {
        return conversationTicket.isWaitingForCsatFlow;
      }
      return false;
    });

    if (!flowTrigger) return null;

    await this.incrementTriggerInvocationCount(flowTrigger.id);
    await this.flowEventsService.createFlowEvent({
      companyId,
      conversationId: conversationTicket.conversationId,
      flowTriggerId: flowTrigger.id,
      flowId: flowTrigger.flowId,
      fromSystem: true,
    });

    return flowTrigger;
  }

  private async incrementTriggerInvocationCount(flowTriggerId: string) {
    return this.updateFlowTrigger(flowTriggerId, {
      invocationCount: {
        increment: 1,
      },
    });
  }
}
