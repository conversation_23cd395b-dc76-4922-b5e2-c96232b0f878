import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  LogSource,
  MetaProductCatalog,
  Prisma,
  ProductVariant,
  SourceIntegration,
} from '@prisma/client';
import axios from 'axios';
import { IntegrationsService } from 'src/integrations/integrations.service';
import { BaseProductQueryParams } from './types/BaseProductQueryParams';
import { appRoutes } from 'src/shared/constants/appRoutes';
import { ProductEventPayload } from './types/ProductEventPayload';
import { CloudApiCreateProductData } from 'src/whatsapp-cloud-api/types/catalog/CloudApiCreateProductData';
import { WhatsappCloudApiService } from 'src/whatsapp-cloud-api/whatsapp-cloud-api.service';
import { CloudApiUpdateProductData } from 'src/whatsapp-cloud-api/types/catalog/CloudApiUpdateProductData';
import { StringUtils } from 'src/shared/utils/string.utils';
import { LogsService } from 'src/logs/logs.service';

export type ScalarProductVariantFields = keyof Pick<
  ProductVariant,
  'status' | 'name' | 'title' | 'sku' | 'sourceId'
>;

@Injectable()
export class ProductsService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly integrationsService: IntegrationsService,
    private readonly whatsappCloudApiService: WhatsappCloudApiService,
    private readonly logsService: LogsService,
  ) {}

  async listProductsPaginated(
    params: {
      page: number;
      perPage: number;
    } & BaseProductQueryParams,
  ) {
    const {
      page,
      perPage,
      companyId,
      searchQuery,
      source,
      status,
      minPrice,
      maxPrice,
      minStock,
      maxStock,
      sortBy,
    } = params;

    const skip = (page - 1) * perPage;
    const limit = perPage;

    const priceFilter: any = {};
    if (minPrice !== undefined && minPrice > 0) priceFilter.gte = minPrice;
    if (maxPrice !== undefined && maxPrice > 0) priceFilter.lte = maxPrice;

    const variantFilters: Prisma.ProductVariantWhereInput = {};
    if (status) {
      variantFilters.status = status;
    }
    if (Object.keys(priceFilter).length > 0) {
      variantFilters.price = priceFilter;
    }

    let where: Prisma.ProductWhereInput = {
      companyId,
      ...(searchQuery && {
        OR: [
          { name: { contains: searchQuery, mode: 'insensitive' } },
          { description: { contains: searchQuery, mode: 'insensitive' } },
          {
            variants: {
              some: {
                OR: [
                  { sku: { contains: searchQuery, mode: 'insensitive' } },
                  { sourceId: { contains: searchQuery, mode: 'insensitive' } },
                ],
              },
            },
          },
        ],
      }),
      ...(source && { source }),
      ...(Object.keys(variantFilters).length > 0 && {
        variants: {
          some: variantFilters,
        },
      }),
    };

    if (minStock !== undefined || maxStock !== undefined) {
      const stockConditions: string[] = [];

      if (minStock !== undefined && minStock > 0) {
        stockConditions.push(`SUM(pv.stock_quantity) >= ${minStock}`);
      }
      if (maxStock !== undefined && maxStock > 0) {
        stockConditions.push(`SUM(pv.stock_quantity) <= ${maxStock}`);
      }

      if (stockConditions.length > 0) {
        const stockHaving = `HAVING ${stockConditions.join(' AND ')}`;

        const productIds = await this.prismaService.$queryRawUnsafe<
          { id: string }[]
        >(
          `
          SELECT p.id 
          FROM products p
          INNER JOIN product_variants pv ON p.id = pv.product_id
          WHERE p.company_id = $1
          GROUP BY p.id
          ${stockHaving}
        `,
          companyId,
        );

        if (productIds.length === 0) {
          return {
            data: [],
            meta: {
              page,
              totalPages: 0,
              perPage: limit,
              totalItems: 0,
            },
          };
        }

        where = {
          ...where,
          id: {
            in: productIds.map((p) => p.id),
          },
        };
      }
    }

    let orderBy: Prisma.ProductOrderByWithRelationInput | undefined;

    if (sortBy) {
      orderBy = this.mapSortByToOrder(sortBy);
    }

    const [rawData, total] = await Promise.all([
      this.prismaService.product.findMany({
        where,
        select: {
          id: true,
          name: true,
          sourceId: true,
          createdAt: true,
          updatedAt: true,
          variants: {
            select: {
              id: true,
              status: true,
              price: true,
              stockQuantity: true,
              imageUrl: true,
            },
          },
        },
        orderBy,
        skip,
        take: limit,
      }),
      this.prismaService.product.count({ where }),
    ]);

    const data = rawData.map(this.mapProductToViewModel);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        page,
        totalPages,
        perPage: limit,
        totalItems: total,
      },
    };
  }

  private mapProductToViewModel(product: {
    id: string;
    name: string | null;
    sourceId: string | null;
    updatedAt: Date;
    variants: {
      id: string;
      status: string | null;
      price: number | null;
      stockQuantity: number | null;
      imageUrl: string | null;
    }[];
  }) {
    const variants = product.variants;

    const totalStock = variants.reduce(
      (sum, variant) => sum + (variant.stockQuantity || 0),
      0,
    );

    const numberOfVariants = variants.length;

    const prices = variants
      .map((v) => v.price)
      .filter((p) => p != null) as number[];

    const averagePrice =
      prices.length > 0
        ? Math.round(prices.reduce((a, b) => a + b, 0) / prices.length)
        : null;

    const imageUrl = variants.find((v) => v.imageUrl)?.imageUrl || null;

    const hasActiveVariants = variants.some((v) => v.status === 'active');
    const status = hasActiveVariants ? 'ACTIVE' : 'DRAFTED';

    return {
      id: product.id,
      name: product.name ?? 'Sem nome',
      sourceId: product.sourceId,
      averagePrice,
      totalStock,
      numberOfVariants,
      status,
      updatedAt: product.updatedAt,
      imageUrl,
      hasStock: totalStock > 0,
      isActive: hasActiveVariants,
    };
  }

  private mapSortByToOrder(
    sortBy?: string,
  ): Prisma.ProductOrderByWithRelationInput | undefined {
    switch (sortBy) {
      case 'nameAsc':
        return { name: 'asc' };
      case 'nameDesc':
        return { name: 'desc' };
      case 'sourceCreatedAtAsc':
        return { sourceCreatedAt: 'asc' };
      case 'sourceCreatedAtDesc':
        return { sourceCreatedAt: 'desc' };
      default:
        return undefined;
    }
  }

  async getProductById(companyId: string, productId: string) {
    const product = await this.prismaService.product.findFirst({
      where: {
        id: productId,
        companyId: companyId,
      },
      include: {
        variants: {
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    if (!product) {
      throw new NotFoundException('Produto não encontrado');
    }

    return product;
  }

  async syncCompanyProducts(companyId: string, source?: SourceIntegration) {
    try {
      const integratorApi = this.integrationsService.getIntegratorApiInstance();

      const payload: any = { companyId };
      if (source) {
        payload.source = source;
      }

      const { data } = await integratorApi.post(
        appRoutes.Integrator.Products.syncCompanyProducts(),
        payload,
      );

      return {
        success: true,
        message: 'Sincronização de produtos iniciada com sucesso',
        data,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          `Integrator API error: ${error.response?.status} - ${error.message}`,
        );
      }
      throw error;
    }
  }

  async listDistinctProductsVariantFieldValues<
    T extends ScalarProductVariantFields,
  >(
    companyId: string,
    field: T,
    source?: SourceIntegration,
    integrationConfigId?: string,
  ): Promise<{ [key in T]: any[] }> {
    const where: Prisma.ProductVariantWhereInput = {
      product: {
        companyId,
        ...(integrationConfigId && { integrationConfigId }),
        ...(source && { source }),
      },
      ...(field !== 'sourceId' && { [field]: { not: null } }),
    };

    const results = await this.prismaService.productVariant.findMany({
      select: { [field]: true },
      where,
      distinct: [field as any],
      orderBy: { [field]: 'asc' },
    });

    const distinctValues = results
      .map((result) => result[field])
      .filter((value) => value !== null && value !== undefined);

    return {
      [field]: distinctValues,
    } as { [key in T]: any[] };
  }

  public async getVariantMappingsWithSource(params: {
    companyId: string;
    catalogId: string;
    externalRetailerIds: string[];
  }): Promise<{
    mappings: Record<string, { variantId: string; price: number }>;
    source: SourceIntegration;
  }> {
    const mappings =
      await this.prismaService.metaCatalogProductMapping.findMany({
        where: {
          metaCatalog: {
            metaCatalogId: params.catalogId,
          },
          metaRetailerProductId: { in: params.externalRetailerIds },
        },
        include: {
          productVariant: {
            include: {
              product: true,
            },
          },
        },
      });

    const result: Record<string, { variantId: string; price: number }> = {};
    let source: SourceIntegration | null = null;

    for (const mapping of mappings) {
      if (mapping.productVariant) {
        result[mapping.metaRetailerProductId] = {
          variantId: mapping.productVariant.id,
          price: mapping.productVariant.price ?? 0,
        };

        if (!source && mapping.productVariant.product?.source) {
          source = mapping.productVariant.product.source;
        }
      }
    }

    if (!source) {
      throw new NotFoundException(
        'Source não encontrado nos produtos do pedido',
      );
    }

    return {
      mappings: result,
      source,
    };
  }

  public async getExternalMappingsFromVariants(params: {
    companyId: string;
    catalogId: string;
    variantIds: string[];
  }): Promise<{
    mappings: Record<string, { externalProductId: string; price: number }>;
    source: SourceIntegration;
    catalog: {
      id: string;
      metaCatalogId: string;
      name: string;
      isActive: boolean;
    };
  }> {
    const mappings =
      await this.prismaService.metaCatalogProductMapping.findMany({
        where: {
          metaCatalogId: params.catalogId,
          productVariantId: { in: params.variantIds },
        },
        include: {
          metaCatalog: true,
          productVariant: {
            include: {
              product: true,
            },
          },
        },
      });

    if (mappings.length === 0) {
      throw new NotFoundException(
        'Nenhum mapeamento encontrado para os produtos informados',
      );
    }

    const result: Record<string, { externalProductId: string; price: number }> =
      {};
    let source: SourceIntegration | null = null;
    let catalog: any = null;

    for (const mapping of mappings) {
      if (mapping.productVariant) {
        result[mapping.productVariantId] = {
          externalProductId: mapping.metaRetailerProductId,
          price: mapping.productVariant.price ?? 0,
        };

        if (!source && mapping.productVariant.product?.source) {
          source = mapping.productVariant.product.source;
        }

        if (!catalog && mapping.metaCatalog) {
          catalog = {
            id: mapping.metaCatalog.id,
            metaCatalogId: mapping.metaCatalog.metaCatalogId,
            name: mapping.metaCatalog.name,
            isActive: mapping.metaCatalog.isActive,
          };
        }
      }
    }

    if (!source) {
      throw new NotFoundException(
        'Source não encontrado nos produtos informados',
      );
    }

    if (!catalog) {
      throw new NotFoundException('Catálogo não encontrado');
    }

    return {
      mappings: result,
      source,
      catalog,
    };
  }

  async handleProductEvents(payload: ProductEventPayload) {
    const productVariant = await this.prismaService.productVariant.findUnique({
      where: { id: payload?.productVariantId },
      include: {
        product: true,
      },
    });

    const company = await this.prismaService.company.findFirst({
      where: {
        id: productVariant?.product.companyId,
      },
    });

    if (!productVariant) {
      throw new NotFoundException('Product not found');
    }

    if (!company) {
      throw new NotFoundException('Company not found');
    }

    await this.processProductEvent(payload, productVariant, company);
  }

  async syncProductsWithWhatsappCatalog(companyId: string) {
    const company = await this.prismaService.company.findFirst({
      where: {
        id: companyId,
      },
    });

    if (!company) {
      throw new Error('Company not found');
    }

    const productVariants = await this.prismaService.productVariant.findMany({
      where: {
        product: {
          companyId,
        },
      },
      include: {
        product: true,
      },
    });

    for (const productVariant of productVariants.slice(10)) {
      try {
        await this.processProductEvent({} as any, productVariant, company);
      } catch (error) {
        console.error('Erro ao processar evento do produto:', error);
      }
    }
  }

  private async processProductEvent(
    payload: ProductEventPayload,
    productVariant: any,
    company: any,
  ) {
    try {
      const activeCatalog =
        await this.prismaService.metaProductCatalog.findFirst({
          where: {
            companyId: company.id,
            isActive: true,
          },
        });

      if (!activeCatalog) {
        console.log(
          `Nenhum catálogo ativo encontrado para a empresa ${company.id}`,
        );
        return;
      }

      const existingMapping =
        await this.prismaService.metaCatalogProductMapping.findFirst({
          where: {
            metaCatalogId: activeCatalog.id,
            productVariantId: productVariant.id,
          },
        });

      if (existingMapping) {
        await this.handleProductUpdate(
          existingMapping.metaProductId,
          productVariant,
          company.id,
        );
      } else {
        await this.handleProductCreate(
          productVariant,
          activeCatalog,
          company.id,
        );
      }
    } catch (error) {
      console.error('Erro ao processar evento do produto:', error);
      throw error;
    }
  }

  private buildCloudApiCreateProductData(
    productVariant: any,
  ): CloudApiCreateProductData {
    const source: SourceIntegration = productVariant.product.source;

    const price =
      Number(productVariant?.price) || Number(productVariant?.salePrice);

    if (!price) {
      throw new BadRequestException(
        `[Cloud API] Produto ${
          productVariant.id || '[sem ID]'
        } está sem preço.`,
      );
    }

    if (
      source === SourceIntegration.vtex_ecommerce &&
      productVariant.status !== 'active'
    ) {
      throw new BadRequestException(
        `[Cloud API] Produto ${
          productVariant.id || '[sem ID]'
        } não está ativo.`,
      );
    }

    return {
      name: productVariant.name || productVariant.title || 'Produto sem nome',
      description: StringUtils.stripHtmlTags(productVariant.description || ''),
      retailer_id: productVariant.id,
      image_url: productVariant.imageUrl || undefined,
      availability: 'in stock',
      condition: 'new',
      price,
      currency: 'BRL',
      brand: productVariant.product?.name || undefined,
      category: undefined,
      url: productVariant.url || undefined,
    };
  }

  private buildCloudApiUpdateProductData(
    productVariant: any,
  ): CloudApiUpdateProductData {
    const price = productVariant.salePrice || productVariant.price || 0;

    return {
      name: productVariant.name || productVariant.title || 'Produto sem nome',
      description: StringUtils.stripHtmlTags(productVariant.description || ''),
      image_url: productVariant.imageUrl || undefined,
      availability: 'in stock',
      condition: 'new',
      price,
      currency: 'BRL',
      brand: productVariant.product?.name || undefined,
      category: undefined,
      url: productVariant.url || undefined,
    };
  }

  private async handleProductCreate(
    productVariant: ProductVariant,
    catalog: MetaProductCatalog,
    companyId: string,
  ) {
    try {
      const productData = this.buildCloudApiCreateProductData(productVariant);

      const result = await this.whatsappCloudApiService.syncProducts(
        companyId,
        catalog.metaCatalogId,
        [productData],
      );

      if (result.success && result.productMappings.length > 0) {
        const productMapping = result.productMappings[0];

        if (productMapping.success && productMapping.externalId) {
          await this.prismaService.metaCatalogProductMapping.create({
            data: {
              metaCatalogId: catalog.id,
              metaProductId: productMapping.externalId,
              metaRetailerProductId:
                productMapping.externalRetailerId || productMapping.retailerId,
              productVariantId: productVariant.id,
            },
          });
        } else {
          await this.logsService.createErrorLog(
            {
              type: 'handleProductCreate',
              source: LogSource.internal,
              message: 'Falha ao criar produto',
              companyId,
            },
            productMapping.error,
          );
        }
      } else {
        await this.logsService.createErrorLog(
          {
            type: 'handleProductCreate',
            source: LogSource.internal,
            message: 'Falha ao sincronizar produtos:',
            companyId,
          },
          result.error,
        );
      }
    } catch (err: any) {
      await this.logsService.createErrorLog(
        {
          type: 'handleProductCreate',
          source: LogSource.internal,
          message: err.message,
          companyId,
        },
        err,
      );
    }
  }

  private async handleProductUpdate(
    metaProductId: string,
    productVariant: ProductVariant,
    companyId: string,
  ) {
    try {
      const updateData = this.buildCloudApiUpdateProductData(productVariant);

      // todo: add the status for sync and update in here
      await this.whatsappCloudApiService.updateProduct(
        companyId,
        metaProductId,
        updateData,
      );
    } catch (err: any) {
      await this.logsService.createErrorLog(
        {
          type: 'handleProductUpdate',
          source: LogSource.internal,
          message: 'Erro ao atualizar produto:',
          companyId,
        },
        err,
      );
    }
  }
}
