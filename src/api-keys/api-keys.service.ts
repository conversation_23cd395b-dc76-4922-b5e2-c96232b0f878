import { BadRequestException, Injectable } from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON>, Prisma } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class ApiKeysService {
  constructor(private readonly prismaService: PrismaService) {}

  async createApiKey(data: Prisma.ApiKeyUncheckedCreateInput): Promise<ApiKey> {
    return await this.prismaService.apiKey.create({
      data,
    });
  }

  async findApiKey(where: Prisma.ApiKeyWhereInput): Promise<ApiKey | null> {
    return await this.prismaService.apiKey.findFirst({
      where,
    });
  }

  async findOrCreateApiKey(
    data: Omit<Prisma.ApiKeyUncheckedCreateInput, 'key'>,
  ): Promise<ApiKey> {
    const apiKey = await this.findApiKey({
      companyId: data.companyId,
      integration: data.integration,
    });

    if (apiKey) {
      return apiKey;
    }

    return await this.createApiKey({
      ...data,
      key: this.createRandomKey(),
    });
  }

  async listApiKeys(where: Prisma.ApiKeyWhereInput): Promise<ApiKey[]> {
    return await this.prismaService.apiKey.findMany({
      where,
    });
  }

  async generateNewKey(apiKeyId: string, companyId: string) {
    const apiKey = await this.prismaService.apiKey.findUnique({
      where: { id: apiKeyId, companyId },
    });

    if (!apiKey) {
      throw new BadRequestException('API Key não encontrada');
    }

    const newApiKey = this.createRandomKey();

    const updatedApiKey = await this.prismaService.apiKey.update({
      where: { id: apiKey.id },
      data: {
        key: newApiKey,
        updatedAt: new Date(),
      },
    });

    return updatedApiKey;
  }

  public createRandomKey(): string {
    const characters =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return Array.from(
      { length: 40 },
      () => characters[Math.floor(Math.random() * characters.length)],
    ).join('');
  }
}
