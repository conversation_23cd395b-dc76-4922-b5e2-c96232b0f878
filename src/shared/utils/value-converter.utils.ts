import { DiscountType } from '@prisma/client';

export enum ReturnFormat {
  REAIS = 'reais',
  CENTAVOS = 'centavos',
}

function toCents(
  value: string | number | null,
  nonNullable?: boolean,
): number | null {
  if (value == null) return value;
  let valueToConvert = 0;
  try {
    valueToConvert = Number(value);
  } catch (error) {
    return nonNullable ? 0 : null;
  }

  const v = Number(valueToConvert.toFixed(2)) * 100;
  return Math.floor(v);
}

function toNormalValue(value: number | null): number | null {
  if (value == null) return value;

  const v = Number(value) / 100;
  return v;
}

function convertValueByType(
  value: number,
  type: DiscountType,
  returnFormat: ReturnFormat,
): number {
  const MAX_REAIS = 200;
  const MAX_PERCENTAGE = 40;

  if (type === DiscountType.fixed_in_cents) {
    if (returnFormat === ReturnFormat.CENTAVOS) {
      const maxCentavos = MAX_REAIS * 100;
      return Math.min(value, maxCentavos); // valor já está em centavos no banco, retorna limitado em centavos
    }

    // retorna em reais, limitado
    const reais = value / 100;
    return Math.min(reais, MAX_REAIS);
  }

  // tipo percentage, ignora returnFormat e retorna valor limitado
  return Math.min(value, MAX_PERCENTAGE);
}

export const ValueConverterUtils = {
  toCents,
  toNormalValue,
  convertValueByType,
};
