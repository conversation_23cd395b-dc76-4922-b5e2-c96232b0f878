const debounceByKeyAsync = (() => {
  const timeouts: Record<string, { timeout: NodeJS.Timeout; count: number }> =
    {};

  return function <T = any>(
    key: string,
    fn: (count: number) => Promise<T>,
    delay: number,
    onError?: (error: any) => void,
  ): void {
    if (timeouts[key]) {
      timeouts[key].count += 1;
      clearTimeout(timeouts[key].timeout);
    } else {
      timeouts[key] = { timeout: {} as NodeJS.Timeout, count: 1 };
    }

    timeouts[key].timeout = setTimeout(async () => {
      try {
        await fn(timeouts[key].count);
      } catch (error) {
        await onError?.(error);
      } finally {
        delete timeouts[key];
      }
    }, delay);
  };
})();

const debounceByKey = (function () {
  const timeouts: Record<string, { timeout: NodeJS.Timeout; count: number }> =
    {};

  return function (key: string, fn: (count: number) => void, delay: number) {
    if (timeouts[key]) {
      timeouts[key].count += 1;
      clearTimeout(timeouts[key].timeout);
    } else {
      timeouts[key] = { timeout: {} as NodeJS.Timeout, count: 1 };
    }

    timeouts[key].timeout = setTimeout(() => {
      fn(timeouts[key].count);
      delete timeouts[key];
    }, delay);
  };
})();

const debounceAiAgentMessageByConversationId = (() => {
  const timeouts: Record<
    string,
    { timeout: NodeJS.Timeout; count: number; finalMessage: string }
  > = {};

  return function <T = any>(
    key: string,
    message: string,
    fn: (count: number, finalMessage: string) => Promise<T>,
    delay: number,
    onError?: (error: any) => void,
  ): void {
    if (timeouts[key]) {
      timeouts[key].count += 1;
      timeouts[key].finalMessage = `${timeouts[key].finalMessage}\n${message}`;
      clearTimeout(timeouts[key].timeout);
    } else {
      timeouts[key] = {
        timeout: {} as NodeJS.Timeout,
        count: 1,
        finalMessage: message,
      };
    }

    timeouts[key].timeout = setTimeout(async () => {
      try {
        await fn(timeouts[key].count, timeouts[key].finalMessage);
      } catch (error) {
        await onError?.(error);
      } finally {
        delete timeouts[key];
      }
    }, delay);
  };
})();

export const DebounceUtils = {
  debounceByKeyAsync,
  debounceByKey,
  debounceAiAgentMessageByConversationId,
};
