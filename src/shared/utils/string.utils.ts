function camelToSnake(camelStr: string) {
  return camelStr.replace(/[A-Z]/g, (match) => '_' + match.toLowerCase());
}

function snakeToCamel(snakeStr: string) {
  return snakeStr.replace(/(_\w)/g, (match) =>
    match.toUpperCase().replace('_', ''),
  );
}

function camelToParameter(camelStr: string) {
  const formattedString = camelToSnake(camelStr).replace(/_/g, ' ');
  return `[${formattedString}]`;
}

function isNumeric(value: string) {
  return /^-?\d+(?:\.\d+)?$/.test(value);
}

function toCamelCase(key: string) {
  return key.toLowerCase().replace(/_([a-zA-Z])/g, function (match, letter) {
    return letter.toUpperCase();
  });
}

function encodeBase64(data: string): string {
  return Buffer.from(data).toString('base64');
}

function extractPrettyFileNameFromFileKey(fileKey: string): string {
  const fileName = fileKey.split('/').pop();

  if (!fileName) {
    throw new Error('File key is invalid');
  }

  try {
    let prettyFileName = fileName?.replace(
      /-[0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12}(?=\.\w+$)/,
      '',
    );
    prettyFileName = prettyFileName.replace(/-pdf/, '');

    return prettyFileName;
  } catch (error) {
    return fileName;
  }
}

function removeUuidFromText(text: string): string {
  return text.replace(
    /-[0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12}(?=\.\w+$)/,
    '',
  );
}

function stripHtmlTags(input: string): string {
  if (!input) return '';
  return input.replace(/<\/?[^>]+(>|$)/g, '').trim();
}

export const StringUtils = {
  camelToSnake,
  snakeToCamel,
  camelToParameter,
  isNumeric,
  toCamelCase,
  encodeBase64,
  extractPrettyFileNameFromFileKey,
  removeUuidFromText,
  stripHtmlTags,
};
