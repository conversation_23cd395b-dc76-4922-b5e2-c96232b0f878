import { FlowTriggersService } from './../flow-triggers/flow-triggers.service';
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import {
  BusinessSector,
  Company,
  CustomersTableHeader,
  Prisma,
} from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
import { CompanyWithIncludes } from './types/CompanyWithIncludes';
import { GupshupService } from 'src/gupshup/gupshup.service';
import PhoneQualityRating from './types/PhoneQualityRating';
import { companyBusinessHoursSchema } from './schemas/company-business-hours.schema';
import { COMPANY_DEFAULT_FIELDS, DEFAULT_BUSINESS_HOURS } from './constants';
import { BusinessHour } from './types/BusinessHour';
import { BusinessHoursPreferences } from './types/BusinesshoursPreferences';
import { CompanyBusinessHoursPreferencesDto } from './dto/update-company-business-hours.dto';
import { TIMEZONE_OFFSET } from 'src/shared/constants/timezone-offset';
import { subHours } from 'date-fns';
import { DateUtils } from 'src/utils/date.utils';
import { UpdateGupshupProfileDto } from './dto/update-gupshup-profile.dto';
import { GupshupProfile } from 'src/gupshup/types/GupshupAppBusiness';

@Injectable()
export class CompaniesService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly flowTriggersService: FlowTriggersService,
    @Inject(forwardRef(() => GupshupService))
    private readonly gupshupService: GupshupService,
  ) {}

  async getCompaniesBy(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.CompanyWhereUniqueInput;
    where?: Prisma.CompanyWhereInput;
    orderBy?: Prisma.CompanyOrderByWithRelationInput;
  }): Promise<Company[]> {
    const { skip, take, cursor, where, orderBy } = params;
    return await this.prismaService.company.findMany({
      where,
      skip,
      take,
      cursor,
      orderBy,
    });
  }

  async listCompanies(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.CompanyWhereUniqueInput;
    where?: Prisma.CompanyWhereInput;
    orderBy?: Prisma.CompanyOrderByWithRelationInput;
  }): Promise<Company[]> {
    const { skip, take, cursor, where, orderBy } = params;
    return await this.prismaService.company.findMany({
      where,
      skip,
      take,
      cursor,
      orderBy,
    });
  }

  async findCompany(data: Prisma.CompanyWhereInput): Promise<Company | null> {
    return await this.prismaService.company.findFirst({
      where: data,
    });
  }

  async companyDetails(
    companyId,
  ): Promise<Pick<
    Company,
    'firstContactMessage' | 'isAutomaticSortingActive' | 'name' | 'phoneNumber'
  > | null> {
    return await this.prismaService.company.findFirst({
      where: { id: companyId },
      select: {
        id: true,
        isShopifyActive: true,
        isVtexActive: true,
        firstContactMessage: true,
        isAutomaticSortingActive: true,
        name: true,
        phoneNumber: true,
        cnpj: true,
        monthlyEmailLimit: true,
        optoutMessage: true,
        shouldIncludeAttendantNameInMessages: true,
      },
    });
  }

  async updateCompany(params: {
    where: Prisma.CompanyWhereUniqueInput;
    data: Prisma.CompanyUncheckedUpdateInput;
  }): Promise<Company> {
    const { where, data } = params;

    return await this.prismaService.company.update({
      data,
      where,
    });
  }

  async updateManyCompanies(params: {
    where: Prisma.CompanyWhereInput;
    data: Prisma.CompanyUpdateManyMutationInput;
  }): Promise<Prisma.BatchPayload> {
    const { where, data } = params;
    return await this.prismaService.company.updateMany({
      data,
      where,
    });
  }

  async createCompany(
    data: Prisma.CompanyUncheckedCreateInput,
  ): Promise<Company> {
    return await this.prismaService.company.create({
      data,
    });
  }

  async findCompanyByWhatsappPhoneNumberId(
    whatsappPhoneNumberId: string,
  ): Promise<CompanyWithIncludes | null> {
    return await this.prismaService.company.findFirst({
      where: {
        whatsappPhoneNumberId,
      },
      include: {
        automaticSortingOptions: {
          where: {
            isActive: true,
          },
          orderBy: {
            pos: 'asc',
          },
          select: {
            conversationCategory: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
  }

  async findCompanyByGupshupAppName(
    gupshupAppName: string,
  ): Promise<CompanyWithIncludes | null> {
    return await this.prismaService.company.findFirst({
      where: {
        gupshupAppName,
      },
      include: {
        automaticSortingOptions: {
          where: {
            isActive: true,
          },
          orderBy: {
            pos: 'asc',
          },
          select: {
            conversationCategory: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
  }

  async findCompanyByPhoneNumberIds(
    phoneNumberIds: string | (string | undefined)[],
  ): Promise<CompanyWithIncludes | null> {
    const phoneNumbers = [phoneNumberIds].flat().filter(Boolean) as string[];
    return await this.prismaService.company.findFirst({
      where: {
        phoneNumberId: {
          in: phoneNumbers.filter(Boolean),
        },
      },
      include: {
        automaticSortingOptions: {
          where: {
            isActive: true,
          },
          orderBy: {
            pos: 'asc',
          },
          select: {
            conversationCategory: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
  }

  async getCompanyBusinessSectorByCompanyId(
    companyId: string,
  ): Promise<BusinessSector | null> {
    const company = await this.prismaService.company.findUnique({
      where: {
        id: companyId,
      },
      select: {
        businessSector: true,
      },
    });
    return company?.businessSector || null;
  }

  async getPhoneQualityRating(companyId: string): Promise<PhoneQualityRating> {
    const company = await this.prismaService.company.findUnique({
      where: { id: companyId },
    });
    const gupshupQualityRating = await this.gupshupService.getQualityRating(
      company?.gupshupAppId ?? '',
    );
    switch (gupshupQualityRating.phoneQuality.toUpperCase()) {
      case 'GREEN':
        return { rating: 'high' };
      case 'YELLOW':
        return { rating: 'medium' };
      case 'RED':
        return { rating: 'low' };
    }
    return { rating: 'unrated' };
    // throw new BadRequestException('Erro ao buscar qualidade do telefone');
  }

  async getCompanyBusinessHoursPreferences(
    companyId: string,
  ): Promise<BusinessHoursPreferences> {
    const data = await this.prismaService.company.findUnique({
      where: { id: companyId },
      select: {
        businessHours: true,
        afterHoursMessage: true,
        calculateTicketMetricsByBusinessHours: true,
      },
    });

    if (!data) {
      throw new BadRequestException('Empresa não encontrada');
    }

    let businessHours = DEFAULT_BUSINESS_HOURS;
    if (
      typeof data.businessHours === 'string' &&
      data.businessHours.length > 0
    ) {
      businessHours = JSON.parse(data.businessHours);
    } else if (
      Array.isArray(data.businessHours) &&
      data.businessHours.length > 0
    ) {
      businessHours = data.businessHours as unknown as BusinessHour[];
    }

    return {
      businessHours,
      afterHoursMessage: data.afterHoursMessage,
      calculateTicketMetricsByBusinessHours:
        data.calculateTicketMetricsByBusinessHours ?? false,
    };
  }

  async updateCompanyBusinessHoursPreferences(
    companyId: string,
    {
      businessHours,
      afterHoursMessage,
      calculateTicketMetricsByBusinessHours,
    }: CompanyBusinessHoursPreferencesDto,
  ) {
    businessHours.forEach((businessHour, index) => {
      const result = companyBusinessHoursSchema.safeParse(businessHour);
      if (!result.success) {
        throw new BadRequestException(
          `Objeto de indíce ${index} de horário de funcionamento inválido`,
        );
      }
    });

    businessHours = businessHours.map(
      ({ startTime, endTime, ...businessHour }) => {
        if (startTime >= endTime)
          endTime = new Date(endTime.setUTCDate(endTime.getUTCDate() + 1));
        return { ...businessHour, startTime, endTime };
      },
    );

    const company = await this.prismaService.company.findUnique({
      where: { id: companyId },
    });
    if (!company) {
      throw new BadRequestException('Empresa não encontrada');
    }
    const result = await this.prismaService.company.update({
      where: { id: companyId },
      data: {
        afterHoursMessage,
        businessHours: businessHours as unknown as Prisma.InputJsonValue,
        calculateTicketMetricsByBusinessHours,
      },
    });

    return result.businessHours;
  }

  async getBillingSettings(companyId: string) {
    const billingSettings = await this.prismaService.billingSettings.findFirst({
      where: {
        companyId,
      },
      include: {
        company: {
          select: {
            id: true,
            cnpj: true,
            razaoSocial: true,
          },
        },
      },
    });
    return billingSettings;
  }

  async toggleDisabledDefaultField(
    companyId: string,
    fieldId: CustomersTableHeader,
  ) {
    const company = await this.prismaService.company.findFirst({
      select: { disabledDefaultFields: true },
      where: { id: companyId },
    });

    if (!company) {
      throw new BadRequestException('Empresa não encontrada');
    }

    const updatedDefaultFields = this.toggleDefaultField(
      company.disabledDefaultFields,
      fieldId,
    );

    const { disabledDefaultFields } = await this.prismaService.company.update({
      where: {
        id: companyId,
      },
      data: {
        disabledDefaultFields: updatedDefaultFields,
      },
    });

    const fieldName = this.getDefaultFieldNameByFieldId(fieldId);

    return {
      companyId: companyId,
      name: fieldName,
      isActive: !disabledDefaultFields.includes(fieldId),
    };
  }

  private toggleDefaultField(
    defaultFields: CustomersTableHeader[],
    field: CustomersTableHeader,
  ) {
    const updatedDefaultFields = defaultFields?.includes(field)
      ? defaultFields.filter((f) => f !== field)
      : [...defaultFields, field];

    return updatedDefaultFields;
  }

  private getDefaultFieldNameByFieldId(fieldId: string) {
    const field = COMPANY_DEFAULT_FIELDS.filter(
      (field) => field.id === fieldId,
    );

    return field[0].header;
  }

  async isWorkingHours(companyId: string, date: Date) {
    const businessHours: BusinessHour[] = (
      await this.getCompanyBusinessHoursPreferences(companyId)
    ).businessHours;

    const todayWeekDay = subHours(date, TIMEZONE_OFFSET).getUTCDay();
    const todayBusinessHours = businessHours.find(
      (hours) => hours.weekday === todayWeekDay,
    );
    if (!todayBusinessHours) return false;

    const minutesOfDay = DateUtils.getMinutesOfDay(date);
    const startMinutes = DateUtils.getMinutesOfDay(
      new Date(todayBusinessHours.startTime),
    );
    const endMinutes = DateUtils.getMinutesOfDay(
      new Date(todayBusinessHours.endTime),
    );

    return minutesOfDay >= startMinutes && minutesOfDay <= endMinutes;
  }

  async getGupshupProfile(companyId: string): Promise<GupshupProfile> {
    const company = await this.findCompany({
      id: companyId,
    });

    if (!company || !company.gupshupAppId) {
      throw new BadRequestException(
        'Empresa não encontrada ou não possui Gupshup App ID associado',
      );
    }

    try {
      const gupshupProfile = await this.gupshupService.getBusinessProfile(
        company.gupshupAppId,
      );
      return {
        ...gupshupProfile,
        email: '',
        contactNumber: company.phoneNumberId,
      };
    } catch {
      throw new BadRequestException('Erro ao acessar perfil');
    }
  }

  async updateGupshupProfile(
    companyId: string,
    updateGupshupProfileDto: UpdateGupshupProfileDto,
  ) {
    const company = await this.findCompany({
      id: companyId,
    });

    if (!company || !company.gupshupAppId) {
      throw new BadRequestException(
        'Empresa não encontrada ou não possui Gupshup App ID associado',
      );
    }

    try {
      return await this.gupshupService.updateBusinessProfile(
        company.gupshupAppId,
        {
          description: updateGupshupProfileDto.description,
          vertical: updateGupshupProfileDto.vertical,
          website: updateGupshupProfileDto.website,
        },
      );
    } catch {
      throw new BadRequestException('Erro ao atualizar perfil');
    }
  }

  async updateGupshupProfilePhoto(
    companyId: string,
    file: Express.Multer.File,
  ) {
    const company = await this.findCompany({
      id: companyId,
    });

    if (!company || !company.gupshupAppId) {
      throw new BadRequestException(
        'Empresa não encontrada ou não possui Gupshup App ID associado',
      );
    }

    try {
      return await this.gupshupService.updateBusinessProfilePhoto(
        company.gupshupAppId,
        file,
      );
    } catch {
      throw new BadRequestException('Erro ao atualizar logo da empresa');
    }
  }
}
