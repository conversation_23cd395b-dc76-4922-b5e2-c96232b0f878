import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import * as bcrypt from 'bcrypt';
import { Prisma } from '@prisma/client';
import {
  TEST_COMPANY_ID,
  TEST_COMPANY_PHONE_NUMBER_ID,
} from './constants/test-company-data.constant';

@Injectable()
export class E2eUtilsService {
  private readonly testCompanyId = TEST_COMPANY_ID;
  private readonly companyPhoneNumberId = TEST_COMPANY_PHONE_NUMBER_ID;
  constructor(private readonly prismaService: PrismaService) {}

  private validateEnvironment() {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('This method is not allowed in production');
    }
  }

  async createUser({ isAgent = false }: { isAgent?: boolean }) {
    this.validateEnvironment();
    const pass = await this.hashPassword('*********');
    const { password, ...userWithoutPassword } =
      await this.prismaService.user.create({
        data: {
          id: `e2e-${Date.now()}`,
          name: `John Doe-${Date.now()}`,
          email: `e2e-${Date.now()}@example.com`,
          password: pass,
          companyId: this.testCompanyId,
          isAgent,
        },
      });

    return userWithoutPassword;
  }

  async createConversationCategory() {
    this.validateEnvironment();
    const conversationCategory =
      await this.prismaService.conversationCategory.create({
        data: {
          id: `e2e-conversation-category-${Date.now()}`,
          name: `E2E Conversation Category-${Date.now()}`,
          companyId: this.testCompanyId,
        },
      });

    return conversationCategory;
  }

  async createConversation({
    withOpenTicket = false,
  }: {
    withOpenTicket?: boolean;
  }) {
    this.validateEnvironment();
    const phoneNumberId = `e2e-${Date.now()}`;
    const customer = await this.prismaService.customer.create({
      data: {
        id: `e2e-customer-${Date.now()}`,
        name: `John Doe-${Date.now()}`,
        email: `e2e-customer-${Date.now()}@example.com`,
        companyId: this.testCompanyId,
        phoneNumberId,
      },
    });
    const conversation = await this.prismaService.conversation.create({
      data: {
        id: `e2e-conversation-${Date.now()}`,
        companyId: this.testCompanyId,
        recipientPhoneNumberId: phoneNumberId,
        recipientName: 'John Doe',
        customerId: customer.id,
        conversationTickets: withOpenTicket
          ? {
              create: {
                id: `e2e-conversation-ticket-${Date.now()}`,
                status: 'open',
              },
            }
          : undefined,
        messages: {
          create: {
            id: `e2e-message-${Date.now()}`,
            text: 'Hello, how can I help you today?',
            senderPhoneNumberId: phoneNumberId,
            recipientPhoneNumberId: this.companyPhoneNumberId,
            status: 'read',
            fromSystem: false,
          },
        },
      },
    });

    return conversation;
  }

  private async hashPassword(password) {
    return await bcrypt.hash(password, 10);
  }
}
