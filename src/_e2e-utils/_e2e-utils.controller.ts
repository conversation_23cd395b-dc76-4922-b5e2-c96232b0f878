import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { E2eUtilsService } from './_e2e-utils.service';
import { E2eUtilsGuard } from './guards/e2e-utils.guard';

@Controller('_e2e-utils')
@UseGuards(E2eUtilsGuard)
export class E2eUtilsController {
  constructor(private readonly e2eUtilsService: E2eUtilsService) {}

  @UseGuards(E2eUtilsGuard)
  @Post('create-user')
  async createUser(@Body() body: { isAgent: boolean }) {
    const user = await this.e2eUtilsService.createUser({
      isAgent: body.isAgent,
    });
    return user;
  }

  @UseGuards(E2eUtilsGuard)
  @Post('create-conversation-category')
  async createConversationCategory() {
    const conversationCategory =
      await this.e2eUtilsService.createConversationCategory();
    return conversationCategory;
  }

  @UseGuards(E2eUtilsGuard)
  @Post('create-conversation')
  async createConversation(@Body() body: { withOpenTicket: boolean }) {
    const conversation = await this.e2eUtilsService.createConversation({
      withOpenTicket: body.withOpenTicket,
    });
    return conversation;
  }
}
