import { CanActivate, Injectable, ForbiddenException } from '@nestjs/common';

@Injectable()
export class E2eUtilsGuard implements CanActivate {
  canActivate(): boolean {
    if (process.env.NODE_ENV !== 'development') {
      throw new ForbiddenException(
        'E2E utils are only available in development mode',
      );
      return false;
    }

    if (process.env.ENABLE_E2E_UTILS === 'true') {
      return true;
    }

    console.error('Attempt to access E2E utils but they are disabled');
    throw new ForbiddenException('E2E utils are disabled');
  }
}
