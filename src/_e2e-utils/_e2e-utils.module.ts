import { Module } from '@nestjs/common';
import { E2eUtilsService } from './_e2e-utils.service';
import { E2eUtilsController } from './_e2e-utils.controller';
import { E2eUtilsGuard } from './guards/e2e-utils.guard';
import { PrismaModule } from 'src/prisma/prisma.module';

@Module({
  providers: [E2eUtilsService, E2eUtilsGuard],
  controllers: [E2eUtilsController],
  imports: [PrismaModule],
})
export class E2eUtilsModule {}
