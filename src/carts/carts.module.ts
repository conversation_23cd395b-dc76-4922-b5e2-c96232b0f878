import { forwardRef, Module } from '@nestjs/common';
import { IntegrationsModule } from 'src/integrations/integrations.module';
import { PrismaModule } from 'src/prisma/prisma.module';
import { CartsService } from './services/carts.service';

@Module({
  exports: [CartsService],
  providers: [CartsService],
  imports: [PrismaModule, forwardRef(() => IntegrationsModule)],
})
export class CartsModule {}
