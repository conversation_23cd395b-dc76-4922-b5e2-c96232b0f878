import { IntegrationsService } from 'src/integrations/integrations.service';
import { Injectable } from '@nestjs/common';
import { Cart, CartItem, Prisma, SourceIntegration } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
import { AddCartItemParamsDTO } from '../dtos/add_cart_item_params.dto';
import { CartItemWithProductVariants } from '../types/CartItem';

@Injectable()
export class CartsService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly integrationsService: IntegrationsService,
  ) {}

  async createCart(data: Prisma.CartUncheckedCreateInput): Promise<Cart> {
    return await this.prismaService.cart.create({ data });
  }

  async finishVtexCart(cartId: string): Promise<{
    cart: string;
    cart_url: string;
  }> {
    const cart = await this.prismaService.cart.findUnique({
      where: { id: cartId },
    });

    if (!cart) {
      throw new Error('Cart not found');
    }

    const response = await this.integrationsService.finishVtexCart({
      companyId: cart.companyId,
      customerId: cart.customerId,
    });

    return response;
  }

  async findCartByCustomerId({
    customerId,
    companyId,
    status,
    source,
  }: {
    customerId: string;
    companyId: string;
    status?: string;
    source?: SourceIntegration;
  }): Promise<Cart | null> {
    const where = { customerId, companyId };
    if (status) {
      where['status'] = status;
    }
    if (source) {
      where['source'] = source;
    }
    return await this.prismaService.cart.findFirst({
      where,
    });
  }

  async findCart(data: Prisma.CartWhereInput): Promise<Cart | null> {
    return await this.prismaService.cart.findFirst({ where: data });
  }

  async updateCart(params: {
    where: Prisma.CartWhereUniqueInput;
    data: Prisma.CartUncheckedUpdateInput;
  }): Promise<Cart> {
    const { where, data } = params;
    return await this.prismaService.cart.update({
      where,
      data,
    });
  }

  async addItemsToCart(
    data: Prisma.CartItemUncheckedCreateInput[],
  ): Promise<CartItem[]> {
    const results = await this.prismaService.$transaction(
      data.map((item) =>
        this.prismaService.cartItem.upsert({
          where: {
            cartId_productVariantId: {
              cartId: item.cartId,
              productVariantId: item.productVariantId,
            },
          },
          create: item,
          update: {
            quantity: {
              increment: item.quantity,
            },
            price: item.price,
          },
        }),
      ),
    );

    return results;
  }

  async listCartItems(
    data: Prisma.CartItemWhereInput,
    select?: Prisma.CartItemSelect,
  ): Promise<CartItem[] | CartItemWithProductVariants[]> {
    return await this.prismaService.cartItem.findMany({
      where: data,
      select,
    });
  }

  // create cart if it doesn't exist
  async addItemsToCartByCustomerId(
    params: AddCartItemParamsDTO,
  ): Promise<Cart & { items: CartItem[] }> {
    let cart = await this.findCartByCustomerId({
      customerId: params.customerId,
      companyId: params.companyId,
      status: 'active',
      source: params.source,
    });

    if (!cart) {
      cart = await this.createCart({
        customerId: params.customerId,
        companyId: params.companyId,
        source: params.source,
        status: 'active',
      });
      if (!cart) {
        throw new Error("Cart not found and can't create new");
      }
    }
    const cartId = cart.id;

    const data = params.items.map((item) => ({
      ...item,
      cartId,
    }));

    return { ...cart, items: await this.addItemsToCart(data) };
  }
}
