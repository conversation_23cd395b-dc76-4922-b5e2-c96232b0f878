import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { RequestWithJwt } from 'src/shared/types/RequestWithJwt';
import { AbandonedCartsService } from '../abandoned-carts.service';
import { ApiKeyGuard } from 'src/auth/api-key-auth.guard';
import { CreateAbandonedCartDto } from './dto/create-abandoned-cart.dto';
import { LogAction } from 'src/shared/decorators/log-action.decorator';
import { LogsService } from 'src/logs/logs.service';
import { addMinutes } from 'date-fns';

@UseGuards(ApiKeyGuard)
@Controller({
  version: '1',
  path: 'abandoned-carts',
})
export class AbandonedCartsPublicController {
  constructor(
    private readonly abandonedCartsService: AbandonedCartsService,
    private readonly logsService: LogsService, // required for LogAction decorator
  ) {}

  @Post()
  @LogAction(
    'Creating abandoned cart via Revi public API',
    'revi_public_api',
    'reviPublicApiCall',
  )
  async createAbandonedCart(
    @Body() createAbandonedCartDto: CreateAbandonedCartDto,
    @Req() req: RequestWithJwt,
  ) {
    const { companyId } = req.user;
    const { customer, valueInCents, delayInMinutes, ...abandonedCart } =
      createAbandonedCartDto;

    const scheduledSendTime = delayInMinutes
      ? addMinutes(new Date(), delayInMinutes)
      : null;

    return await this.abandonedCartsService.createAbandonedCart({
      ...abandonedCart,
      customerEmail: customer.email,
      customerName: customer.name,
      customerPhoneNumber: customer.phoneNumber,
      companyId,
      value: valueInCents,
      scheduledSendTime,
    });
  }
}
