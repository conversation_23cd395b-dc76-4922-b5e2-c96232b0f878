import { SourceIntegration } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Customer } from 'src/orders/public-api/dto/place-order.dto';

export class CreateAbandonedCartDto {
  @ValidateNested()
  @Type(() => Customer)
  customer: Customer;

  @IsNotEmpty()
  @IsDateString()
  sourceCreatedAt: Date;

  @IsOptional()
  @IsString()
  sourceId?: string;

  @IsOptional()
  @IsString()
  sourceOrderId?: string;

  @IsNotEmpty()
  @IsEnum(SourceIntegration)
  source: SourceIntegration;

  @IsOptional()
  @IsNumber()
  valueInCents?: number;

  @IsOptional()
  @IsString()
  cartUrl?: string;

  @IsOptional()
  @IsNumber()
  delayInMinutes?: number;
}
