import { AbandonedCartStatus } from '@prisma/client';
import { Transform } from 'class-transformer';
import {
  IsDate,
  IsEnum,
  IsNumber,
  IsOptional,
  IsNotEmpty,
  IsString,
  IsBoolean,
} from 'class-validator';

export class ListAbandonedCartsDto {
  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  perPage?: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @IsOptional()
  page?: number;

  @IsDate()
  @Transform(({ value }) => new Date(value))
  @IsOptional()
  startDate?: Date;

  @IsDate()
  @Transform(({ value }) => new Date(value))
  @IsOptional()
  endDate?: Date;

  @IsEnum(AbandonedCartStatus, { each: true })
  @Transform(({ value }) => value.split(','))
  @IsOptional()
  status?: AbandonedCartStatus[];

  @IsString()
  @IsOptional()
  searchQuery?: string;

  @IsEnum(['asc', 'desc'])
  @Transform(({ value }) => value.toLowerCase())
  @IsOptional()
  createdAtOrder?: 'asc' | 'desc';
}
