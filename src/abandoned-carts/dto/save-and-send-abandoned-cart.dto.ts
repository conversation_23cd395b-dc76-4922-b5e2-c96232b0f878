import { SourceIntegration } from '@prisma/client';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class SaveAndSendAbandonedCartDto {
  @IsDateString()
  sourceCreatedAt: Date;

  @IsOptional()
  @IsString()
  sourceId?: string;

  @IsOptional()
  @IsString()
  sourceOrderId?: string;

  @IsString()
  customerPhoneNumber: string;

  @IsOptional()
  @IsString()
  customerEmail?: string;

  @IsNotEmpty()
  @IsString()
  customerName: string;

  @IsNotEmpty()
  @IsString()
  companyId: string;

  @IsNotEmpty()
  @IsEnum(SourceIntegration)
  source: SourceIntegration;

  @IsOptional()
  @IsNumber()
  value?: number;

  @IsOptional()
  @IsString()
  cartUrl?: string;
}
