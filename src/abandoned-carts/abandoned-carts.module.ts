import { forwardRef, Module } from '@nestjs/common';
import { AbandonedCartsService } from './abandoned-carts.service';
import { PrismaModule } from 'src/prisma/prisma.module';
import { AbandonedCartsController } from './abandoned-carts.controller';
import { MessagesModule } from 'src/messages/messages.module';
import { AuthModule } from 'src/auth/auth.module';
import { UsersModule } from 'src/users/users.module';
import { ConfigModule } from '@nestjs/config';
import { AbandonedCartsPublicController } from './public-api/abandoned-carts.public.controller';
import { ApiKeysModule } from 'src/api-keys/api-keys.module';
import { LogsModule } from 'src/logs/logs.module';
import { FlowsModule } from 'src/flows/flows.module';
import { FlowEventsModule } from 'src/flow-events/flow-events.module';
import { ConversationsModule } from 'src/conversations/conversations.module';
import { FlowTriggersModule } from 'src/flow-triggers/flow-triggers.module';
import { EmailsModule } from 'src/emails/emails.module';

@Module({
  providers: [AbandonedCartsService],
  exports: [AbandonedCartsService],
  imports: [
    PrismaModule,
    forwardRef(() => MessagesModule),
    forwardRef(() => AuthModule),
    UsersModule,
    ConfigModule,
    ApiKeysModule,
    LogsModule,
    forwardRef(() => FlowsModule),
    FlowEventsModule,
    forwardRef(() => ConversationsModule),
    ConversationsModule,
    FlowTriggersModule,
    forwardRef(() => EmailsModule),
  ],
  controllers: [AbandonedCartsController, AbandonedCartsPublicController],
})
export class AbandonedCartsModule {}
