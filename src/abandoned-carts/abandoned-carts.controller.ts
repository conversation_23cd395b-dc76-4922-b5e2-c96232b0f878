import {
  Body,
  Controller,
  Post,
  UseGuards,
  Get,
  Req,
  Query,
  DefaultValuePipe,
} from '@nestjs/common';
import { AbandonedCartsService } from './abandoned-carts.service';
import { SaveAndSendAbandonedCartDto } from './dto/save-and-send-abandoned-cart.dto';
import { ReviIntegratorAuthGuard } from 'src/auth/revi-integrator-auth.guard';
import { RequestWithJwt } from 'src/shared/types/RequestWithJwt';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { AbandonedCartStatus, Prisma } from '@prisma/client';
import { ListAbandonedCartsDto } from './dto/list-abandoned-carts.dto';

@Controller('abandoned-carts')
export class AbandonedCartsController {
  constructor(private readonly abandonedCartsService: AbandonedCartsService) {}

  @UseGuards(ReviIntegratorAuthGuard)
  @Post('send-recovery-message')
  async sendCartRecoveryMessage(
    @Body() saveAndSendAbandonedCartDto: SaveAndSendAbandonedCartDto,
  ) {
    return await this.abandonedCartsService.saveAndSendAbandonedCart(
      saveAndSendAbandonedCartDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async listAbandonedCarts(
    @Req() req: RequestWithJwt,
    @Query() listAbandonedCartsDto: ListAbandonedCartsDto,
  ) {
    const { companyId } = req.user;

    const {
      perPage = 20,
      page = 1,
      startDate,
      endDate,
      status,
      searchQuery,
      createdAtOrder,
    } = listAbandonedCartsDto;

    const whereConditions = {
      companyId,
      ...(startDate && endDate
        ? {
            sourceCreatedAt: {
              gte: new Date(startDate),
              lte: new Date(endDate),
            },
          }
        : {}),
      ...(status && status.length > 0 ? { status: { in: status } } : {}),
      ...(searchQuery
        ? {
            OR: [
              { customerName: { contains: searchQuery, mode: 'insensitive' } },
              { customerEmail: { contains: searchQuery, mode: 'insensitive' } },
              {
                customerPhoneNumber: {
                  contains: searchQuery,
                  mode: 'insensitive',
                },
              },
            ],
          }
        : {}),
    };

    const abandonedCartsData =
      await this.abandonedCartsService.listAbandonedCarts({
        where: whereConditions as Prisma.AbandonedCartWhereInput,
        skip: perPage * (page - 1),
        take: perPage,
        ...(createdAtOrder && {
          orderBy: { sourceCreatedAt: createdAtOrder },
        }),
      });

    const totalItems = await this.abandonedCartsService.countAbandonedCarts({
      where: whereConditions,
    });

    const totalPages = Math.ceil(totalItems / perPage);

    return {
      data: abandonedCartsData,
      meta: {
        page: 1,
        perPage: perPage,
        totalPages: totalPages,
        totalItems: totalItems,
      },
    };
  }
}
