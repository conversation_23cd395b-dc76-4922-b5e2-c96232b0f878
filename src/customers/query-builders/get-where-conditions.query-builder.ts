import { Prisma } from '@prisma/client';
import {
  addDays,
  eachDayOfInterval,
  startOfDay,
  subDays,
  format,
} from 'date-fns';
import { splitStringToArray } from '../utils/split-string-to-array';
import { EngagementActionsEnum } from 'src/shared/types/EngajementActionsEnum';
import { DataTypeUtils } from 'src/shared/utils/dataType.utils';
import { CustomFieldComparisonTypeEnum } from '../types/CustomFieldComparisonTypeEnum';
import { PrismaSqlUtils } from './../../shared/utils/prisma-sql.utils';
import { NumberUtils } from './../../shared/utils/number.utils';

function buildCustomFieldCondition(customField) {
  const { value, comparisonType, companyDefinedField } = customField;

  const formattedValue = DataTypeUtils.formatData(
    value,
    companyDefinedField.dataType,
  );

  return Prisma.sql`
    (c."customFields"->> ${companyDefinedField.name})::${Prisma.raw(
    PrismaSqlUtils.mapDataTypeToSQL(companyDefinedField.dataType),
  )} ${
    comparisonType === CustomFieldComparisonTypeEnum.EQUALS
      ? Prisma.sql`= ${formattedValue}`
      : comparisonType === CustomFieldComparisonTypeEnum.CONTAINS
      ? Prisma.sql`ilike ${'%' + formattedValue + '%'}`
      : comparisonType === CustomFieldComparisonTypeEnum.LESS_THAN
      ? Prisma.sql`< ${formattedValue}`
      : comparisonType === CustomFieldComparisonTypeEnum.GREATER_THAN
      ? Prisma.sql`> ${formattedValue}`
      : comparisonType === CustomFieldComparisonTypeEnum.LESS_THAN_OR_EQUAL
      ? Prisma.sql`<= ${formattedValue}`
      : comparisonType === CustomFieldComparisonTypeEnum.GREATER_THAN_OR_EQUAL
      ? Prisma.sql`>= ${formattedValue}`
      : Prisma.empty
  }
      `;
}

function getCustomerWhereConditions({
  companyId,
  searchQuery,
  wildCardSearch,
  selectedStates,
  selectedCities,
  customFields,
  hasEmail,
  hasPhoneNumberId,
  isCreatingCampaign,
  selectedCampaignChannel,
}) {
  const customFieldConditions = customFields?.map(buildCustomFieldCondition);

  return Prisma.sql`
    and c.company_id = ${companyId}
    and c.is_deleted = false
    ${
      searchQuery
        ? Prisma.sql`
        and (
          c.name ilike ${wildCardSearch}
          or c.email ilike ${searchQuery}
          or c.phone_number_id ilike ${wildCardSearch}
          or c.city ilike ${searchQuery}
          or c.state ilike ${searchQuery}
        )
    `
        : Prisma.empty
    }
    ${
      selectedStates
        ? Prisma.sql`and c.state in (${Prisma.join(
            splitStringToArray(selectedStates),
          )})`
        : Prisma.empty
    }
    ${
      selectedCities
        ? Prisma.sql`and c.city in (${Prisma.join(
            splitStringToArray(selectedCities),
          )})`
        : Prisma.empty
    }
    ${
      customFields.length > 0
        ? Prisma.sql`and (${Prisma.join(customFieldConditions, ` and `)})`
        : Prisma.empty
    }
    ${
      hasEmail || (isCreatingCampaign && selectedCampaignChannel === 'email')
        ? Prisma.sql`
        AND c.email IS NOT NULL
        AND c.email != ''
        AND c.email NOT ILIKE '%@outlook.%'
        AND c.email NOT ILIKE '%@hotmail.%'
        AND c.email NOT ILIKE '%@live.%'
        AND c.email NOT ILIKE '%@msn.%'
        AND c.email NOT ILIKE '%@ct.vtex%'
      `
        : Prisma.empty
    }
    ${
      hasPhoneNumberId ||
      (isCreatingCampaign && selectedCampaignChannel !== 'email')
        ? Prisma.sql`and c.phone_number_id is not null and c.phone_number_id != ''`
        : Prisma.empty
    }
  `;
}

function getOrderAggregateWhereConditions({
  minTotalPurchases,
  maxTotalPurchases,
  minAverageOrderValue,
  maxAverageOrderValue,
  minAverageItemValue,
  maxAverageItemValue,
  minTotalOrders,
  maxTotalOrders,
  minDaysSinceLastPurchase,
  maxDaysSinceLastPurchase,
  exactDaysSinceLastPurchase,
}) {
  return Prisma.sql`
    -- totalPurchases
    ${
      NumberUtils.isValidNumericFilter(minTotalPurchases)
        ? Prisma.sql`and oa."totalPurchases" >= ${minTotalPurchases}`
        : Prisma.empty
    }
    ${
      NumberUtils.isValidNumericFilter(maxTotalPurchases)
        ? Prisma.sql`and oa."totalPurchases" < ${maxTotalPurchases}`
        : Prisma.empty
    }
    --  averageOrderValue
    ${
      NumberUtils.isValidNumericFilter(minAverageOrderValue)
        ? Prisma.sql`and oa."averageOrderValue" >= ${minAverageOrderValue}`
        : Prisma.empty
    }
    ${
      NumberUtils.isValidNumericFilter(maxAverageOrderValue)
        ? Prisma.sql`and oa."averageOrderValue" <= ${maxAverageOrderValue}`
        : Prisma.empty
    }
    -- averageItemValue
    ${
      NumberUtils.isValidNumericFilter(minAverageItemValue)
        ? Prisma.sql`and oa."averageItemValue" >= ${minAverageItemValue}`
        : Prisma.empty
    }
    ${
      NumberUtils.isValidNumericFilter(maxAverageItemValue)
        ? Prisma.sql`and oa."averageItemValue" <= ${maxAverageItemValue}`
        : Prisma.empty
    }
    -- totalOrders
    ${
      NumberUtils.isValidNumericFilter(minTotalOrders)
        ? Prisma.sql`and oa."totalOrders" >= ${minTotalOrders}`
        : Prisma.empty
    }
    ${
      NumberUtils.isValidNumericFilter(maxTotalOrders)
        ? Prisma.sql`and oa."totalOrders" <= ${maxTotalOrders}`
        : Prisma.empty
    }
    -- minDaysSinceLastPurchase
    ${
      NumberUtils.isValidNumericFilter(minDaysSinceLastPurchase)
        ? Prisma.sql`and (current_date - oa."lastPurchaseAt" >= ${minDaysSinceLastPurchase} or oa."lastPurchaseAt" is null)`
        : Prisma.empty
    }
    -- maxDaysSinceLastPurchase
    ${
      NumberUtils.isValidNumericFilter(maxDaysSinceLastPurchase)
        ? Prisma.sql`and (current_date - oa."lastPurchaseAt" <= ${maxDaysSinceLastPurchase})`
        : Prisma.empty
    }
    -- exactDaysSinceLastPurchase
    ${
      NumberUtils.isValidNumericFilter(exactDaysSinceLastPurchase) &&
      !NumberUtils.isValidNumericFilter(minDaysSinceLastPurchase) &&
      !NumberUtils.isValidNumericFilter(maxDaysSinceLastPurchase)
        ? Prisma.sql`and (current_date - oa."lastPurchaseAt" = ${exactDaysSinceLastPurchase})`
        : Prisma.empty
    }
  `;
}

function getCustomersEngagedByTemplateExpression({
  selectedTemplateIds,
  selectedEngagementActionTypes,
}: {
  selectedTemplateIds?: string;
  selectedEngagementActionTypes?: string;
}): Prisma.Sql {
  const actions = splitStringToArray(selectedEngagementActionTypes || '');
  const templateIds = splitStringToArray(selectedTemplateIds || '');

  if (templateIds.length == 0) {
    return Prisma.sql`
      (select id as customer_id from customers where 1=2)
    `;
  }

  if (!actions || actions.length == 0) {
    return Prisma.sql`
      (select
        c.customer_id
      from
        conversations c
      join
        messages m on c.id = m.conversation_id
      where
        m.message_template_id in (${Prisma.join(templateIds)})
      )
    `;
  }

  const expressions: Prisma.Sql[] = [];

  if (actions.includes(EngagementActionsEnum.RECEIVED)) {
    expressions.push(Prisma.sql`
      (select
        c.customer_id
      from
        conversations c
      join
        messages m on c.id = m.conversation_id
      where
        m.message_template_id in (${Prisma.join(templateIds)})
        and m.status not in ('failed', 'enqueued')
      )
    `);
  }
  if (actions.includes(EngagementActionsEnum.CLICKED)) {
    expressions.push(Prisma.sql`
      (select
        c.customer_id
      from
        short_url_clicks suc
      join short_urls su on
        su.id = suc.short_url_id
      join messages m on
        m.id = su.message_id
      join message_templates mt on
        mt.id = m.message_template_id
      join conversations c on
        c.id = m.conversation_id
      where
        mt.id in (${Prisma.join(templateIds)})
      )
    `);
  }
  if (actions.includes(EngagementActionsEnum.NOT_CLICKED)) {
    expressions.push(Prisma.sql`
      (select
        c.customer_id
      from
        conversations c
      join messages m on
        c.id = m.conversation_id
      join message_templates mt on
        m.message_template_id = mt.id
      left join short_urls su on
        m.id = su.message_id
      left join short_url_clicks suc on
        su.id = suc.short_url_id
      where
        mt.id in (${Prisma.join(templateIds)})
        and suc.id is null
      )
    `);
  }
  if (actions.includes(EngagementActionsEnum.REPLIED)) {
    expressions.push(Prisma.sql`
      (select
        c.customer_id
      from
        conversations c
      join messages m on
        c.id = m.conversation_id
      where
        m.message_template_id in (${Prisma.join(templateIds)})
        and m.first_reply_id is not null
      )
    `);
  }
  if (actions.includes(EngagementActionsEnum.NOT_REPLIED)) {
    expressions.push(Prisma.sql`
      (select
        c.customer_id
      from
        conversations c
      join messages m on
        c.id = m.conversation_id
      where
        m.message_template_id in (${Prisma.join(templateIds)})
        and m.first_reply_id is null
      )
    `);
  }
  if (actions.includes(EngagementActionsEnum.READ)) {
    expressions.push(Prisma.sql`
      (select
        c.customer_id
      from
        conversations c
      join messages m on
        c.id = m.conversation_id
      where
        m.message_template_id in (${Prisma.join(templateIds)})
        and m.status = 'read'
      )
    `);
  }
  if (actions.includes(EngagementActionsEnum.NOT_READ)) {
    expressions.push(Prisma.sql`
      (select
        c.customer_id
      from
        conversations c
      join messages m on
        c.id = m.conversation_id
      where
        m.message_template_id in (${Prisma.join(templateIds)})
        and m.status = 'delivered'
      )
    `);
  }

  return Prisma.join(expressions, ' union ');
}

function getCustomersEngagedByEmailTemplateExpression({
  selectedEmailTemplateIds,
  selectedEmailEngagementActionTypes,
}: {
  selectedEmailTemplateIds?: string;
  selectedEmailEngagementActionTypes?: string;
}): Prisma.Sql {
  const actions = splitStringToArray(selectedEmailEngagementActionTypes || '');
  const emailTemplateIds = splitStringToArray(selectedEmailTemplateIds || '');

  if (emailTemplateIds.length == 0) {
    return Prisma.sql`
      (select id as customer_id from customers where 1=2)
    `;
  }
  if (!actions || actions.length == 0) {
    return Prisma.sql`
      (select
        e.customer_id
      from
        emails e
      where
        e.email_template_id in (${Prisma.join(emailTemplateIds)})
      )
    `;
  }

  const expressions: Prisma.Sql[] = [];

  if (actions.includes(EngagementActionsEnum.RECEIVED)) {
    expressions.push(Prisma.sql`
      (select
        e.customer_id
      from
        emails e
      where
        e.email_template_id in (${Prisma.join(emailTemplateIds)})
        and e.status not in ('failed')
      )
    `);
  }
  if (actions.includes(EngagementActionsEnum.CLICKED)) {
    expressions.push(Prisma.sql`
      (select
        e.customer_id
      from
        short_url_clicks suc
      join short_urls su on
        su.id = suc.short_url_id
      join emails e on
        e.id = su.email_id
      join email_templates et on
        et.id = e.email_template_id
      where
        et.id in (${Prisma.join(emailTemplateIds)})
      )
    `);
  }
  if (actions.includes(EngagementActionsEnum.NOT_CLICKED)) {
    expressions.push(Prisma.sql`
      (select
        e.customer_id
      from
        emails e
      join email_templates et on
        e.email_template_id = et.id
      left join short_urls su on
        e.id = su.email_id
      left join short_url_clicks suc on
        su.id = suc.short_url_id
      where
        et.id in (${Prisma.join(emailTemplateIds)})
        and suc.id is null
      )
    `);
  }
  if (actions.includes(EngagementActionsEnum.READ)) {
    expressions.push(Prisma.sql`
      (select
        e.customer_id
      from
        emails e
      where
        e.email_template_id in (${Prisma.join(emailTemplateIds)})
        and e.status = 'opened'
      )
    `);
  }
  if (actions.includes(EngagementActionsEnum.NOT_READ)) {
    expressions.push(Prisma.sql`
      (select
        e.customer_id
      from
        emails e
      where
        e.email_template_id in (${Prisma.join(emailTemplateIds)})
        and e.status in ('sent', 'delivered')
      )
    `);
  }

  return Prisma.join(expressions, ' union ');
}

function getCustomersThatReceivedCampaignInLastDays(
  minDaysSinceLastCampaign,
  companyId,
  selectedTemplateIds,
) {
  return Prisma.sql`
    select
      1
    from
      conversations conv
    join
      messages m on m.conversation_id = conv.id
    where
      conv.customer_id = c.id
      and m.created_at >= ${startOfDay(
        subDays(new Date(), minDaysSinceLastCampaign),
      )}
      and m.whatsapp_campaign_id is not null
      and conv.company_id = ${companyId}
      ${
        selectedTemplateIds
          ? Prisma.sql`and m.message_template_id in (${Prisma.join(
              splitStringToArray(selectedTemplateIds),
            )})`
          : Prisma.empty
      }
  `;
}

function getCustomersThatReceivedEmailCampaignInLastDays(
  minDaysSinceLastCampaign: number,
  companyId: string,
  selectedTemplateIds: string,
) {
  return Prisma.sql`
    select
      1
    from
      emails e
    where
      e.customer_id = c.id
      and e.created_at >= ${startOfDay(
        subDays(new Date(), minDaysSinceLastCampaign),
      )}
      and e.email_campaign_id is not null
      and e.company_id = ${companyId}
      ${
        selectedTemplateIds
          ? Prisma.sql`and e.email_template_id in (${Prisma.join(
              splitStringToArray(selectedTemplateIds),
            )})`
          : Prisma.empty
      }
  `;
}

function getFormattedDateRangeFromToday(
  daysFromToday: number,
  dateFormat = 'MM-dd',
): string[] | undefined {
  if (!daysFromToday && daysFromToday !== 0) return undefined;
  const today = new Date();
  const endDate = addDays(today, daysFromToday);

  const dateRange = eachDayOfInterval({ start: today, end: endDate });
  const mmddRange = dateRange.map((date) => format(date, dateFormat));

  return mmddRange;
}

function getExcludedCustomerWhereConditions({
  excludedTemplateIds,
  isCreatingCampaign,
  selectedCampaignChannel,
  isScheduledCampaignsVisible,
  isScheduledEmailCampaignsVisible,
  excludedEmailTemplateIds,
  companyId,
}: {
  excludedTemplateIds?: string;
  isCreatingCampaign: boolean;
  selectedCampaignChannel: string;
  isScheduledCampaignsVisible: boolean;
  isScheduledEmailCampaignsVisible: boolean;
  excludedEmailTemplateIds?: string;
  companyId: string;
}): Prisma.Sql {
  const expressions: Prisma.Sql[] = [];

  if (excludedTemplateIds) {
    expressions.push(
      Prisma.sql`
        AND NOT EXISTS (
          SELECT 1
          FROM conversations c2
          JOIN messages m ON c2.id = m.conversation_id
          WHERE
            m.message_template_id IN (${Prisma.join(
              splitStringToArray(excludedTemplateIds),
            )})
            AND c2.customer_id = c.id
        )
      `,
    );
  }

  if (excludedEmailTemplateIds) {
    expressions.push(
      Prisma.sql`
        AND NOT EXISTS (
          SELECT 1
          FROM emails e2
          WHERE
            e2.email_template_id IN (${Prisma.join(
              splitStringToArray(excludedEmailTemplateIds),
            )})
            AND e2.customer_id = c.id
        )
      `,
    );
  }

  if (isCreatingCampaign) {
    if (selectedCampaignChannel !== 'email' && !isScheduledCampaignsVisible) {
      expressions.push(
        Prisma.sql`
          AND NOT EXISTS (
            SELECT 1
            FROM campaign_recipients cr1
            LEFT JOIN whatsapp_campaigns wc ON wc.id = cr1.whatsapp_campaign_id
            WHERE
              wc.status IN ('scheduled', 'in_progress')
              AND wc.company_id = ${companyId}
              AND cr1.customer_id = c.id
          )
          AND NOT EXISTS (
            SELECT 1
            FROM campaign_recipients cr2
            LEFT JOIN sms_campaigns sc ON sc.id = cr2.sms_campaign_id
            WHERE
              sc.status IN ('scheduled', 'in_progress')
              AND sc.company_id = ${companyId}
              AND cr2.customer_id = c.id
          )
        `,
      );
    } else if (
      selectedCampaignChannel === 'email' &&
      !isScheduledEmailCampaignsVisible
    ) {
      expressions.push(
        Prisma.sql`
          AND NOT EXISTS (
            SELECT 1
            FROM campaign_recipients cr1
            LEFT JOIN email_campaigns ec ON ec.id = cr1.email_campaign_id
            WHERE ec.status IN ('scheduled', 'in_progress')
              AND ec.company_id = ${companyId}
              AND cr1.customer_id = c.id
          )
        `,
      );
    }
  }

  return expressions.length > 0 ? Prisma.join(expressions, ' ') : Prisma.empty;
}

export function getWhereConditions({
  selectedEngagementActionTypes,
  selectedEmailEngagementActionTypes,
  selectedTemplateIds,
  selectedEmailTemplateIds,
  companyId,
  selectedTags,
  excludedTags,
  selectedDefaultAgentIds: selectedDefaultAgentIdsStr,
  isCreatingCampaign,
  searchQuery,
  selectedStates,
  selectedCities,
  customFields,
  minTotalPurchases,
  maxTotalPurchases,
  minAverageOrderValue,
  maxAverageOrderValue,
  minAverageItemValue,
  maxAverageItemValue,
  minTotalOrders,
  maxTotalOrders,
  minDaysSinceLastPurchase,
  maxDaysSinceLastPurchase,
  exactDaysSinceLastPurchase,
  excludedTemplateIds,
  excludedEmailTemplateIds,
  minDaysSinceLastCampaign,
  minDaysSinceLastEmailCampaign,
  isScheduledCampaignsVisible,
  isScheduledEmailCampaignsVisible,
  excludedAutomationId,
  selectedCampaignChannel,
  hasEmail,
  hasPhoneNumberId,
  birthdayInNextDays,
  isUsingAnyProductFilters,
  isOrderSubscription,
}) {
  const wildCardSearch = `%${searchQuery || ''}%`;

  const selectedTagsIds = selectedTags ? splitStringToArray(selectedTags) : [];
  const excludedTagsIds = excludedTags ? splitStringToArray(excludedTags) : [];

  const selectedDefaultAgentsIds = selectedDefaultAgentIdsStr
    ? splitStringToArray(selectedDefaultAgentIdsStr)
    : [];

  const engagedCustomerIdsExpression = getCustomersEngagedByTemplateExpression({
    selectedTemplateIds,
    selectedEngagementActionTypes,
  });

  const engagedEmailCustomerIdsExpression =
    getCustomersEngagedByEmailTemplateExpression({
      selectedEmailTemplateIds: selectedEmailTemplateIds,
      selectedEmailEngagementActionTypes: selectedEmailEngagementActionTypes,
    });

  const receivedCampaignInLastDays = getCustomersThatReceivedCampaignInLastDays(
    minDaysSinceLastCampaign,
    companyId,
    selectedTemplateIds,
  );

  const receivedEmailCampaignInLastDays =
    getCustomersThatReceivedEmailCampaignInLastDays(
      minDaysSinceLastEmailCampaign,
      companyId,
      selectedEmailTemplateIds,
    );

  const birthdayMonthDayRange =
    getFormattedDateRangeFromToday(birthdayInNextDays);

  const excludedCustomerWhereConditions = getExcludedCustomerWhereConditions({
    excludedTemplateIds,
    isCreatingCampaign,
    selectedCampaignChannel,
    isScheduledCampaignsVisible,
    isScheduledEmailCampaignsVisible,
    excludedEmailTemplateIds,
    companyId,
  });

  return Prisma.sql`
    ${getCustomerWhereConditions({
      companyId,
      searchQuery,
      wildCardSearch,
      selectedStates,
      selectedCities,
      customFields,
      hasEmail,
      hasPhoneNumberId,
      isCreatingCampaign,
      selectedCampaignChannel,
    })}
    ${getOrderAggregateWhereConditions({
      minTotalPurchases,
      maxTotalPurchases,
      minAverageOrderValue,
      maxAverageOrderValue,
      minAverageItemValue,
      maxAverageItemValue,
      minTotalOrders,
      maxTotalOrders,
      minDaysSinceLastPurchase,
      maxDaysSinceLastPurchase,
      exactDaysSinceLastPurchase,
    })}
    ${excludedCustomerWhereConditions}
    ${
      selectedTemplateIds
        ? Prisma.sql`and c.id in (${engagedCustomerIdsExpression})`
        : Prisma.empty
    }
    ${
      selectedEmailTemplateIds
        ? Prisma.sql`and c.id in (${engagedEmailCustomerIdsExpression})`
        : Prisma.empty
    }
    ${
      isUsingAnyProductFilters
        ? Prisma.sql`AND c.id in (select customer_id from product_customer_ids)`
        : Prisma.empty
    }
    ${
      NumberUtils.isValidNumericFilter(minDaysSinceLastCampaign)
        ? Prisma.sql`and not exists (${receivedCampaignInLastDays})`
        : Prisma.empty
    }
    ${
      NumberUtils.isValidNumericFilter(minDaysSinceLastEmailCampaign)
        ? Prisma.sql`and not exists (${receivedEmailCampaignInLastDays})`
        : Prisma.empty
    }
    ${
      excludedAutomationId
        ? Prisma.sql`and not exists (
        select
          1
        from
          conversations conv
        join
          messages m on m.conversation_id = conv.id
        where
          conv.customer_id = c.id
          and m.automation_id = ${excludedAutomationId}
      )`
        : Prisma.empty
    }
    ${
      selectedTagsIds.length > 0
        ? Prisma.sql`and exists (select 1 from customer_tags ct where ct.customer_id = c.id and ct.tag_id in (${Prisma.join(
            selectedTagsIds,
          )}))`
        : Prisma.empty
    }
    ${
      excludedTagsIds.length > 0
        ? Prisma.sql`and not exists(select 1 from customer_tags ct2 where ct2.customer_id = c.id and ct2.tag_id in (${Prisma.join(
            excludedTagsIds,
          )}))`
        : Prisma.empty
    }
    ${
      selectedDefaultAgentsIds.length > 0
        ? Prisma.sql`and c.default_agent_id in (${Prisma.join(
            selectedDefaultAgentsIds,
          )})`
        : Prisma.empty
    }
    ${
      selectedCampaignChannel === 'email' && isCreatingCampaign
        ? Prisma.sql`and c.email_state <> 'opted_out'`
        : isCreatingCampaign
        ? Prisma.sql`and c.is_opted_out = false`
        : Prisma.empty
    }
    ${
      birthdayMonthDayRange
        ? Prisma.sql`and to_char(c.birth_date, 'MM-DD') in (${Prisma.join(
            birthdayMonthDayRange,
          )})`
        : Prisma.empty
    }
    ${
      isOrderSubscription
        ? Prisma.sql`and exists (
            select 1 from orders o where o.customer_id = c.id and o.is_subscription = true
          )`
        : Prisma.empty
    }
    `;
}
