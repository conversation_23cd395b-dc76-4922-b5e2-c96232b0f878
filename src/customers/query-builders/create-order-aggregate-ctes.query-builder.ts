import { Prisma } from '@prisma/client';
import { DateTimeUtils } from 'src/shared/utils/datetime.utils';
import { splitStringToArray } from '../utils/split-string-to-array';
import { differenceInDays } from 'date-fns';
import { NumberUtils } from 'src/shared/utils/number.utils';

export function createOrderAggregateCTEs({
  companyId,
  company,
  startOrdersCreatedAt,
  endOrdersCreatedAt,
  minDaysSinceLastPurchase,
  maxDaysSinceLastPurchase,
  effectiveExcludedStatuses,
  platformOrderSource,
  selectedCoupons,
  selectedOrdersStatuses,
  selectedOrdersSources,
  selectedOrdersSalesChannels,
  selectedOrdersStoreNames,
  isOrderSubscription,
}): Prisma.Sql {
  return Prisma.sql`
    order_aggregate as (
      select
        o.customer_id,
        (FLOOR(SUM(o.value) / 100) * 100)::float as "totalPurchases",
        (sum(o.value) / count(distinct o.id))::float as "averageOrderValue",
        (sum(o.value) / sum(o.total_items_quantity))::float as "averageItemValue",
        nullif(count(distinct o.id)::float, 0) as "totalOrders",
        max(o.source_created_at)::date as "lastPurchaseAt"
      from
        orders o
      where
        o.company_id = ${companyId}
        -- allowedStoreNames
        ${
          company && company.allowedStoreNames.length > 0
            ? Prisma.sql`and o.store_name = any(${company.allowedStoreNames})`
            : Prisma.empty
        }
        -- allowedSalesChannels
        ${
          company && company.allowedSalesChannels.length > 0
            ? Prisma.sql`and o.sales_channel = any(${company.allowedSalesChannels})`
            : Prisma.empty
        }
          -- disallowedStoreNames
          ${
            company && company.disallowedStoreNames.length > 0
              ? Prisma.sql`and COALESCE(o.store_name, '') <> ALL(ARRAY[${company.disallowedStoreNames}]::text[])`
              : Prisma.empty
          }
          -- disallowedSalesChannels
          ${
            company && company.disallowedSalesChannels.length > 0
              ? Prisma.sql`and COALESCE(o.sales_channel, '') <> ALL(ARRAY[${company.disallowedSalesChannels}]::text[])`
              : Prisma.empty
          }
        -- ordersCreatedAt
        ${
          startOrdersCreatedAt
            ? Prisma.sql`and current_date - o.source_created_at::date <= ${differenceInDays(
                new Date(),
                new Date(startOrdersCreatedAt),
              )}`
            : Prisma.empty
        }
        ${
          endOrdersCreatedAt &&
          !NumberUtils.isValidNumericFilter(minDaysSinceLastPurchase) &&
          !NumberUtils.isValidNumericFilter(maxDaysSinceLastPurchase)
            ? Prisma.sql`and current_date - o.source_created_at::date >= ${differenceInDays(
                new Date(),
                new Date(endOrdersCreatedAt),
              )}`
            : Prisma.empty
        }
        ${
          effectiveExcludedStatuses?.length > 0
            ? Prisma.sql`and (o.status is null or o.status not in (${Prisma.join(
                effectiveExcludedStatuses,
              )}))`
            : Prisma.empty
        }
        -- platformOrderSource
        ${
          platformOrderSource
            ? Prisma.sql`and o.platform_order_source = ${String(
                platformOrderSource,
              )}`
            : Prisma.empty
        }
        -- selectedCoupons
        ${
          selectedCoupons
            ? Prisma.sql`and o.coupon in (${Prisma.join(
                splitStringToArray(selectedCoupons),
              )})`
            : Prisma.empty
        }
        -- selectedOrdersStatuses
        ${
          selectedOrdersStatuses
            ? Prisma.sql`and o.status in (${Prisma.join(
                splitStringToArray(selectedOrdersStatuses),
              )})`
            : Prisma.empty
        }
        -- selectedOrdersSources
        ${
          selectedOrdersSources
            ? Prisma.sql`and o.source in (${Prisma.join(
                splitStringToArray(selectedOrdersSources).map(
                  (s) => Prisma.sql`${s}::"SourceIntegration"`,
                ),
              )})`
            : Prisma.empty
        }
        -- selectedOrdersSalesChannels
        ${
          selectedOrdersSalesChannels
            ? Prisma.sql`and o.sales_channel in (${Prisma.join(
                splitStringToArray(selectedOrdersSalesChannels),
              )})`
            : Prisma.empty
        }
        -- selectedOrdersStoreNames
        ${
          selectedOrdersStoreNames
            ? Prisma.sql`and o.store_name in (${Prisma.join(
                splitStringToArray(selectedOrdersStoreNames),
              )})`
            : Prisma.empty
        }
        -- isOrderSubscription
        ${
          isOrderSubscription === true
            ? Prisma.sql`and o.is_subscription = true`
            : isOrderSubscription === false
            ? Prisma.sql`and o.is_subscription = false`
            : Prisma.empty
        }
        and o.value > 0
        and (o.total_items_quantity > 0 or o.total_items_quantity is null)
      group by
        o.customer_id
    )`;
}
