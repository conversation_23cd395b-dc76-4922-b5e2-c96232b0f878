import { Prisma } from '@prisma/client';
import { splitStringToArray } from '../utils/split-string-to-array';
import { startOfDay, subDays } from 'date-fns';
import { NumberUtils } from './../../shared/utils/number.utils';

export function getCustomersByProductPurchasesCTEs(params: {
  companyId: string;
  selectedProductIds?: string;
  productNameContains?: string;
  minProductQuantity?: number;
  maxProductQuantity?: number;
  minDaysSinceLastProductPurchase?: number;
  maxDaysSinceLastProductPurchase?: number;
  isLastProductPurchased?: boolean;
  excludedProductIds?: string;
  selectedProductComparator?: 'OR' | 'AND';
  excludedProductComparator?: 'OR' | 'AND';
}): Prisma.Sql {
  const {
    companyId,
    selectedProductIds,
    productNameContains,
    minProductQuantity,
    maxProductQuantity,
    minDaysSinceLastProductPurchase,
    maxDaysSinceLastProductPurchase,
    isLastProductPurchased,
    excludedProductIds,
    selectedProductComparator,
    excludedProductComparator,
  } = params;

  const selectedComparator = selectedProductComparator || 'OR';
  const excludedComparator = excludedProductComparator || 'OR';

  const productNameContainsTerm = `%${productNameContains || ''}%`;
  const productOrdersAggregateTable = isLastProductPurchased
    ? Prisma.sql`last_order_by_customer`
    : Prisma.sql`orders`;

  const hasInclusionFilters =
    selectedProductIds ||
    productNameContains ||
    minProductQuantity ||
    maxProductQuantity ||
    minDaysSinceLastProductPurchase ||
    maxDaysSinceLastProductPurchase ||
    false;

  const hasExcludedProducts =
    excludedProductIds && excludedProductIds.trim() !== '';

  const excludedProductIdsArray = hasExcludedProducts
    ? splitStringToArray(excludedProductIds)
    : [];

  return Prisma.sql`
    ${
      isLastProductPurchased
        ? Prisma.sql`
          ranked_orders_by_customer AS (
            SELECT
              ROW_NUMBER() OVER(PARTITION BY customer_id ORDER BY source_created_at DESC) AS order_rank,
              o.*
            FROM
              orders o
          ),
          last_order_by_customer AS (
            SELECT
              roc.*
            FROM
              ranked_orders_by_customer roc
            WHERE
              roc.order_rank = 1
          ),
        `
        : Prisma.empty
    }
    filtered_products AS (
      SELECT
        id
      FROM
        order_products p
      WHERE
        p.company_id = ${companyId}
        AND (
          ${hasInclusionFilters ? Prisma.sql`1=2` : Prisma.sql`1=1`}
          ${
            selectedProductIds
              ? Prisma.sql`
                OR p.id IN (${Prisma.join(
                  splitStringToArray(selectedProductIds),
                )})
              `
              : Prisma.empty
          }
          ${
            productNameContains
              ? Prisma.sql`OR p.name ILIKE ${productNameContainsTerm}`
              : Prisma.empty
          }
        )
    ),
    selected_product_count AS (
      SELECT COUNT(*) as count FROM filtered_products
    ),
    ${
      selectedComparator === 'AND' && selectedProductIds
        ? Prisma.sql`
        customer_product_purchases AS (
          SELECT
            o.customer_id,
            COUNT(DISTINCT oi.product_id) as products_bought_count
          FROM
            ${productOrdersAggregateTable} o
          JOIN order_items oi ON oi.order_id = o.id
          JOIN filtered_products fp ON oi.product_id = fp.id
          WHERE
            o.company_id = ${companyId}
          GROUP BY
            o.customer_id
          HAVING
            COUNT(DISTINCT oi.product_id) = (SELECT count FROM selected_product_count)
        ),
      `
        : Prisma.empty
    }
    product_orders_aggregate AS (
      SELECT
        o.customer_id
      FROM
        ${productOrdersAggregateTable} o
      JOIN order_items oi ON oi.order_id = o.id
      WHERE
        o.company_id = ${companyId}
        ${
          selectedComparator === 'OR'
            ? Prisma.sql`AND oi.product_id IN (SELECT id FROM filtered_products)`
            : selectedComparator === 'AND' && selectedProductIds
            ? Prisma.sql`AND o.customer_id IN (SELECT customer_id FROM customer_product_purchases)`
            : Prisma.empty
        }
      GROUP BY
        o.customer_id
      HAVING
        1=1
        -- minProductQuantity
        ${
          NumberUtils.isValidNumericFilter(minProductQuantity)
            ? Prisma.sql`AND SUM(oi.quantity)::float >= ${minProductQuantity}`
            : Prisma.empty
        }
        -- maxProductQuantity
        ${
          NumberUtils.isValidNumericFilter(maxProductQuantity)
            ? Prisma.sql`AND SUM(oi.quantity)::float <= ${maxProductQuantity}`
            : Prisma.empty
        }
      -- minDaysSinceLastProductPurchase
      ${
        NumberUtils.isValidNumericFilter(minDaysSinceLastProductPurchase)
          ? Prisma.sql`and max(o.source_created_at) <= ${startOfDay(
              subDays(new Date(), minDaysSinceLastProductPurchase),
            )}`
          : Prisma.empty
      }
      -- maxDaysSinceLastProductPurchase
      ${
        NumberUtils.isValidNumericFilter(maxDaysSinceLastProductPurchase)
          ? Prisma.sql`and max(o.source_created_at) >= ${startOfDay(
              subDays(new Date(), maxDaysSinceLastProductPurchase),
            )}`
          : Prisma.empty
      }
    ),
    excluded_product_count AS (
      ${
        hasExcludedProducts
          ? Prisma.sql`
          SELECT ${excludedProductIdsArray.length} as count
        `
          : Prisma.sql`
          SELECT 0 as count
        `
      }
    ),
    customers_purchased_excluded_products AS (
      ${
        hasExcludedProducts
          ? excludedComparator === 'OR'
            ? Prisma.sql`
            SELECT DISTINCT o.customer_id
            FROM orders o
            JOIN order_items oi ON o.id = oi.order_id
            WHERE o.company_id = ${companyId}
            AND oi.product_id IN (${Prisma.join(excludedProductIdsArray)})
            `
            : Prisma.sql`
            SELECT o.customer_id
            FROM orders o
            JOIN order_items oi ON o.id = oi.order_id
            WHERE o.company_id = ${companyId}
            AND oi.product_id IN (${Prisma.join(excludedProductIdsArray)})
            GROUP BY o.customer_id
            HAVING COUNT(DISTINCT oi.product_id) = (SELECT count FROM excluded_product_count)
            `
          : Prisma.sql`
          SELECT NULL as customer_id WHERE 1=0
          `
      }
    ), product_customer_ids AS (
      SELECT
        product_orders_aggregate.customer_id
      FROM
        product_orders_aggregate
        ${
          hasExcludedProducts
            ? Prisma.sql`
            WHERE
              product_orders_aggregate.customer_id NOT IN (SELECT customer_id FROM customers_purchased_excluded_products)
          `
            : Prisma.empty
        }
    )`;
}
