import { BadRequestException, forwardRef, Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { Prisma } from '@prisma/client';
import { CompaniesService } from 'src/companies/companies.service';
import { PrismaReadService } from 'src/prisma/prisma-read.service';
import { EXCLUDED_ORDER_STATUSES } from 'src/shared/constants/excluded-order-statuses';
import { QUERY_STRING_DELIMITER } from 'src/shared/constants/query-string-delimiter';
import { CompanyDefinedFieldsService } from '../../company-defined-fields/company-defined-fields.service';
import { ListCustomersPaginatedQuery } from '../queries/list-customers-paginated.query';
import { createOrderAggregateCTEs } from '../query-builders/create-order-aggregate-ctes.query-builder';
import { getWhereConditions } from '../query-builders/get-where-conditions.query-builder';
import { createInsightsQuery } from '../query-builders/insights-query.query-builder';
import { createPaginatedCustomersQuery } from '../query-builders/paginated-customers.query-builder';
import { CustomerSql } from '../types/CustomerSql';
import { getCustomersByProductPurchasesCTEs } from '../query-builders/create-products-filter-cte';
import { NumberUtils } from './../../shared/utils/number.utils';

@QueryHandler(ListCustomersPaginatedQuery)
export class ListCustomersPaginatedHandler
  implements IQueryHandler<ListCustomersPaginatedQuery>
{
  constructor(
    private readonly prismaReadService: PrismaReadService,
    private readonly companyDefinedFieldsService: CompanyDefinedFieldsService,
    @Inject(forwardRef(() => CompaniesService))
    private readonly companiesService: CompaniesService,
  ) {}

  async execute(query: ListCustomersPaginatedQuery): Promise<any> {
    const { page, perPage, format } = query;

    const { insightsQuery, paginatedCustomersQuery } =
      await this.getCommonTableExpression({
        query,
      });

    if (format === 'table') {
      let customers: CustomerSql[] = await this.prismaReadService.$queryRaw(
        paginatedCustomersQuery,
      );
      customers = customers[0]?.id ? customers : [];
      const customersCount = customers[0]?.totalRows || 0;
      const newPerPage = perPage || customersCount;
      return {
        data: customers,
        meta: {
          page: page || 1,
          perPage: newPerPage,
          totalPages: Math.ceil(customersCount / newPerPage),
          totalItems: customersCount,
        },
      };
    } else {
      let customersInsights: any = await this.prismaReadService.$queryRaw(
        insightsQuery,
      );

      customersInsights = customersInsights[0]?.insights
        ? customersInsights[0]?.insights
        : [];

      return { data: customersInsights };
    }
  }

  private async getCommonTableExpression({
    query,
    excludedOrderStatuses = EXCLUDED_ORDER_STATUSES,
  }: {
    query: ListCustomersPaginatedQuery;
    excludedOrderStatuses?: string[];
  }): Promise<{
    insightsQuery: Prisma.Sql;
    paginatedCustomersQuery: Prisma.Sql;
  }> {
    const {
      page,
      perPage,
      sortBy,
      companyId,
      selectedTemplateIds,
      selectedEmailTemplateIds,
      selectedEngagementActionTypes,
      selectedEmailEngagementActionTypes,
      searchQuery,
      minTotalPurchases,
      maxTotalPurchases,
      minAverageOrderValue,
      maxAverageOrderValue,
      minAverageItemValue,
      maxAverageItemValue,
      minTotalOrders,
      maxTotalOrders,
      startOrdersCreatedAt,
      endOrdersCreatedAt,
      minDaysSinceLastCampaign,
      minDaysSinceLastEmailCampaign,
      selectedTags,
      minDaysSinceLastPurchase,
      maxDaysSinceLastPurchase,
      exactDaysSinceLastPurchase,
      selectedCoupons,
      selectedOrdersStatuses,
      excludedTags,
      selectedDefaultAgentIds,
      selectedProductIds,
      excludedProductIds,
      selectedProductComparator,
      excludedProductComparator,
      minProductQuantity,
      maxProductQuantity,
      minDaysSinceLastProductPurchase,
      maxDaysSinceLastProductPurchase,
      productNameContains,
      isLastProductPurchased,
      isScheduledCampaignsVisible,
      isScheduledEmailCampaignsVisible,
      isCreatingCampaign,
      excludedAutomationId,
      platformOrderSource,
      selectedStates,
      selectedCities,
      hasEmail,
      hasPhoneNumberId,
      selectedCampaignChannel,
      excludedEmailTemplateIds,
      birthdayInNextDays,
      selectedOrdersSources,
      selectedOrdersSalesChannels,
      selectedOrdersStoreNames,
      isOrderSubscription,
    } = query;

    const excludedTemplateIds = [
      query.excludedTemplateId,
      query.excludedTemplateIds,
    ]
      .filter(Boolean)
      .join(QUERY_STRING_DELIMITER);

    const company = await this.companiesService.findCompany({
      id: query.companyId,
    });

    const maxCustomFieldFilters = 4;
    const customFields: any = [];
    for (let i = 1; i <= maxCustomFieldFilters; i++) {
      const customFieldId = query[`customFieldId${i}`];
      const customFieldValue = query[`customFieldValue${i}`];
      const customFieldComparisonType = query[`customFieldComparisonType${i}`];

      if (customFieldId) {
        const companyDefinedField =
          await this.companyDefinedFieldsService.findCompanyDefinedField({
            id: customFieldId,
          });

        if (!companyDefinedField) {
          throw new BadRequestException('Custom field not found');
        }

        customFields.push({
          id: customFieldId,
          value: customFieldValue,
          comparisonType: customFieldComparisonType,
          companyDefinedField: companyDefinedField,
        });
      }
    }

    let effectiveExcludedStatuses = excludedOrderStatuses;
    if (selectedOrdersStatuses) {
      effectiveExcludedStatuses = EXCLUDED_ORDER_STATUSES.filter(
        (status) => !selectedOrdersStatuses.includes(status),
      );
    }

    const orderAggregateCTEs = createOrderAggregateCTEs({
      companyId,
      company,
      startOrdersCreatedAt,
      endOrdersCreatedAt,
      minDaysSinceLastPurchase,
      maxDaysSinceLastPurchase,
      effectiveExcludedStatuses,
      platformOrderSource,
      selectedCoupons,
      selectedOrdersStatuses,
      selectedOrdersSources,
      selectedOrdersSalesChannels,
      selectedOrdersStoreNames,
      isOrderSubscription,
    });

    const isUsingAnyProductFilters =
      selectedProductIds ||
      productNameContains ||
      NumberUtils.isValidNumericFilter(minProductQuantity) ||
      NumberUtils.isValidNumericFilter(maxProductQuantity) ||
      NumberUtils.isValidNumericFilter(minDaysSinceLastProductPurchase) ||
      NumberUtils.isValidNumericFilter(maxDaysSinceLastProductPurchase) ||
      isLastProductPurchased ||
      excludedProductIds;

    const customersByProductPurchasesCTEs = getCustomersByProductPurchasesCTEs({
      companyId,
      selectedProductIds,
      selectedProductComparator,
      excludedProductComparator,
      productNameContains,
      minProductQuantity,
      maxProductQuantity,
      minDaysSinceLastProductPurchase,
      maxDaysSinceLastProductPurchase,
      isLastProductPurchased,
      excludedProductIds,
    });

    const whereConditions = getWhereConditions({
      companyId,
      selectedTemplateIds,
      selectedEmailTemplateIds,
      selectedEngagementActionTypes,
      selectedEmailEngagementActionTypes,
      excludedTemplateIds,
      excludedEmailTemplateIds,
      minDaysSinceLastCampaign,
      minDaysSinceLastEmailCampaign,
      isScheduledCampaignsVisible,
      isScheduledEmailCampaignsVisible,
      excludedAutomationId,
      searchQuery,
      selectedStates,
      selectedCities,
      customFields,
      selectedTags,
      excludedTags,
      selectedDefaultAgentIds,
      isCreatingCampaign,
      minTotalPurchases,
      maxTotalPurchases,
      minAverageOrderValue,
      maxAverageOrderValue,
      minAverageItemValue,
      maxAverageItemValue,
      minTotalOrders,
      maxTotalOrders,
      minDaysSinceLastPurchase,
      maxDaysSinceLastPurchase,
      exactDaysSinceLastPurchase,
      selectedCampaignChannel,
      hasEmail,
      hasPhoneNumberId,
      birthdayInNextDays,
      isUsingAnyProductFilters,
      isOrderSubscription,
    });

    const randomSortKey =
      sortBy === 'random' ? await this.getRandomSortKey(companyId) : 'random';

    const paginatedCustomersQuery = createPaginatedCustomersQuery({
      orderAggregateCTEs,
      customersByProductPurchasesCTEs,
      whereConditions,
      companyId,
      sortBy,
      randomSortKey,
      perPage,
      page,
    });

    const insightsQuery = createInsightsQuery({
      orderAggregateCTEs,
      customersByProductPurchasesCTEs,
      whereConditions,
      companyId,
    });

    return {
      paginatedCustomersQuery,
      insightsQuery,
    };
  }

  private async getRandomSortKey(companyId: string) {
    const lastCreatedCampaign =
      await this.prismaReadService.whatsappCampaign.findFirst({
        where: {
          companyId,
          createdByUserId: {
            not: null,
          },
        },
        select: {
          id: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    return lastCreatedCampaign?.id || 'random';
  }
}
