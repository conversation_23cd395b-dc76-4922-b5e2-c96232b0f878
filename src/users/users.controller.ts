import {
  Controller,
  forwardRef,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { RequestWithJwt } from 'src/shared/types/RequestWithJwt';
import { CacheTagsService } from 'src/cache-tags/cache-tags.service';

@UseGuards(JwtAuthGuard)
@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    @Inject(forwardRef(() => CacheTagsService))
    private readonly cacheTagsService: CacheTagsService,
  ) {}

  @Get()
  async listUsers(@Req() req: RequestWithJwt) {
    return await this.usersService.listUsers({
      where: {
        companyId: req.user.companyId,
      },
    });
  }

  @Get('/company-agents')
  async getCompanyAgents(
    @Req() req: RequestWithJwt,
    @Query('skipAiAgents') skipAiAgents: boolean = true,
    @Query('skipInactiveUsers') skipInactiveUsers?: boolean,
  ) {
    return await this.usersService.listUsers(
      {
        where: {
          companyId: req.user.companyId,
          isAgent: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
      {
        skipAiAgents,
        skipInactiveUsers,
      },
    );
  }

  @UseGuards(JwtAuthGuard)
  @Post('announcements/:id/view')
  async updateUserLastAnnouncementViewedAt(@Req() req: RequestWithJwt) {
    return this.usersService.updateUser(req.user.sub, {
      lastAnnouncementViewedAt: new Date(),
    });
  }

  @UseGuards(JwtAuthGuard)
  @Put(':id/toggle/status')
  async toggleUserActiveStatus(@Param('id') id: string) {
    const user = await this.usersService.findUser({
      id,
    });
    return this.usersService.updateUser(id, {
      isActive: !user?.isActive,
    });
  }

  @UseGuards(JwtAuthGuard)
  @Put(':id/toggle/can-view-all-conversations')
  async toggleCanViewAllConversations(@Param('id') id: string) {
    const user = await this.usersService.findUser({
      id,
    });
    return this.usersService.updateUser(id, {
      canViewAllConversations: !user?.canViewAllConversations,
    });
  }
}
