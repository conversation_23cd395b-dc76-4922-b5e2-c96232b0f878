import { EmailsModule } from './emails/emails.module';
import { forwardRef, Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { WhatsappModule } from './whatsapp/whatsapp.module';
import { MessagesModule } from './messages/messages.module';
import { ConversationsModule } from './conversations/conversations.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { DialogflowModule } from './dialogflow/dialogflow.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ConversationCategoriesModule } from './conversation-categories/conversation-categories.module';
import { SocketsModule } from './sockets/sockets.module';
import { CompaniesModule } from './companies/companies.module';
import { ConversationTicketsModule } from './conversation-tickets/conversation-tickets.module';
import { StatisticsModule } from './statistics/statistics.module';
import { AutomaticSortingOptionsModule } from './automatic-sorting-options/automatic-sorting-options.module';
import { FilesModule } from './files/files.module';
import { WhatsappSessionsModule } from './whatsapp-sessions/whatsapp-sessions.module';
import { GupshupModule } from './gupshup/gupshup.module';
import { MessageTemplatesModule } from './message-templates/message-templates.module';
import { CustomersModule } from './customers/customers.module';
import { WhatsappCampaignsModule } from './whatsapp-campaigns/whatsapp-campaigns.module';
import { ShortUrlsModule } from './short-urls/short-urls.module';
import { LogsModule } from './logs/logs.module';
import { PrismaModule } from './prisma/prisma.module';
import { AutomaticRepliesModule } from './automatic-replies/automatic-replies.module';
import { MessageTemplateSuggestionsModule } from './message-template-suggestions/message-template-suggestions.module';
import { CompanyDefinedFieldsModule } from './company-defined-fields/company-defined-fields.module';
import { ApiKeysModule } from './api-keys/api-keys.module';
import { TagsModule } from './tags/tags.module';
import { ScheduleModule } from '@nestjs/schedule';
import { VtexModule } from './vtex/vtex.module';
import { OrdersModule } from './orders/orders.module';
import { OrderProductsModule } from './order-products/order-products.module';
import { OrderProductCategoriesModule } from './order-product-categories/order-product-categories.module';
import { CategoriesModule } from './categories/categories.module';
import { OrderItemsModule } from './order-items/order-items.module';
import { ColumnMappingConfigsModule } from './column-mapping-configs/column-mapping-configs.module';
import { ShopifyModule } from './shopify/shopify.module';
import { AutomationsModule } from './automations/automations.module';
import { FiltersModule } from './filters/filters.module';
import { TaskDispatcherModule } from './task-dispatcher/task-dispatcher.module';
import { RabbitmqModule } from './rabbitmq/rabbitmq.module';
import { AudienceRecommendationsModule } from './audience-recommendations/audience-recommendations.module';
import { BullModule } from '@nestjs/bull';
import { OpenaiModule } from './openai/openai.module';
import { PromptCompletitionsModule } from './prompt-completitions/prompt-completitions.module';
import { InitialPromptsModule } from './initial-prompts/initial-prompts.module';
import { GupshupBillingEventsModule } from './gupshup-billing-events/gupshup-billing-events.module';
import { QuickRepliesModule } from './quick-replies/quick-replies.module';
import { SmsCampaignsModule } from './sms-campaigns/sms-campaigns.module';
import { VisualECommerceModule } from './visual-ecommerce/visual-ecommerce.module';
import { FlowsModule } from './flows/flows.module';
import { FlowNodesModule } from './flow-nodes/flow-nodes.module';
import { FlowTriggersModule } from './flow-triggers/flow-triggers.module';
import { FlowNodeButtonsModule } from './flow-node-buttons/flow-node-buttons.module';
import { LojaIntegradaModule } from './loja-integrada/loja-integrada.module';
import { BlingModule } from './bling/bling.module';
import { NestjsFormDataModule } from 'nestjs-form-data';
import { MagazordModule } from './magazord/magazord.module';
import { AbandonedCartsModule } from './abandoned-carts/abandoned-carts.module';
import { ReportsModule } from './reports/reports.module';
import { CampaignExperimentsModule } from './campaign-experiments/campaign-experiments.module';
import { UnboxModule } from './unbox/unbox.module';
import { RecommendationsModule } from './recommendations/recommendations.module';
import { IntegrationsModule } from './integrations/integrations.module';
import { InvoicesModule } from './invoices/invoices.module';
import { CryptoModule } from './crypto/crypto.module';
import { CacheModule } from '@nestjs/cache-manager';
import { EmailDomainsModule } from './email-domains/email-domains.module';
import { EmailCampaignsModule } from './email-campaigns/email-campaigns.module';
import { EmailTemplatesModule } from './email-templates/email-templates.module';
import { ConversationSectorsModule } from './conversation-sectors/conversation-sectors.module';
import { BillingSettingsModule } from './billing-settings/billing-settings.module';
import { DebugModule } from './debug/debug.module';
import { RolesModule } from './roles/roles.module';
import { OrderStatusHistoryModule } from './order-status-history/order-status-history.module';
import { WhatsappCloudApiModule } from './whatsapp-cloud-api/whatsapp-cloud-api.module';
import { CashbackConfigsModule } from './cashback-configs/cashback-configs.module';
import { CouponsModule } from './coupons/coupons.module';
import { AnnouncementsModule } from './announcements/announcements.module';
import { ProductsModule } from './products/products.module';
import { WebPushModule } from './web-push/web-push.module';
import { E2eUtilsModule } from './_e2e-utils/_e2e-utils.module';
import * as redisStore from 'cache-manager-ioredis';
import { ApiTrackModule } from './api-track/api-track.module';
import { CartsModule } from './carts/carts.module';
import { ProductCatalogModule } from './product-catalog/product-catalog.module';
import { AiAgentsModule } from './ai-agents/ai-agents.module';
import { CqrsModule } from '@nestjs/cqrs';

@Module({
  imports: [
    ConfigModule.forRoot(),
    EventEmitterModule.forRoot(),
    PrismaModule,
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST,
        port: Number(process.env.REDIS_PORT),
        username: process.env.REDIS_USERNAME,
        password: process.env.REDIS_PASSWORD,
      },
      prefix: process.env.REDIS_QUEUE_PREFIX,
    }),
    NestjsFormDataModule,
    CacheModule.register({
      isGlobal: true,
      store: redisStore as any,
      host: process.env.REDIS_HOST,
      port: Number(process.env.REDIS_PORT),
      username: process.env.REDIS_USERNAME,
      password: process.env.REDIS_PASSWORD,
      max: 10000, // Maximum number of items in cache
    }),
    ApiKeysModule,
    GupshupModule,
    WhatsappModule,
    forwardRef(() => MessagesModule),
    ConversationsModule,
    AuthModule,
    UsersModule,
    DialogflowModule,
    ConversationCategoriesModule,
    SocketsModule,
    CompaniesModule,
    ConversationTicketsModule,
    StatisticsModule,
    AutomaticSortingOptionsModule,
    FilesModule,
    WhatsappSessionsModule,
    MessageTemplatesModule,
    CustomersModule,
    WhatsappCampaignsModule,
    ShortUrlsModule,
    LogsModule,
    AutomaticRepliesModule,
    MessageTemplateSuggestionsModule,
    CompanyDefinedFieldsModule,
    TagsModule,
    ScheduleModule.forRoot(),
    VtexModule,
    OrdersModule,
    OrderProductsModule,
    OrderProductCategoriesModule,
    CategoriesModule,
    OrderItemsModule,
    ColumnMappingConfigsModule,
    ShopifyModule,
    AutomationsModule,
    FiltersModule,
    TaskDispatcherModule,
    RabbitmqModule,
    AudienceRecommendationsModule,
    OpenaiModule,
    PromptCompletitionsModule,
    InitialPromptsModule,
    GupshupBillingEventsModule,
    QuickRepliesModule,
    SmsCampaignsModule,
    VisualECommerceModule,
    MagazordModule,
    BlingModule,
    FlowsModule,
    FlowNodesModule,
    FlowTriggersModule,
    FlowNodeButtonsModule,
    LojaIntegradaModule,
    ReportsModule,
    forwardRef(() => AbandonedCartsModule),
    CampaignExperimentsModule,
    UnboxModule,
    RecommendationsModule,
    IntegrationsModule,
    InvoicesModule,
    CryptoModule,
    EmailsModule,
    EmailDomainsModule,
    EmailTemplatesModule,
    EmailCampaignsModule,
    ConversationSectorsModule,
    BillingSettingsModule,
    DebugModule,
    RolesModule,
    OrderStatusHistoryModule,
    WhatsappCloudApiModule,
    CashbackConfigsModule,
    CouponsModule,
    AnnouncementsModule,
    ProductsModule,
    WebPushModule,
    E2eUtilsModule,
    ApiTrackModule,
    CartsModule,
    ProductCatalogModule,
    forwardRef(() => AiAgentsModule),
    CqrsModule.forRoot(),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
