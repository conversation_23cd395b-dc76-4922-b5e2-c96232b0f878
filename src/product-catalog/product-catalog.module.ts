import { Module } from '@nestjs/common';
import { ProductCatalogService } from './service/product-catalog.service';
import { ProductCatalogController } from './product-catalog.controller';
import { ProductsModule } from 'src/products/products.module';
import { CartsModule } from 'src/carts/carts.module';
import { PrismaModule } from 'src/prisma/prisma.module';
import { LogsModule } from 'src/logs/logs.module';

@Module({
  providers: [ProductCatalogService],
  controllers: [ProductCatalogController],
  imports: [ProductsModule, CartsModule, PrismaModule, LogsModule],
  exports: [ProductCatalogService],
})
export class ProductCatalogModule {}
