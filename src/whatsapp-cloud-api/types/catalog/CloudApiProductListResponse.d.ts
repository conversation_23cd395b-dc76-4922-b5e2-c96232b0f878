export interface CloudApiProduct {
  id: string;
  name: string;
  description?: string;
  price: string;
  currency: string;
  url?: string;
  retailer_id: string;
  image_url?: string;
  availability?: string;
  brand?: string;
  category?: string;
  condition?: string;
  sale_price?: string;
  additional_image_urls?: string[];
}

export type CloudApiProductListResponse =
  PaginatedCloudApiResponse<CloudApiProduct>;
