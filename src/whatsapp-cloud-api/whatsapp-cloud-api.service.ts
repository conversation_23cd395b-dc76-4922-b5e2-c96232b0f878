import { CompaniesService } from 'src/companies/companies.service';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import axios, { AxiosError } from 'axios';
import FormData from 'form-data';
import { CreateMessageTemplateResponse } from '../whatsapp/strategies/whatsapp-strategy.interface';
import { ProcessCloudApiEventCommand } from './types/commands/process-cloud-api-event.command';
import { CreateCloudApiTemplateData } from './types/create-message-template/CreateCloudApiTemplateData';
import { SendCloudApiMessageTemplatePayload } from './types/send-message-template';
import { SendCloudApiSessionMessagePayload } from './types/send-session-message';
import { SendSessionMessageResponse } from './types/SendSessionMessageResponse';
import { LogsService } from 'src/logs/logs.service';
import { UrlUtils } from 'src/shared/utils/url.utils';
import {
  CloudApiProduct,
  CloudApiProductListResponse,
} from './types/catalog/CloudApiProductListResponse';
import { CloudApiCreateProductCatalogData } from './types/catalog/CloudApiCreateProductCatalogData';
import {
  CloudApiProductCatalog,
  CloudApiProductCatalogListResponse,
} from './types/catalog/CloudApiProductCatalogResponse';
import { CloudApiCreateProductData } from './types/catalog/CloudApiCreateProductData';
import { CloudApiUpdateProductData } from './types/catalog/CloudApiUpdateProductData';
import { ProductMapping } from './types/catalog/ProductMapping';

interface WhatsAppErrorResponse {
  error: {
    message: string;
  };
}

@Injectable()
export class WhatsappCloudApiService {
  private readonly baseUrl = 'https://graph.facebook.com/v22.0';

  constructor(
    private readonly commandBus: CommandBus,
    @Inject(forwardRef(() => CompaniesService))
    private readonly companiesService: CompaniesService,
    private readonly logsService: LogsService,
  ) {}

  async createMessageTemplate(
    companyId: string,
    data: CreateCloudApiTemplateData,
  ): Promise<CreateMessageTemplateResponse> {
    try {
      const { whatsappAccessToken, whatsappBusinessAccountId } =
        await this.getWhatsappCredentials(companyId);
      const response = await this.getWABAInstance({
        whatsappAccessToken,
        whatsappBusinessAccountId,
      }).post('message_templates', data);

      return {
        template: {
          id: response.data.id,
        },
      };
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;
      throw new Error(
        `Failed to create WhatsApp template: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async sendMessageTemplate(
    companyId: string,
    sendMessageTemplateData: SendCloudApiMessageTemplatePayload,
  ) {
    try {
      const { whatsappAccessToken, whatsappPhoneNumberId } =
        await this.getWhatsappCredentials(companyId);
      const response = await this.getPhoneNumberInstance({
        whatsappAccessToken,
        whatsappPhoneNumberId,
      }).post('/messages', sendMessageTemplateData);

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;
      throw new Error(
        `Failed to send WhatsApp template: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async sendSessionMessage(
    companyId: string,
    sendSessionMessageData: SendCloudApiSessionMessagePayload,
  ) {
    try {
      const { whatsappAccessToken, whatsappPhoneNumberId } =
        await this.getWhatsappCredentials(companyId);
      const { data } = await this.getPhoneNumberInstance({
        whatsappAccessToken,
        whatsappPhoneNumberId,
      }).post<SendSessionMessageResponse>('/messages', sendSessionMessageData);
      return data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;
      throw new Error(
        `Failed to send WhatsApp session message: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async processEvent(eventPayload: any) {
    return await this.commandBus.execute(
      new ProcessCloudApiEventCommand(eventPayload),
    );
  }

  private getBusinessInstance({
    whatsappBusinessId,
    whatsappAccessToken,
  }: {
    whatsappBusinessId: string;
    whatsappAccessToken: string;
  }) {
    return axios.create({
      baseURL: `${this.baseUrl}/${whatsappBusinessId}`,
      headers: {
        Authorization: `Bearer ${whatsappAccessToken}`,
      },
    });
  }

  private getWABAInstance({
    whatsappBusinessAccountId,
    whatsappAccessToken,
  }: {
    whatsappBusinessAccountId: string;
    whatsappAccessToken: string;
  }) {
    return axios.create({
      baseURL: `${this.baseUrl}/${whatsappBusinessAccountId}`,
      headers: {
        Authorization: `Bearer ${whatsappAccessToken}`,
      },
    });
  }

  private getPhoneNumberInstance({
    contentType = 'application/json',
    whatsappAccessToken,
    whatsappPhoneNumberId,
  }: {
    contentType?: string;
    whatsappAccessToken: string;
    whatsappPhoneNumberId: string;
  }) {
    return axios.create({
      baseURL: `${this.baseUrl}/${whatsappPhoneNumberId}`,
      headers: {
        Authorization: `Bearer ${whatsappAccessToken}`,
        'Content-Type': contentType,
      },
    });
  }

  async uploadMedia(
    companyId: string,
    file: Express.Multer.File,
  ): Promise<string> {
    const { whatsappAccessToken, whatsappPhoneNumberId } =
      await this.getWhatsappCredentials(companyId);
    const formData = new FormData();
    formData.append('messaging_product', 'whatsapp');
    formData.append('file', file.buffer, {
      filename: file.originalname,
      contentType: file.mimetype,
    });
    formData.append('type', file.mimetype);

    const response = await this.getPhoneNumberInstance({
      contentType: 'multipart/form-data',
      whatsappAccessToken,
      whatsappPhoneNumberId,
    }).post('/media', formData);

    return response.data.id;
  }

  async getHandleId(
    companyId: string,
    file: Express.Multer.File,
  ): Promise<string> {
    try {
      const { whatsappAccessToken, whatsappPhoneNumberId } =
        await this.getWhatsappCredentials(companyId);
      // Step 1: Create upload session
      const createSessionResponse = await axios.post(
        `${this.baseUrl}/${whatsappPhoneNumberId}/uploads`,
        null,
        {
          params: {
            file_name: file.originalname,
            file_length: file.size,
            file_type: file.mimetype,
            access_token: whatsappAccessToken,
          },
        },
      );

      const uploadSessionId = createSessionResponse.data.id;

      // Step 2: Upload file at once
      const uploadResponse = await axios.post(
        `${this.baseUrl}/${uploadSessionId}`,
        file.buffer,
        {
          headers: {
            Authorization: `Bearer ${whatsappAccessToken}`,
            'Content-Type': file.mimetype,
          },
        },
      );

      return uploadResponse.data.h;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;
      throw new Error(
        `Failed to upload file to WhatsApp: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async getMedia(companyId: string, mediaId: string) {
    const { whatsappAccessToken, whatsappPhoneNumberId } =
      await this.getWhatsappCredentials(companyId);
    const { data } = await this.getPhoneNumberInstance({
      whatsappAccessToken,
      whatsappPhoneNumberId,
    }).get<{
      messaging_product: 'whatsapp';
      url: string;
      mime_type: string;
      sha256: string;
      file_size: number;
      id: string;
    }>(`/media/${mediaId}`);
    return data;
  }

  private getAppInstance({
    whatsappAccessToken,
    whatsappAppId,
  }: {
    whatsappAccessToken: string;
    whatsappAppId: string;
  }) {
    return axios.create({
      baseURL: `${this.baseUrl}/${whatsappAppId}`,
      headers: {
        Authorization: `Bearer ${whatsappAccessToken}`,
      },
    });
  }

  private async getWhatsappCredentials(companyId: string) {
    const company = await this.companiesService.findCompany({ id: companyId });
    if (!company) {
      throw new Error('Company not found');
    }

    if (
      !company.whatsappAccessToken ||
      !company.whatsappPhoneNumberId ||
      !company.whatsappBusinessAccountId ||
      !company.whatsappAppId ||
      !company.whatsappBusinessId
    ) {
      throw new Error('Whatsapp credentials not found');
    }

    return {
      whatsappAccessToken: company.whatsappAccessToken,
      whatsappPhoneNumberId: company.whatsappPhoneNumberId,
      whatsappBusinessAccountId: company.whatsappBusinessAccountId,
      whatsappAppId: company.whatsappAppId,
      whatsappBusinessId: company.whatsappBusinessId,
    };
  }

  async createCatalog(
    companyId: string,
    data: CloudApiCreateProductCatalogData,
  ): Promise<CloudApiProductCatalog> {
    try {
      const { whatsappAccessToken, whatsappBusinessAccountId } =
        await this.getWhatsappCredentials(companyId);

      const response = await this.getWABAInstance({
        whatsappAccessToken,
        whatsappBusinessAccountId,
      }).post('/owned_product_catalogs', data);

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;

      await this.logsService.createErrorLog(
        {
          type: 'whatsappCloudApiCall',
          source: 'whatsapp_cloud_api',
          message: 'Failed to create WhatsApp catalog',
          companyId,
          meta: {
            catalogData: JSON.parse(JSON.stringify(data)),
            statusCode: axiosError.response?.status,
            errorDetail:
              axiosError.response?.data?.error?.message || axiosError.message,
          },
        },
        axiosError,
      );

      throw new Error(
        `Failed to create WhatsApp catalog: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async listProductCatalogs(
    companyId: string,
    limit: number = 25,
    after?: string,
  ): Promise<CloudApiProductCatalogListResponse> {
    try {
      const { whatsappAccessToken, whatsappBusinessId } =
        await this.getWhatsappCredentials(companyId);

      const params: any = { limit };
      if (after) params.after = after;

      const response = await this.getBusinessInstance({
        whatsappAccessToken,
        whatsappBusinessId,
      }).get('/owned_product_catalogs', { params });

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;

      await this.logsService.createErrorLog(
        {
          type: 'whatsappCloudApiCall',
          source: 'whatsapp_cloud_api',
          message: 'Failed to get WhatsApp catalogs',
          companyId,
          meta: {
            limit,
            after,
            statusCode: axiosError.response?.status,
            errorDetail:
              axiosError.response?.data?.error?.message || axiosError.message,
          },
        },
        axiosError,
      );

      throw new Error(
        `Failed to get WhatsApp catalogs: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async listProductsByCatalogId(
    companyId: string,
    catalogId: string,
    limit: number = 25,
    after?: string,
  ): Promise<CloudApiProductListResponse> {
    try {
      const { whatsappAccessToken } = await this.getWhatsappCredentials(
        companyId,
      );

      const response = await axios.get(
        `${this.baseUrl}/${catalogId}/products`,
        {
          headers: {
            Authorization: `Bearer ${whatsappAccessToken}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;

      await this.logsService.createErrorLog(
        {
          type: 'whatsappCloudApiCall',
          source: 'whatsapp_cloud_api',
          message: 'Failed to get WhatsApp products',
          companyId,
          meta: {
            catalogId,
            limit,
            after,
            statusCode: axiosError.response?.status,
            errorDetail:
              axiosError.response?.data?.error?.message || axiosError.message,
          },
        },
        axiosError,
      );

      throw new Error(
        `Failed to get WhatsApp products: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async displayTypingIndicator(companyId: string, wamId: string) {
    try {
      const { whatsappAccessToken, whatsappPhoneNumberId } =
        await this.getWhatsappCredentials(companyId);

      const phoneNumberInstance = await this.getPhoneNumberInstance({
        whatsappAccessToken,
        whatsappPhoneNumberId,
      });

      const response = await phoneNumberInstance.post(`/messages`, {
        messaging_product: 'whatsapp',
        status: 'read',
        message_id: wamId,
        typing_indicator: {
          type: 'text',
        },
      });

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;

      await this.logsService.createErrorLog(
        {
          type: 'whatsappCloudApiCall',
          source: 'whatsapp_cloud_api',
          message: 'Failed to display WhatsApp typing indicator',
          companyId,
          meta: {
            wamId,
            statusCode: axiosError.response?.status,
            errorDetail:
              axiosError.response?.data?.error?.message || axiosError.message,
          },
        },
        axiosError,
      );
    }
  }

  async getProductCatalogById(
    companyId: string,
    productCatalogId: string,
  ): Promise<CloudApiProductCatalog> {
    try {
      const { whatsappAccessToken } = await this.getWhatsappCredentials(
        companyId,
      );

      const response = await axios.get(`${this.baseUrl}/${productCatalogId}`, {
        headers: {
          Authorization: `Bearer ${whatsappAccessToken}`,
        },
      });

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;

      await this.logsService.createErrorLog(
        {
          type: 'whatsappCloudApiCall',
          source: 'whatsapp_cloud_api',
          message: 'Failed to get WhatsApp catalog',
          companyId,
          meta: {
            productCatalogId,
            statusCode: axiosError.response?.status,
            errorDetail:
              axiosError.response?.data?.error?.message || axiosError.message,
          },
        },
        axiosError,
      );

      throw new Error(
        `Failed to get WhatsApp catalog: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async updateProductCatalog(
    companyId: string,
    catalogId: string,
    data: Partial<CloudApiCreateProductCatalogData>,
  ): Promise<{ success: boolean }> {
    try {
      const { whatsappAccessToken } = await this.getWhatsappCredentials(
        companyId,
      );

      const response = await axios.post(`${this.baseUrl}/${catalogId}`, data, {
        headers: {
          Authorization: `Bearer ${whatsappAccessToken}`,
        },
      });

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;

      await this.logsService.createErrorLog(
        {
          type: 'whatsappCloudApiCall',
          source: 'whatsapp_cloud_api',
          message: 'Failed to update WhatsApp catalog',
          companyId,
          meta: {
            catalogId,
            updateData: data,
            statusCode: axiosError.response?.status,
            errorDetail:
              axiosError.response?.data?.error?.message || axiosError.message,
          },
        },
        axiosError,
      );

      throw new Error(
        `Failed to update WhatsApp catalog: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async createProduct(
    companyId: string,
    catalogId: string,
    data: CloudApiCreateProductData,
  ): Promise<CloudApiProduct> {
    try {
      const { whatsappAccessToken } = await this.getWhatsappCredentials(
        companyId,
      );

      const response = await axios.post(
        `${this.baseUrl}/${catalogId}/products`,
        data,
        {
          headers: {
            Authorization: `Bearer ${whatsappAccessToken}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;

      await this.logsService.createErrorLog(
        {
          type: 'whatsappCloudApiCall',
          source: 'whatsapp_cloud_api',
          message: 'Failed to create WhatsApp product',
          companyId,
          meta: {
            catalogId,
            productData: JSON.parse(JSON.stringify(data)),
            statusCode: axiosError.response?.status,
            errorDetail:
              axiosError.response?.data?.error?.message || axiosError.message,
          },
        },
        axiosError,
      );

      throw new Error(
        `Failed to create WhatsApp product: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async listProductsByProductCatalogId(
    companyId: string,
    catalogId: string,
    limit: number = 25,
    after?: string,
  ): Promise<CloudApiProductListResponse> {
    try {
      const { whatsappAccessToken } = await this.getWhatsappCredentials(
        companyId,
      );

      const params: any = { limit };
      if (after) params.after = after;

      const response = await axios.get(
        `${this.baseUrl}/${catalogId}/products`,
        {
          params,
          headers: {
            Authorization: `Bearer ${whatsappAccessToken}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;

      await this.logsService.createErrorLog(
        {
          type: 'whatsappCloudApiCall',
          source: 'whatsapp_cloud_api',
          message: 'Failed to get WhatsApp products',
          companyId,
          meta: {
            catalogId,
            limit,
            after,
            statusCode: axiosError.response?.status,
            errorDetail:
              axiosError.response?.data?.error?.message || axiosError.message,
          },
        },
        axiosError,
      );

      throw new Error(
        `Failed to get WhatsApp products: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async fetchProduct(
    companyId: string,
    productId: string,
  ): Promise<CloudApiProduct> {
    try {
      const { whatsappAccessToken } = await this.getWhatsappCredentials(
        companyId,
      );

      const response = await axios.get(`${this.baseUrl}/${productId}`, {
        headers: {
          Authorization: `Bearer ${whatsappAccessToken}`,
        },
      });

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;

      await this.logsService.createErrorLog(
        {
          type: 'whatsappCloudApiCall',
          source: 'whatsapp_cloud_api',
          message: 'Failed to get WhatsApp product',
          companyId,
          meta: {
            productId,
            statusCode: axiosError.response?.status,
            errorDetail:
              axiosError.response?.data?.error?.message || axiosError.message,
          },
        },
        axiosError,
      );

      throw new Error(
        `Failed to get WhatsApp product: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async updateProduct(
    companyId: string,
    metaProductId: string,
    data: CloudApiUpdateProductData,
  ): Promise<{ success: boolean }> {
    try {
      const { whatsappAccessToken } = await this.getWhatsappCredentials(
        companyId,
      );

      const response = await axios.post(
        `${this.baseUrl}/${metaProductId}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${whatsappAccessToken}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<WhatsAppErrorResponse>;

      await this.logsService.createErrorLog(
        {
          type: 'whatsappCloudApiCall',
          source: 'whatsapp_cloud_api',
          message: 'Failed to update WhatsApp product',
          companyId,
          meta: {
            metaProductId,
            updateData: JSON.parse(JSON.stringify(data)),
            statusCode: axiosError.response?.status,
            errorDetail:
              axiosError.response?.data?.error?.message || axiosError.message,
          },
        },
        axiosError,
      );

      throw new Error(
        `Failed to update WhatsApp product: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }

  async syncProducts(
    companyId: string,
    catalogId: string,
    products: CloudApiCreateProductData[],
  ): Promise<{
    success: boolean;
    results: any[];
    productMappings: ProductMapping[];
    totalProducts: number;
    totalBatches: number;
    processedBatches: number;
    error?: string;
  }> {
    const BATCH_SIZE = 50;
    const DELAY_BETWEEN_BATCHES = 1000;

    try {
      const { whatsappAccessToken } = await this.getWhatsappCredentials(
        companyId,
      );

      const productBatches = this.createBatches(products, BATCH_SIZE);
      const allResults: any[] = [];
      const allProductMappings: ProductMapping[] = [];
      let totalSuccess = true;

      for (let i = 0; i < productBatches.length; i++) {
        try {
          const { batchResult, productMappings } = await this.processBatch(
            productBatches[i],
            i,
            catalogId,
            whatsappAccessToken,
          );

          allResults.push(batchResult);
          allProductMappings.push(...productMappings);

          if (productMappings.some((mapping) => !mapping.success)) {
            totalSuccess = false;
          }

          if (i < productBatches.length - 1) {
            await this.delay(DELAY_BETWEEN_BATCHES);
          }
        } catch (error) {
          totalSuccess = false;
          await this.handleBatchError(
            error,
            productBatches[i],
            i,
            companyId,
            catalogId,
          );

          const failedMappings = this.createFailedMappings(
            productBatches[i],
            error,
          );
          allProductMappings.push(...failedMappings);
          allResults.push(this.createErrorResult(error, i));
        }
      }

      return {
        success: totalSuccess,
        results: allResults,
        productMappings: allProductMappings,
        totalProducts: products.length,
        totalBatches: productBatches.length,
        processedBatches: allResults.length,
      };
    } catch (error) {
      await this.handleGeneralError(error, products, companyId, catalogId);

      return {
        success: false,
        results: [],
        productMappings: this.createFailedMappings(products, error),
        totalProducts: products.length,
        totalBatches: 0,
        processedBatches: 0,
        error: this.extractErrorMessage(error),
      };
    }
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  private async processBatch(
    productBatch: CloudApiCreateProductData[],
    batchIndex: number,
    catalogId: string,
    whatsappAccessToken: string,
  ): Promise<{ batchResult: any; productMappings: ProductMapping[] }> {
    const batch = productBatch.map((product, index) => ({
      method: 'POST',
      relative_url: `${catalogId}/products`,
      body: this.encodeBody(product),
      name: `product_${batchIndex * 50 + index}`,
    }));

    const response = await axios.post(
      `${this.baseUrl}`,
      { batch },
      {
        headers: {
          Authorization: `Bearer ${whatsappAccessToken}`,
        },
      },
    );

    const productMappings = productBatch.map((product, index) =>
      this.createProductMapping(product, response.data, index),
    );

    return {
      batchResult: response.data,
      productMappings,
    };
  }

  private createProductMapping(
    product: CloudApiCreateProductData,
    batchResponse: any,
    index: number,
  ): ProductMapping {
    const mapping: ProductMapping = {
      retailerId: product.retailer_id,
      externalId: null,
      externalRetailerId: null,
      success: false,
    };

    try {
      if (Array.isArray(batchResponse)) {
        const productResult = batchResponse[index];
        if (productResult?.code === 200) {
          const parsedBody = this.parseResponseBody(productResult.body);
          if (parsedBody?.id) {
            mapping.externalId = parsedBody.id;
            mapping.externalRetailerId = product.retailer_id;
            mapping.success = true;
          } else {
            mapping.error = 'ID do produto não encontrado na resposta';
          }
        } else {
          mapping.error = this.extractErrorFromProductResult(productResult);
        }
      } else if (batchResponse?.id) {
        mapping.externalId = batchResponse.id;
        mapping.externalRetailerId = product.retailer_id;
        mapping.success = true;
      } else {
        mapping.error =
          batchResponse?.error?.message ||
          'Resposta da API em formato não reconhecido';
      }
    } catch (error) {
      mapping.error = 'Erro ao processar resposta da API';
      throw new Error(`Erro ao extrair ID do produto: ${error}`);
    }

    return mapping;
  }

  private parseResponseBody(body: any): any {
    if (typeof body === 'string') {
      try {
        return JSON.parse(body);
      } catch (error) {
        throw new Error(`Erro ao fazer parse do body: ${error}`);
      }
    }
    return body;
  }

  private extractErrorFromProductResult(productResult: any): string {
    let errorBody = productResult?.body;

    if (typeof errorBody === 'string') {
      try {
        errorBody = JSON.parse(errorBody);
      } catch {
        // Mantém errorBody como string se o parse falhar
      }
    }

    return (
      errorBody?.error?.message ||
      `Erro HTTP ${productResult?.code}` ||
      'Erro desconhecido'
    );
  }

  private createFailedMappings(
    products: CloudApiCreateProductData[],
    error: any,
  ): ProductMapping[] {
    return products.map((product) => ({
      retailerId: product.retailer_id,
      externalId: null,
      externalRetailerId: null,
      success: false,
      error: this.extractErrorMessage(error),
    }));
  }

  private createErrorResult(error: any, batchIndex: number): any {
    return {
      success: false,
      error: this.extractErrorMessage(error),
      batchIndex: batchIndex + 1,
    };
  }

  private extractErrorMessage(error: any): string {
    const axiosError = error as AxiosError<WhatsAppErrorResponse>;
    return (
      axiosError.response?.data?.error?.message ||
      axiosError.message ||
      'Erro desconhecido'
    );
  }

  private async handleBatchError(
    error: any,
    productBatch: CloudApiCreateProductData[],
    batchIndex: number,
    companyId: string,
    catalogId: string,
  ): Promise<void> {
    console.error(`Erro no lote ${batchIndex + 1}:`, error);

    const axiosError = error as AxiosError<WhatsAppErrorResponse>;

    await this.logsService.createErrorLog(
      {
        type: 'syncProducts',
        source: 'whatsapp_cloud_api',
        message: `Failed to sync WhatsApp products - Batch ${batchIndex + 1}`,
        companyId,
        meta: {
          catalogId,
          batchIndex: batchIndex + 1,
          batchSize: productBatch.length,
          statusCode: axiosError.response?.status,
          errorDetail: this.extractErrorMessage(error),
        },
      },
      axiosError,
    );
  }

  private async handleGeneralError(
    error: any,
    products: CloudApiCreateProductData[],
    companyId: string,
    catalogId: string,
  ): Promise<void> {
    const axiosError = error as AxiosError<WhatsAppErrorResponse>;

    await this.logsService.createErrorLog(
      {
        type: 'syncProducts',
        source: 'whatsapp_cloud_api',
        message: 'Failed to sync WhatsApp products - General Error',
        companyId,
        meta: {
          catalogId,
          productsCount: products.length,
          statusCode: axiosError.response?.status,
          errorDetail: this.extractErrorMessage(error),
        },
      },
      axiosError,
    );
  }

  private encodeBody(obj: Record<string, any>): string {
    const processed = UrlUtils.serializeArrayValues(obj);
    return UrlUtils.convertObjectToQueryString(processed);
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async setDefaultCatalog(
    companyId: string,
    catalogId: string,
  ): Promise<{ success: boolean }> {
    try {
      const { whatsappAccessToken } = await this.getWhatsappCredentials(
        companyId,
      );

      const response = await axios.post(
        `${this.baseUrl}/${catalogId}`,
        {
          is_catalog_default: true,
        },
        {
          headers: {
            Authorization: `Bearer ${whatsappAccessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<any>;

      await this.logsService.createErrorLog(
        {
          type: 'whatsappCloudApiCall',
          source: 'whatsapp_cloud_api',
          message: 'Failed to set default WhatsApp catalog',
          companyId,
          meta: {
            catalogId,
            statusCode: axiosError.response?.status,
            errorDetail:
              axiosError.response?.data?.error?.message || axiosError.message,
          },
        },
        axiosError,
      );

      throw new Error(
        `Failed to set default WhatsApp catalog: ${
          axiosError.response?.data?.error?.message || axiosError.message
        }`,
      );
    }
  }
}
