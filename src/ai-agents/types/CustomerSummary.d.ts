export interface CustomerSummary {
  company: {
    id: string;
    name: string;
    valueProposition: string;
    productDescription: string;
  };
  customer: {
    id: string;
    createdAt: Date;
    name: string;
    email: string;
    phone: string;
  };
  messages: {
    text: string;
    createdAt: Date;
    sender: 'system' | 'customer';
  }[];
  latestOrder: {
    id: string;
    customerId: string;
    companyId: string;
    sourceCreatedAt: Date;
    // Adicione outros campos relevantes de "order" se necessário
  } | null;
  latestCart: {
    id: string;
    customerId: string;
    companyId: string;
    createdAt: Date;
    // Adicione outros campos relevantes de "cart" se necessário
  } | null;
}
