import { forwardRef, Module } from '@nestjs/common';
import { AiAgentsService } from './ai-agents.service';
import { FilesModule } from 'src/files/files.module';
import { MessagesModule } from 'src/messages/messages.module';
import { ConversationsModule } from 'src/conversations/conversations.module';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from 'src/prisma/prisma.module';
import { LogsModule } from 'src/logs/logs.module';
import { ConversationTicketsModule } from 'src/conversation-tickets/conversation-tickets.module';

@Module({
  exports: [AiAgentsService],
  providers: [AiAgentsService],
  imports: [
    FilesModule,
    forwardRef(() => MessagesModule),
    ConfigModule,
    PrismaModule,
    LogsModule,
    forwardRef(() => ConversationTicketsModule),
  ],
})
export class AiAgentsModule {}
