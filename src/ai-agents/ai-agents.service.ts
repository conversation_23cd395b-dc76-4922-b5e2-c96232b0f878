import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MediaType } from '@prisma/client';
import axios from 'axios';
import { subDays } from 'date-fns';
import { CompanyWithIncludes } from 'src/companies/types/CompanyWithIncludes';
import { ConversationTicketsService } from 'src/conversation-tickets/conversation-tickets.service';
import { ConversationsService } from 'src/conversations/conversations.service';
import { ConversationWithIncludes } from 'src/conversations/types/ConversationWithIncludes';
import { FilesService } from 'src/files/files.service';
import { SendProductCatalogDto } from 'src/messages/dto/send-product-catalog.dto';
import { MessagesService } from 'src/messages/messages.service';
import { SessionCatalogData } from 'src/messages/types/SessionCatalogData';
import { PrismaService } from 'src/prisma/prisma.service';
import { CustomerSummary } from './types/CustomerSummary';
import { LogsService } from 'src/logs/logs.service';

@Injectable()
export class AiAgentsService {
  constructor(
    private readonly filesService: FilesService,
    @Inject(forwardRef(() => MessagesService))
    private readonly messagesService: MessagesService,
    @Inject(forwardRef(() => ConversationTicketsService))
    private readonly conversationTicketService: ConversationTicketsService,
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService,
    private readonly logsService: LogsService,
  ) {}

  async processCustomerMessage({
    company,
    conversation,
  }: {
    company: CompanyWithIncludes;
    conversation: ConversationWithIncludes;
    qtyMessagesSent: number;
  }) {
    let agentResponse;
    try {
      const { data: aiAgentResponse } = await axios.post(
        `${this.configService.get('REVI_N8N_URL')}/webhook/agent-message`,
        {
          customerId: conversation.customerId,
          companyId: company.id,
          conversationId: conversation.id,
          qtyMessagesSent: conversation.messages.length,
        },
        {
          headers: {
            accept: 'application/json',
            'Content-Type': 'application/json',
          },
        },
      );
      agentResponse = aiAgentResponse;
      const response: {
        image_url: string[];
        messages: string[];
        catalog?: SendProductCatalogDto;
        transfer_to_human_agent: boolean;
      } =
        typeof aiAgentResponse !== 'string'
          ? aiAgentResponse
          : JSON.parse(aiAgentResponse);

      if (response.image_url.length === 1) {
        const file = await this.filesService.downloadFileFromUrl(
          response.image_url[0],
        );
        const uploadedFile = await this.filesService.uploadFile({
          companyId: company.id,
          file,
          keyPrefix: 'ai-agent-replies/media',
        });
        if (uploadedFile) {
          await this.messagesService.sendMessage({
            isAutomaticResponse: true,
            companyId: company.id,
            senderPhoneNumberId: company.phoneNumberId,
            conversationId: conversation.id,
            mediaType: uploadedFile.mediaType || MediaType.image,
            fileKey: uploadedFile.key,
            text: response.messages[0],
          });
        }
      }

      for (const message of response.messages) {
        await this.messagesService.sendMessage({
          isAutomaticResponse: true,
          companyId: company.id,
          senderPhoneNumberId: company.phoneNumberId,
          conversationId: conversation.id,
          text: message,
        });
      }
      if (response.catalog) {
        response.catalog.sections = response.catalog.sections?.map(
          (section) => ({
            ...section,
            title: 'Destaques',
          }),
        );
        response.catalog.footerText = 'Veja o que separei pra você';
        await this.messagesService.sendProductCatalog(response.catalog);
      }
      if (response.transfer_to_human_agent) {
        const ticket =
          await this.conversationTicketService.findOrCreateConversationTicketByConversation(
            conversation.id,
          );
        await this.conversationTicketService.updateConversationTicket({
          where: {
            id: ticket.id,
          },
          data: {
            agentId: null,
          },
          notifyUser: false,
          companyId: company.id,
        });
      }
      return true;
    } catch (error: any) {
      await this.logsService.createErrorLog(
        {
          companyId: company.id,
          message: 'AiAgentService.processCustomerMessage',
          meta: {
            conversationId: conversation.id,
            companyId: company.id,
            agentId: 'orlando',
            error: error.message,
            customerId: conversation.customerId,
            customerName: conversation.customer.name,
            customerPhone: conversation.customer.phoneNumberId,
            agentResponse,
          },
        },
        error,
      );
      throw new Error(error.message);
    }
  }

  async processCustomerMessage2({
    company,
    message,
    conversation,
  }: {
    company: CompanyWithIncludes;
    message: string;
    conversation: ConversationWithIncludes;
  }) {
    let agentResponse;
    try {
      const messages = await this.prismaService.message.findMany({
        where: {
          conversationId: conversation.id,
          createdAt: {
            gte: subDays(new Date(), 1),
          },
        },
        select: {
          id: true,
          text: true,
          createdAt: true,
          fromSystem: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 6,
      });
      const latestOrder = await this.prismaService.order.findFirst({
        where: {
          customerId: conversation.customerId,
          companyId: company.id,
          sourceCreatedAt: {
            gte: subDays(new Date(), 7),
          },
        },
        orderBy: {
          sourceCreatedAt: 'desc',
        },
      });
      const latestCart = await this.prismaService.cart.findFirst({
        where: {
          customerId: conversation.customerId,
          companyId: company.id,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      const customerSummary: CustomerSummary = {
        company: {
          id: company.id,
          name: company.name,
          valueProposition: company.valueProposition || '',
          productDescription: company.productDescription || '',
        },
        customer: {
          id: conversation.customerId,
          createdAt: conversation.customer.createdAt,
          name: conversation.customer.name,
          email: conversation.customer.email || '',
          phone: conversation.customer.phoneNumberId || '',
        },
        messages: messages.map((message) => ({
          text: message.text,
          createdAt: message.createdAt,
          sender: message.fromSystem ? 'system' : 'customer',
        })),
        latestOrder,
        latestCart,
      };
      const chatInfo = await this.prismaService.aIAgentChatInfo.findFirst({
        where: {
          customerId: conversation.customerId,
        },
      });
      if (!chatInfo) {
        await this.prismaService.aIAgentChatInfo.create({
          data: {
            customerId: conversation.customerId,
            summary: '',
            agentName: 'ReviApp2',
          },
        });
      }

      const { data: aiAgentResponse } = await axios.post<{
        image_url: string[];
        messages: string[];
        catalog?: SendProductCatalogDto;
        postMessages?: string[];
      }>(
        `${this.configService.get('REVI_N8N_URL')}/webhook/agent-message2`,
        {
          message: message,
          customerId: conversation.customerId,
          companyId: company.id,
          conversationId: conversation.id,
          customerSummary,
        },
        {
          headers: {
            accept: 'application/json',
            'Content-Type': 'application/json',
          },
        },
      );
      agentResponse = aiAgentResponse;
      // console.dir({ aiAgentResponse }, { depth: null, colors: true });
      const response: {
        image_url: string[];
        messages: string[];
        catalog: {
          productVariants: {
            json: {
              id: string;
              name: string;
            };
          }[];
        };
        postMessages?: string[];
      } =
        typeof aiAgentResponse !== 'string'
          ? aiAgentResponse
          : JSON.parse(aiAgentResponse);

      if (response.image_url?.length === 1) {
        const file = await this.filesService.downloadFileFromUrl(
          response.image_url[0],
        );
        const uploadedFile = await this.filesService.uploadFile({
          companyId: company.id,
          file,
          keyPrefix: 'ai-agent-replies/media',
        });
        if (uploadedFile) {
          await this.messagesService.sendMessage({
            isAutomaticResponse: true,
            companyId: company.id,
            senderPhoneNumberId: company.phoneNumberId,
            conversationId: conversation.id,
            mediaType: uploadedFile.mediaType || MediaType.image,
            fileKey: uploadedFile.key,
            text: response.messages[0],
          });
        }
      }

      for (const message of response.messages) {
        await this.messagesService.sendMessage({
          isAutomaticResponse: true,
          companyId: company.id,
          senderPhoneNumberId: company.phoneNumberId,
          conversationId: conversation.id,
          text: this.removeWrongLineBreaks(message),
        });
        await new Promise((resolve) => setTimeout(resolve, 3000));
      }
      if (response.catalog && response.catalog.productVariants) {
        const catalog = await this.prismaService.metaProductCatalog.findFirst({
          where: {
            companyId: company.id,
          },
        });
        if (!catalog) {
          return false;
        }

        if (response.catalog.productVariants.length === 0) {
          return false;
        }

        let sessionCatalogData: SessionCatalogData;
        if (response.catalog.productVariants.length == 1) {
          sessionCatalogData = {
            type: 'single_product',
            productRetailerId: response.catalog.productVariants[0].json.id,
            catalogId: catalog.metaCatalogId,
            bodyText: response.catalog.productVariants[0].json.name || '',
            footerText: 'Veja o que separei pra você',
          };
        } else {
          sessionCatalogData = {
            type: 'multi_product',
            headerText: 'Produtos',
            catalogId: catalog.metaCatalogId,
            bodyText: 'Segue uma lista de produtos que encontrei para você',
            footerText: 'Veja o que separei pra você',
            productSections: [
              {
                title: 'Produtos',
                productItems: response.catalog.productVariants.map(
                  (productVariant) => ({
                    productRetailerId: productVariant.json.id,
                  }),
                ),
              },
            ],
          };
        }

        await this.messagesService.sendProductCatalogMessage({
          companyId: company.id,
          senderPhoneNumberId: company.phoneNumberId,
          text: 'Segue uma lista de produtos que encontrei para você',
          conversationId: conversation.id,
          catalogData: sessionCatalogData,
          isAutomaticResponse: true,
        });
      }

      for (const message of response.postMessages || []) {
        // wait 1 second
        await new Promise((resolve) => setTimeout(resolve, 3000));
        await this.messagesService.sendMessage({
          isAutomaticResponse: true,
          companyId: company.id,
          senderPhoneNumberId: company.phoneNumberId,
          conversationId: conversation.id,
          text: this.removeWrongLineBreaks(message),
        });
      }
      return true;
    } catch (error: any) {
      await this.logsService.createErrorLog(
        {
          companyId: company.id,
          message: 'AiAgentService.processCustomerMessage2',
          meta: {
            conversationId: conversation.id,
            companyId: company.id,
            agentId: 'marcelo',
            error: error.message,
            message,
            customerId: conversation.customerId,
            customerName: conversation.customer.name,
            customerPhone: conversation.customer.phoneNumberId,
            agentResponse,
          },
        },
        error,
      );
      throw new Error(error.message);
    }
  }

  private removeWrongLineBreaks(text: string) {
    return text.replace(/\\n/g, '').replace(/\\/g, '');
  }
}
